-- 人员信息表
CREATE TABLE IF NOT EXISTS ioc_cnccj_personnel_info (
  id SERIAL PRIMARY KEY, -- 自增主键
  personnel_count INT, -- 人员总数
  total_worker_count INT, -- 普工人数
  total_worker_rate DECIMAL(5, 2), --普工占比
  total_clerical_count INT, -- 职员人数
  total_clerical_rate DECIMAL(5, 2),--职员占比
  record_date DATE, -- 日期
  updated_by VARCHAR(20), -- 更新人
  updated_at TIMESTAMP, -- 更新时间，使用 TIMESTAMP 更精确
  reserved_field1 VARCHAR(255), -- 备用字段1
  reserved_field2 VARCHAR(255), -- 备用字段2
  reserved_field3 VARCHAR(255) -- 备用字段3
);

COMMENT ON TABLE ioc_cnccj_personnel_info IS '人员信息表'; 
COMMENT ON COLUMN ioc_cnccj_personnel_info.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_personnel_info.personnel_count IS '人员总数';
COMMENT ON COLUMN ioc_cnccj_personnel_info.total_worker_count IS '普工人数';
COMMENT ON COLUMN ioc_cnccj_personnel_info.total_worker_rate IS '普工占比';
COMMENT ON COLUMN ioc_cnccj_personnel_info.total_clerical_count IS '职员人数';
COMMENT ON COLUMN ioc_cnccj_personnel_info.total_clerical_rate IS '职员占比';
COMMENT ON COLUMN ioc_cnccj_personnel_info.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_personnel_info.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_personnel_info.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_personnel_info.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_personnel_info.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_personnel_info.reserved_field3 IS '备用字段3';

-- 一天出勤率表
CREATE TABLE IF NOT EXISTS ioc_cnccj_attendance_rate (
  id SERIAL PRIMARY KEY, -- 自增主键
  clerk_attendance_rate DECIMAL(5, 2), -- 职员出勤率
  worker_attendance_rate DECIMAL(5, 2), -- 普工出勤率
  record_date DATE, -- 日期
  updated_by VARCHAR(20), -- 更新人
  updated_at TIMESTAMP, -- 更新时间
  reserved_field1 VARCHAR(255), -- 备用字段1
  reserved_field2 VARCHAR(255), -- 备用字段2
  reserved_field3 VARCHAR(255) -- 备用字段3
);
COMMENT ON TABLE ioc_cnccj_attendance_rate IS 'CNC车间出勤率表';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.clerk_attendance_rate IS '职员出勤率';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.worker_attendance_rate IS '普工出勤率';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_attendance_rate.reserved_field3 IS '备用字段3';

--设备信息表
CREATE TABLE IF NOT EXISTS ioc_cnccj_equipment_info (
  id SERIAL PRIMARY KEY, -- 自增主键
  operating_rate DECIMAL(5, 2), -- 开机率
  total_count INT, -- 总数
  running_count INT, -- 运行数量
  dee_rate DECIMAL(5, 2), -- DEE率
  record_date DATE, -- 日期
  updated_by VARCHAR(20), -- 更新人
  updated_at TIMESTAMP, -- 更新时间
  reserved_field1 VARCHAR(255), -- 备用字段1
  reserved_field2 VARCHAR(255), -- 备用字段2
  reserved_field3 VARCHAR(255) -- 备用字段3
);

COMMENT ON TABLE ioc_cnccj_equipment_info IS '设备信息表';
COMMENT ON COLUMN ioc_cnccj_equipment_info.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_equipment_info.operating_rate IS '开机率';
COMMENT ON COLUMN ioc_cnccj_equipment_info.total_count IS '总数';
COMMENT ON COLUMN ioc_cnccj_equipment_info.running_count IS '运行数量';
COMMENT ON COLUMN ioc_cnccj_equipment_info.dee_rate IS 'DEE率';
COMMENT ON COLUMN ioc_cnccj_equipment_info.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_equipment_info.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_equipment_info.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_equipment_info.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_equipment_info.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_equipment_info.reserved_field3 IS '备用字段3';

--车间设备状况表
CREATE TABLE IF NOT EXISTS ioc_cnccj_workshop_status (
  id SERIAL PRIMARY KEY, -- 自增主键
  workshop_name VARCHAR(50), -- 车间名称
  operating_rate DECIMAL(5, 2), -- 开机率
  total_equipment_count INT, -- 设备总数
  operating_equipment_count INT, -- 总开机数
  record_date DATE, -- 日期
  updated_by VARCHAR(20), -- 更新人
  updated_at TIMESTAMP, -- 更新时间
  reserved_field1 VARCHAR(255), -- 备用字段1
  reserved_field2 VARCHAR(255), -- 备用字段2
  reserved_field3 VARCHAR(255) -- 备用字段3
);

COMMENT ON TABLE ioc_cnccj_workshop_status IS '车间设备状况表';
COMMENT ON COLUMN ioc_cnccj_workshop_status.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_workshop_status.workshop_name IS '车间名称';
COMMENT ON COLUMN ioc_cnccj_workshop_status.operating_rate IS '开机率';
COMMENT ON COLUMN ioc_cnccj_workshop_status.total_equipment_count IS '设备总数';
COMMENT ON COLUMN ioc_cnccj_workshop_status.operating_equipment_count IS '总开机数';
COMMENT ON COLUMN ioc_cnccj_workshop_status.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_workshop_status.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_workshop_status.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_workshop_status.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_workshop_status.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_workshop_status.reserved_field3 IS '备用字段3';


-- 项目开机分布表
CREATE TABLE IF NOT EXISTS ioc_cnccj_project_status (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
    project_name VARCHAR(20), -- 项目名称
    product_name VARCHAR(20), -- 产品名称
    machine_id VARCHAR(20), -- 机台号
    record_date DATE, -- 日期
    updated_by VARCHAR(20), -- 更新人
    updated_at TIMESTAMP, -- 更新时间，使用 TIMESTAMP 更精确
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
);
COMMENT ON TABLE ioc_cnccj_project_status IS '项目开机分布表';
COMMENT ON COLUMN ioc_cnccj_project_status.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_project_status.project_name IS '项目名称';
COMMENT ON COLUMN ioc_cnccj_project_status.product_name IS '产品名称';
COMMENT ON COLUMN ioc_cnccj_project_status.machine_id IS '机台号';
COMMENT ON COLUMN ioc_cnccj_project_status.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_project_status.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_project_status.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_project_status.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_project_status.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_project_status.reserved_field3 IS '备用字段3';

-- 计划达成率当天表
CREATE TABLE IF NOT EXISTS ioc_cnccj_daily_achievement_rate (
  id SERIAL PRIMARY KEY, -- 自增主键
  workshop_name VARCHAR(20), -- 所属车间
  planned_quantity INT, -- 计划数量
  actual_quantity INT, -- 实际完成数量
  achievement_rate FLOAT, -- 计划达成率
  record_date DATE, -- 日期
  updated_by VARCHAR(20), -- 更新人
  updated_at TIMESTAMP, -- 更新时间
  reserved_field1 VARCHAR(255), -- 备用字段1
  reserved_field2 VARCHAR(255), -- 备用字段2
  reserved_field3 VARCHAR(255) -- 备用字段3
);
COMMENT ON TABLE ioc_cnccj_daily_achievement_rate IS '计划达成率当天表';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.planned_quantity IS '计划数量';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.actual_quantity IS '实际完成数量';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.achievement_rate IS '计划达成率';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_daily_achievement_rate.reserved_field3 IS '备用字段3';

--计划达成率7天表
CREATE TABLE IF NOT EXISTS ioc_cnccj_plan_completion_rate_7days (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
    completion_rate FLOAT, -- 达成率
    record_date DATE, -- 日期
    updated_by VARCHAR(20), -- 更新人
    update_date DATE, -- 更新日期
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
);
COMMENT ON TABLE ioc_cnccj_plan_completion_rate_7days IS '计划达成率近7天表';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.completion_rate IS '达成率';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.update_date IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_plan_completion_rate_7days.reserved_field3 IS '备用字段3';

--巡检检验合格率表
CREATE TABLE IF NOT EXISTS ioc_cnccj_xjjy_quality_rate (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
    quality_rate DECIMAL(5, 2), -- 当前合格率
    updated_by VARCHAR(20), -- 更新人
    updated_at TIMESTAMP, -- 更新日期，使用 TIMESTAMP 更精确
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
) ;
COMMENT ON TABLE ioc_cnccj_xjjy_quality_rate IS '巡检检验合格率表';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.quality_rate IS '合格率';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.updated_at IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate.reserved_field3 IS '备用字段3';

--巡检检验合格率近7天表
CREATE TABLE IF NOT EXISTS ioc_cnccj_xjjy_quality_rate_7days (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
    quality_rate DECIMAL(5, 2), -- 合格率
    record_date DATE, -- 日期
    updated_by VARCHAR(20), -- 更新人
    updated_at TIMESTAMP, -- 更新日期，使用 TIMESTAMP 更精确
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
) ;
COMMENT ON TABLE ioc_cnccj_xjjy_quality_rate_7days IS '巡检检验合格率近7天表';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.quality_rate IS '合格率';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.updated_at IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_xjjy_quality_rate_7days.reserved_field3 IS '备用字段3';

--刀具用量近7天表
CREATE TABLE IF NOT EXISTS ioc_cnccj_cuttingtool_usage_7days (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
    cuttingtool_usage DECIMAL(5, 2), -- 刀具用量
    record_date DATE, -- 日期
    updated_by VARCHAR(20), -- 更新人
    updated_at TIMESTAMP, -- 更新日期，使用 TIMESTAMP 更精确
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
) ;
COMMENT ON TABLE ioc_cnccj_cuttingtool_usage_7days IS '刀具用量近7天表';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.cuttingtool_usage IS '刀具用量';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.updated_at IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_7days.reserved_field3 IS '备用字段3';

--刀具消耗数据表
CREATE TABLE IF NOT EXISTS ioc_cnccj_cuttingtool_usage_info (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
    cuttingtool_usage DECIMAL(5, 2), -- 刀具用量
    updated_by VARCHAR(20), -- 更新人
    updated_at TIMESTAMP, -- 更新日期，使用 TIMESTAMP 更精确
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
) ;
COMMENT ON TABLE ioc_cnccj_cuttingtool_usage_info IS '刀具消耗数据表';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.cuttingtool_usage IS '刀具用量';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.updated_at IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_usage_info.reserved_field3 IS '备用字段3';

--设备状态分布表
CREATE TABLE IF NOT EXISTS ioc_cnccj_equipment_status (
    id SERIAL PRIMARY KEY, -- 自增主键
    workshop_name VARCHAR(20), -- 所属车间
status_name VARCHAR(20), -- 状态名称
status_count INT, -- 状态数量
updated_by VARCHAR(20), -- 更新人
    updated_at TIMESTAMP, -- 更新日期，使用 TIMESTAMP 更精确
    reserved_field1 VARCHAR(255), -- 备用字段1
    reserved_field2 VARCHAR(255), -- 备用字段2
    reserved_field3 VARCHAR(255) -- 备用字段3
) ;
COMMENT ON TABLE ioc_cnccj_equipment_status IS '设备状态分布表';
COMMENT ON COLUMN ioc_cnccj_equipment_status.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_equipment_status.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_equipment_status.status_name IS '状态名称';
COMMENT ON COLUMN ioc_cnccj_equipment_status.status_count IS '状态数量';
COMMENT ON COLUMN ioc_cnccj_equipment_status.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_equipment_status.updated_at IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_equipment_status.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_equipment_status.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_equipment_status.reserved_field3 IS '备用字段3';


--刀具近7天下机原因表
CREATE TABLE IF NOT EXISTS ioc_cnccj_cuttingtool_offline_7days (
   id SERIAL PRIMARY KEY, -- 自增主键
   workshop_name VARCHAR(20), -- 所属车间
   offline_name VARCHAR(20), -- 原因名称
   offline_count INT, -- 下机数量
   offline_rate  DECIMAL(5, 2),-- 下机占比
   record_date DATE, -- 日期
   updated_by VARCHAR(20), -- 更新人
   updated_at TIMESTAMP, -- 更新日期，使用 TIMESTAMP 更精确
   reserved_field1 VARCHAR(255), -- 备用字段1
   reserved_field2 VARCHAR(255), -- 备用字段2
   reserved_field3 VARCHAR(255) -- 备用字段3
) ;
COMMENT ON TABLE ioc_cnccj_cuttingtool_offline_7days IS '刀具近7天下机原因表';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.id IS '自增主键';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.offline_name IS '原因名称';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.offline_count IS '下机数量';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.offline_rate IS '下机占比';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.updated_at IS '更新日期';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_cuttingtool_offline_7days.reserved_field3 IS '备用字段3';

-- 环境信息表
CREATE TABLE IF NOT EXISTS ioc_cnccj_environment_info (
  id SERIAL PRIMARY KEY, -- 自增主键
  dust_free_workshop_level VARCHAR(20), -- 无尘车间级别
  average_humidity DECIMAL(5, 2), -- 平均湿度
  average_temperature DECIMAL(5, 2), -- 平均温度
  workshop_name VARCHAR(20), -- 所属车间
  record_date DATE, -- 日期
  updated_by VARCHAR(20), -- 更新人
  updated_at TIMESTAMP, -- 更新时间，使用 TIMESTAMP 更精确
  reserved_field1 VARCHAR(255), -- 备用字段1
  reserved_field2 VARCHAR(255), -- 备用字段2
  reserved_field3 VARCHAR(255) -- 备用字段3
);
COMMENT ON TABLE ioc_cnccj_environment_info IS '环境信息表';
COMMENT ON COLUMN ioc_cnccj_environment_info.dust_free_workshop_level IS '无尘车间级别';
COMMENT ON COLUMN ioc_cnccj_environment_info.average_humidity IS '平均湿度';
COMMENT ON COLUMN ioc_cnccj_environment_info.average_temperature IS '平均温度';
COMMENT ON COLUMN ioc_cnccj_environment_info.workshop_name IS '所属车间';
COMMENT ON COLUMN ioc_cnccj_environment_info.record_date IS '日期';
COMMENT ON COLUMN ioc_cnccj_environment_info.updated_by IS '更新人';
COMMENT ON COLUMN ioc_cnccj_environment_info.updated_at IS '更新时间';
COMMENT ON COLUMN ioc_cnccj_environment_info.reserved_field1 IS '备用字段1';
COMMENT ON COLUMN ioc_cnccj_environment_info.reserved_field2 IS '备用字段2';
COMMENT ON COLUMN ioc_cnccj_environment_info.reserved_field3 IS '备用字段3';

















package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncQualityRate7Days;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncQualityRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncQualityRate7DaysService {

    private final CncQualityRate7DaysRepository qualityRate7DaysRepository;

    /**
     * 获取指定车间的质量率近7天数据
     */
    public List<CncQualityRate7DaysDTO> getQualityRate7Days(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<CncQualityRate7Days> entities = qualityRate7DaysRepository.findLatest7RecordsByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncQualityRate7DaysDTO convertToDTO(CncQualityRate7Days entity) {
        CncQualityRate7DaysDTO dto = new CncQualityRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

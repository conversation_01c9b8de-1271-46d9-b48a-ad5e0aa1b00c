package com.vfdata.pujiang_vfd.modules.cnc.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_cnccj_personnel_info")
@Schema(description = "CNC车间人员信息")
public class CncPersonnelInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "personnel_type")
    @Schema(description = "人员类型")
    private String personnelType;

    @Column(name = "staff_count")
    @Schema(description = "人员总数")
    private Integer staffCount;

    @Column(name = "general_worker_count")
    @Schema(description = "类型下人员数")
    private Integer generalWorkerCount;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

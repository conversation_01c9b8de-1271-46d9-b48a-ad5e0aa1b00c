package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.VisitorRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface VisitorRecordRepository extends JpaRepository<VisitorRecord, Long> {
    
    List<VisitorRecord> findTop15ByOrderByVisitTimeDesc();
}

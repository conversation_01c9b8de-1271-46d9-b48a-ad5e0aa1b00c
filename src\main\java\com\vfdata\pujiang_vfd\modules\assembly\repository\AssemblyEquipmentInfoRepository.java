package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface AssemblyEquipmentInfoRepository extends JpaRepository<AssemblyEquipmentInfo, Long> {
    
    @Query("SELECT e FROM AssemblyEquipmentInfo e WHERE e.recordDate = (SELECT MAX(e2.recordDate) FROM AssemblyEquipmentInfo e2)")
    Optional<AssemblyEquipmentInfo> findLatest();
} 
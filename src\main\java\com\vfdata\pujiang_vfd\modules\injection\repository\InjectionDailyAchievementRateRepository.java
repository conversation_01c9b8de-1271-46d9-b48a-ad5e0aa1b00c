package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionDailyAchievementRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface InjectionDailyAchievementRateRepository extends JpaRepository<InjectionDailyAchievementRate, Long> {
    
    @Query(value = "SELECT DISTINCT ON (workshop_name) * FROM ioc_zscj_daily_achievement_rate ORDER BY workshop_name, record_date DESC, id DESC", nativeQuery = true)
    List<InjectionDailyAchievementRate> findLatestByWorkshop();

    @Query(value = "SELECT * FROM ioc_zscj_daily_achievement_rate WHERE workshop_name = :workshopName ORDER BY record_date DESC, id DESC LIMIT 7", nativeQuery = true)
    List<InjectionDailyAchievementRate> findLatest7ByWorkshopName(@Param("workshopName") String workshopName);

    @Query(value = "SELECT * FROM ioc_zscj_daily_achievement_rate WHERE workshop_name = :workshopName AND record_date BETWEEN :startDate AND :endDate ORDER BY record_date", nativeQuery = true)
    List<InjectionDailyAchievementRate> findByWorkshopNameAndDateBetween(
            @Param("workshopName") String workshopName,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    @Query(value = "SELECT * FROM ioc_zscj_plan_completion_rate_7days ORDER BY record_date DESC", nativeQuery = true)
    List<InjectionDailyAchievementRate> findLatest7();

    @Query(value = "SELECT * FROM ioc_zscj_daily_achievement_rate WHERE id in (select max(id) from ioc_zscj_daily_achievement_rate group by workshop_name  )", nativeQuery = true)
    List<InjectionDailyAchievementRate> findTodayAchievementRate();
} 
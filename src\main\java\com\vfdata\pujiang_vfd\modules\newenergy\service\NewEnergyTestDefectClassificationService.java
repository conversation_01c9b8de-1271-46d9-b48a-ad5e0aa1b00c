package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyTestDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyTestDefectClassification;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyTestDefectClassificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyTestDefectClassificationService {

    private final NewEnergyTestDefectClassificationRepository testDefectClassificationRepository;

    /**
     * 获取最新记录日期的所有测试不良分类数据
     */
    public List<NewEnergyTestDefectClassificationDTO> getLatestTestDefectClassification() {
        List<NewEnergyTestDefectClassification> defectList = testDefectClassificationRepository.findLatestRecords();
        
        return defectList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyTestDefectClassificationDTO convertToDTO(NewEnergyTestDefectClassification entity) {
        NewEnergyTestDefectClassificationDTO dto = new NewEnergyTestDefectClassificationDTO();
        dto.setId(entity.getId());
        dto.setDefect_type_name(entity.getDefectTypeName());
        dto.setDefect_type_count(entity.getDefectTypeCount());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

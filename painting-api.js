import request from './request.js'

// 喷涂车间人员信息
export const personnel_info = async () => {
    return await request.get(`/painting/personnel-info/latest`);
};

// 喷涂车间设备信息
export const equipment_info = async () => {
    return await request.get(`/painting/equipment-info/latest`);
};

// 喷涂车间出勤率
export const attendance_rate = async () => {
    return await request.get(`/painting/attendance-rate/latest`);
};

// 喷涂车间环境信息
export const environment_info = async () => {
    return await request.get(`/painting/environment-info/latest`);
};

// 喷涂车间当日计划达成率 (可选车间参数)
export const daily_achievement_rate = async (workshop_name = '') => {
    const params = workshop_name ? `?workshop_name=${encodeURIComponent(workshop_name)}` : '';
    return await request.get(`/painting/daily-achievement-rate${params}`);
};

// 喷涂车间品质检验良率 (可选车间参数)
export const quality_rate = async (workshop_name = '') => {
    const params = workshop_name ? `?workshop_name=${encodeURIComponent(workshop_name)}` : '';
    return await request.get(`/painting/quality-rate${params}`);
};

// 喷涂车间油漆用量 (可选车间参数)
export const paint_usage = async (workshop_name = '') => {
    const params = workshop_name ? `?workshop_name=${encodeURIComponent(workshop_name)}` : '';
    return await request.get(`/painting/paint-usage/latest${params}`);
};

// 喷涂车间计划达成率近7天 (需要车间参数)
export const plan_completion_rate_7days = async (workshop_name) => {
    return await request.get(`/painting/plan-completion-rate-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// 喷涂车间品质检验良率近7天 (需要车间参数)
export const quality_rate_7days = async (workshop_name) => {
    return await request.get(`/painting/quality-rate-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// 喷涂车间设备状态分布 (可选车间参数)
export const equipment_status = async (workshop_name = '') => {
    const params = workshop_name ? `?workshop_name=${encodeURIComponent(workshop_name)}` : '';
    return await request.get(`/painting/equipment-status${params}`);
};

// 喷涂车间项目开机分布 (需要车间参数)
export const project_status = async (workshop_name) => {
    return await request.get(`/painting/project-status?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// 喷涂车间品质不良分类 (可选车间参数)
export const defect_classification = async (workshop_name = '') => {
    const params = workshop_name ? `?workshop_name=${encodeURIComponent(workshop_name)}` : '';
    return await request.get(`/painting/defect-classification${params}`);
};

// 喷涂车间RTO运行状态
export const rto_status = async () => {
    return await request.get(`/painting/rto-status/latest`);
};

// 喷涂车间设备状况
export const workshop_status = async () => {
    return await request.get(`/painting/workshop-status/latest`);
};

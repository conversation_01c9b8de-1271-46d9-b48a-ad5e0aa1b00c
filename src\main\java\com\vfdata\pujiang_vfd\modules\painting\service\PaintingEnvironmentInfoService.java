package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingEnvironmentInfo;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingEnvironmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingEnvironmentInfoService {

    private final PaintingEnvironmentInfoRepository environmentInfoRepository;

    /**
     * 获取最新的环境信息
     */
    public List<PaintingEnvironmentInfoDTO> getLatestEnvironmentInfo() {
        List<PaintingEnvironmentInfo> entities = environmentInfoRepository.findLatestRecords();
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingEnvironmentInfoDTO convertToDTO(PaintingEnvironmentInfo entity) {
        PaintingEnvironmentInfoDTO dto = new PaintingEnvironmentInfoDTO();
        dto.setId(entity.getId());
        dto.setDust_free_workshop_level(entity.getDustFreeWorkshopLevel());
        dto.setAverage_humidity(entity.getAverageHumidity());
        dto.setAverage_temperature(entity.getAverageTemperature());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyQualityRate7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyQualityRate7DaysRepository extends JpaRepository<NewEnergyQualityRate7Days, Long> {
    
    /**
     * 根据车间名称获取最新7条记录数据
     * 按record_date降序排列，取前7条
     */
    @Query("SELECT n FROM NewEnergyQualityRate7Days n WHERE n.workshopName = :workshopName ORDER BY n.recordDate DESC LIMIT 7")
    List<NewEnergyQualityRate7Days> findTop7ByWorkshopNameOrderByRecordDateDesc(@Param("workshopName") String workshopName);
}

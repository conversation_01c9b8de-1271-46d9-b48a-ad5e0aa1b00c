package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingWorkshopStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingWorkshopStatusRepository extends JpaRepository<PaintingWorkshopStatus, Long> {

    /**
     * 获取最新记录日期的所有车间设备状况数据
     */
    @Query("SELECT p FROM PaintingWorkshopStatus p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingWorkshopStatus p2)")
    List<PaintingWorkshopStatus> findLatestRecords();
}

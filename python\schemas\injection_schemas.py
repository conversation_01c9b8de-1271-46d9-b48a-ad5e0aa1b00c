from pydantic import BaseModel, ConfigDict
from datetime import date, datetime
from typing import Optional, List
from decimal import Decimal

# 基础模型，包含共同字段
class BaseInfo(BaseModel):
    record_date: Optional[date] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    class Config:
        from_attributes = True

# 人员信息模型
class PersonnelInfoBase(BaseModel):
    personnel_type: Optional[str] = None
    staff_count: Optional[int] = None
    general_worker_count: Optional[int] = None

class PersonnelInfoCreate(PersonnelInfoBase, BaseInfo):
    pass

class PersonnelInfo(PersonnelInfoCreate):
    id: int

# 出勤率模型
class AttendanceRateBase(BaseModel):
    clerk_attendance_rate: Optional[Decimal] = None
    worker_attendance_rate: Optional[Decimal] = None

class AttendanceRateCreate(AttendanceRateBase, BaseInfo):
    pass

class AttendanceRate(AttendanceRateCreate):
    id: int

# 环境信息模型
class EnvironmentInfoBase(BaseModel):
    dust_free_workshop_level: Optional[str] = None
    average_humidity: Optional[Decimal] = None
    average_temperature: Optional[Decimal] = None
    workshop_name: Optional[str] = None

class EnvironmentInfoCreate(EnvironmentInfoBase, BaseInfo):
    pass

class EnvironmentInfo(EnvironmentInfoCreate):
    id: int

# 设备信息模型
class EquipmentInfoBase(BaseModel):
    total_equipment_count: Optional[int] = None
    operating_equipment_count: Optional[int] = None
    equipment_overall_efficiency: Optional[Decimal] = None
    operating_rate: Optional[Decimal] = None

class EquipmentInfoCreate(EquipmentInfoBase, BaseInfo):
    pass

class EquipmentInfo(EquipmentInfoCreate):
    id: int

# 设备状态分布模型
class EquipmentStatusDistributionBase(BaseModel):
    status_name: Optional[str] = None
    status_count: Optional[int] = None
    workshop_name: Optional[str] = None
class EquipmentStatusDistributionCreate(EquipmentStatusDistributionBase, BaseInfo):
    pass

class EquipmentStatusDistribution(EquipmentStatusDistributionCreate):
    id: int

# 质量分布模型
class QualityDistributionBase(BaseModel):
    workshop_name: Optional[str] = None
    quality_name: Optional[str] = None
    quality_rate: Optional[Decimal] = None

class QualityDistributionCreate(QualityDistributionBase, BaseInfo):
    pass

class QualityDistribution(QualityDistributionCreate):
    id: int

# 停机类型分布模型
class StopTypeDistributionBase(BaseModel):
    workshop_name: Optional[str] = None
    stop_type_name: Optional[str] = None
    type_count: Optional[int] = None

class StopTypeDistributionCreate(StopTypeDistributionBase, BaseInfo):
    pass

class StopTypeDistribution(StopTypeDistributionCreate):
    id: int

# 抽检不良类型分析模型
class InspectionDefectTypeAnalysisBase(BaseModel):
    workshop_name: Optional[str] = None
    defect_type_name: Optional[str] = None
    type_count: Optional[int] = None

class InspectionDefectTypeAnalysisCreate(InspectionDefectTypeAnalysisBase, BaseInfo):
    pass

class InspectionDefectTypeAnalysis(InspectionDefectTypeAnalysisCreate):
    id: int

# 生产计划模型
class ProductionPlanBase(BaseModel):
    """生产计划基础模型"""
    product_name: str
    plan_quantity: int
    actual_quantity: int
    completion_rate: float

class ProductionPlanCreate(ProductionPlanBase):
    """创建生产计划模型"""
    pass

class ProductionPlan(ProductionPlanBase):
    """生产计划模型"""
    id: int

# 设备状态模型
class EquipmentStatusBase(BaseModel):
    """设备状态基础模型"""
    equipment_name: str
    status: str
    runtime: float

class EquipmentStatusCreate(EquipmentStatusBase):
    """创建设备状态模型"""
    pass

class EquipmentStatus(EquipmentStatusBase):
    """设备状态模型"""
    id: int

# 质量记录模型
class QualityRecordBase(BaseModel):
    """质量记录基础模型"""
    product_name: str
    inspection_quantity: int
    defect_quantity: int
    defect_rate: float

class QualityRecordCreate(QualityRecordBase):
    """创建质量记录模型"""
    pass

class QualityRecord(QualityRecordBase):
    """质量记录模型"""
    id: int

# 物料消耗模型
class MaterialConsumptionBase(BaseModel):
    """物料消耗基础模型"""
    material_name: str
    consumption_quantity: float
    unit: str

class MaterialConsumptionCreate(MaterialConsumptionBase):
    """创建物料消耗模型"""
    pass

class MaterialConsumption(MaterialConsumptionBase):
    """物料消耗模型"""
    id: int

# 添加新的人员信息统计模型
class PersonnelCount(BaseModel):
    """人员数量统计模型"""
    count: Optional[int] = None
    personnel_type: Optional[str] = None

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
    )

class InjectionBase(BaseModel):
    """基础模型"""
    id: Optional[int] = None
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S") if v else None
        }
    )

class PersonnelInfo(InjectionBase):
    """人员信息模型"""
    personnel_type: Optional[str] = None
    staff_count: Optional[int] = None
    general_worker_count: Optional[int] = None

class AttendanceRate(InjectionBase):
    """出勤率模型"""
    clerk_attendance_rate: Optional[float] = None
    worker_attendance_rate: Optional[float] = None

class EnvironmentInfo(InjectionBase):
    """环境信息模型"""
    dust_free_workshop_level: Optional[str] = None
    average_humidity: Optional[float] = None
    average_temperature: Optional[float] = None
    workshop_name: Optional[str] = None

class ProjectStatus(InjectionBase):
    """项目状态模型"""
    workshop_name: Optional[str] = None
    project_name: Optional[str] = None
    product_name: Optional[str] = None
    machine_id: Optional[str] = None

class DailyAchievementRate(InjectionBase):
    """当天计划达成率模型"""
    workshop_name: Optional[str] = None
    planned_quantity: Optional[int] = None
    actual_quantity: Optional[int] = None
    achievement_rate: Optional[float] = None

class PlanCompletionRate7Days(InjectionBase):
    """7天计划达成率模型"""
    workshop_name: Optional[str] = None
    completion_rate: Optional[float] = None
    update_date: Optional[datetime] = None

class PatrolInspection(InjectionBase):
    """巡检合格率模型"""
    workshop_name: Optional[str] = None
    inspection_type: Optional[str] = None
    pass_rate: Optional[float] = None

class SamplingInspectionPassRate(InjectionBase):
    """抽检合格率模型"""
    workshop_name: Optional[str] = None
    pass_rate: Optional[float] = None

class EquipmentStatusDistribution(InjectionBase):
    """设备状态分布模型"""
    workshop_name: Optional[str] = None
    status_name: Optional[str] = None
    status_count: Optional[int] = None

class QualityDistribution(InjectionBase):
    """品质分布模型"""
    workshop_name: Optional[str] = None
    quality_name: Optional[str] = None
    quality_rate: Optional[float] = None

class StopTypeDistribution(InjectionBase):
    """停机类型分布模型"""
    workshop_name: Optional[str] = None
    stop_type_name: Optional[str] = None
    type_count: Optional[int] = None

class InspectionDefectTypeAnalysis(InjectionBase):
    """不良类型分析模型"""
    workshop_name: Optional[str] = None
    defect_type_name: Optional[str] = None
    type_count: Optional[int] = None

class InspectionDefectTypeAnalysisCreate(BaseModel):
    """创建不良类型分析模型"""
    workshop_name: Optional[str] = None
    defect_type_name: Optional[str] = None
    type_count: Optional[int] = None
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None

class QualityOverall(InjectionBase):
    """整体品质分布模型"""
    workshop_name: Optional[str] = None
    quality_type: Optional[str] = None
    quantity: Optional[int] = None
    proportion: Optional[float] = None

class PatrolDefect(InjectionBase):
    """巡检不良类型分析模型"""
    workshop_name: Optional[str] = None
    defect_type: Optional[str] = None
    defect_count: Optional[int] = None

class WorkshopEquipmentStatus(InjectionBase):
    """车间设备状态模型"""
    workshop_name: Optional[str] = None
    equipment_name: Optional[str] = None
    status: Optional[str] = None
    runtime: Optional[float] = None

class InspectionPassRate(InjectionBase):
    """巡检合格率模型"""
    workshop_name: Optional[str] = None
    pass_rate: Optional[float] = None
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    class Config:
        from_attributes = True

class WorkshopStatus(InjectionBase):
    """车间设备状态模型"""
    workshop_name: Optional[str] = None
    total_equipment: Optional[int] = None
    operating_equipment: Optional[int] = None
    operating_rate: Optional[float] = None
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    class Config:
        from_attributes = True

class EquipmentInfo(InjectionBase):
    """设备信息模型"""
    total_equipment_count: Optional[int] = None
    operating_equipment_count: Optional[int] = None
    equipment_overall_efficiency: Optional[float] = None
    operating_rate: Optional[float] = None
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    class Config:
        from_attributes = True 
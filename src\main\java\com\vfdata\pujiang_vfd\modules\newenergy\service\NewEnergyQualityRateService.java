package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyQualityRate;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyQualityRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyQualityRateService {

    private final NewEnergyQualityRateRepository qualityRateRepository;

    /**
     * 获取每个车间最新记录日期的抽检检验合格率数据
     */
    public List<NewEnergyQualityRateDTO> getLatestQualityRate() {
        List<NewEnergyQualityRate> qualityRateList = qualityRateRepository.findLatestByEachWorkshop();
        
        return qualityRateList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyQualityRateDTO convertToDTO(NewEnergyQualityRate entity) {
        NewEnergyQualityRateDTO dto = new NewEnergyQualityRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

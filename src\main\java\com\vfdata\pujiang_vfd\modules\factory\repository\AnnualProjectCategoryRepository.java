package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.AnnualProjectCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface AnnualProjectCategoryRepository extends JpaRepository<AnnualProjectCategory, Long> {

    @Query("SELECT a FROM AnnualProjectCategory a WHERE a.projectName = :projectName ORDER BY a.recordDate DESC")
    List<AnnualProjectCategory> findByProjectName(@Param("projectName") String projectName);

    @Query("SELECT a FROM AnnualProjectCategory a ORDER BY a.recordDate DESC")
    List<AnnualProjectCategory> findAllOrderByRecordDateDesc();

    @Query("SELECT a FROM AnnualProjectCategory a WHERE a.projectName = :projectName AND " +
           "(:startDate IS NULL OR a.recordDate >= :startDate) AND " +
           "(:endDate IS NULL OR a.recordDate <= :endDate) ORDER BY a.recordDate DESC")
    List<AnnualProjectCategory> findByProjectNameAndDateRange(
            @Param("projectName") String projectName,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    @Query("SELECT a FROM AnnualProjectCategory a WHERE " +
           "(:startDate IS NULL OR a.recordDate >= :startDate) AND " +
           "(:endDate IS NULL OR a.recordDate <= :endDate) ORDER BY a.recordDate DESC")
    List<AnnualProjectCategory> findByDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
} 
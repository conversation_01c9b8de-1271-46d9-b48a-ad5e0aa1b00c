package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.PersonnelCountDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyPersonnelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/personnel")
@RequiredArgsConstructor
public class NewEnergyPersonnelInfoController {

    private final NewEnergyPersonnelInfoService personnelInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新人员信息")
    public ResponseUtils.Result<List<PersonnelCountDTO>> getLatestPersonnelInfo() {
        try {
            List<PersonnelCountDTO> infoList = personnelInfoService.getLatestPersonnelInfo();
            return ResponseUtils.success(infoList);
        } catch (Exception e) {
            return ResponseUtils.error("获取人员信息失败：" + e.getMessage());
        }
    }
}

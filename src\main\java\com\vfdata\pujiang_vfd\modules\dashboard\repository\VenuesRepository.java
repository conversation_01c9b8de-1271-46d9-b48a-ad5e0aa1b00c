package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.Venues;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface VenuesRepository extends JpaRepository<Venues, Long> {
    
    @Query("SELECT v.venuesType, v.venuesNum, v.venuesRemaining " +
           "FROM Venues v " +
           "WHERE v.id IN ( " +
           "    SELECT MAX(v2.id) " +
           "    FROM Venues v2 " +
           "    GROUP BY v2.venuesType " +
           ")")
    List<Object[]> findVenuesStatistics();
}

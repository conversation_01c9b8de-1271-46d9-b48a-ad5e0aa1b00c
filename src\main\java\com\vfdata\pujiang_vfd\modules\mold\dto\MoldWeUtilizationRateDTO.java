package com.vfdata.pujiang_vfd.modules.mold.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "模具车间WE稼动率DTO")
public class MoldWeUtilizationRateDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "周期")
    private String period;

    @Schema(description = "浦江WE稼动率")
    private BigDecimal once_utilization_rate;

    @Schema(description = "博罗WE稼动率")
    private BigDecimal end_utilization_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

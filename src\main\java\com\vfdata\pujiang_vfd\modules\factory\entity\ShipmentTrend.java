package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Table(name = "ioc_qc_shipment_count_12months")
@Schema(description = "近12个月出货数量")
@Getter
@Setter
public class ShipmentTrend extends BaseEntity {
    
    @Schema(description = "发货数量")
    @Column(name = "shipment_count", precision = 10, scale = 2)
    private BigDecimal shipmentCount;
    
    @Schema(description = "预留字段1")
    private String reservedField1;
    
    @Schema(description = "预留字段2")
    private String reservedField2;
    
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
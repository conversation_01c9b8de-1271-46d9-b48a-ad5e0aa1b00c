package com.vfdata.pujiang_vfd.sync.mapper;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.EmployeeInOut;
import com.vfdata.pujiang_vfd.sync.mapper.dashboard.EmployeeInOutMapper;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class EmployeeInOutMapperTest {

    private final EmployeeInOutMapper mapper = new EmployeeInOutMapper();

    @Test
    void testMapWithRecordDateContainingTime() {
        // 测试recordDate包含时间信息的情况
        Map<String, Object> sourceData = new HashMap<>();
        sourceData.put("id", 1655014L);
        sourceData.put("employeeName", "龚武");
        sourceData.put("employeeAddr", "5号宿舍闸机右3出");
        sourceData.put("employeeState", "出");
        sourceData.put("recordDate", "2025-06-23 00:00:13");  // 包含时间信息
        sourceData.put("updateman", null);
        sourceData.put("updatetime", null);  // updatetime为空

        EmployeeInOut result = mapper.mapToEntity(sourceData);

        assertNotNull(result);
        assertEquals(Long.valueOf(1655014L), result.getId());
        assertEquals("龚武", result.getEmployeeName());
        assertEquals("5号宿舍闸机右3出", result.getEmployeeAddr());
        assertEquals("出", result.getEmployeeState());

        // recordDate现在保存完整的时间信息
        assertEquals(LocalDateTime.of(2025, 6, 23, 0, 0, 13), result.getRecordDate());

        // updatetime为空
        assertNull(result.getUpdatetime());
    }

    @Test
    void testMapWithSeparateRecordDateAndUpdatetime() {
        // 测试recordDate和updatetime都有值的情况
        Map<String, Object> sourceData = new HashMap<>();
        sourceData.put("id", 1655015L);
        sourceData.put("employeeName", "张三");
        sourceData.put("employeeAddr", "1号门");
        sourceData.put("employeeState", "进");
        sourceData.put("recordDate", "2025-06-23 08:30:00");
        sourceData.put("updateman", "admin");
        sourceData.put("updatetime", "2025-06-23 08:35:00");  // updatetime有值

        EmployeeInOut result = mapper.mapToEntity(sourceData);

        assertNotNull(result);
        assertEquals(LocalDateTime.of(2025, 6, 23, 8, 30, 0), result.getRecordDate());  // 保存完整时间
        assertEquals(LocalDateTime.of(2025, 6, 23, 8, 35, 0), result.getUpdatetime());  // 使用updatetime的值
        assertEquals("admin", result.getUpdateman());
    }

    @Test
    void testMapWithDateOnlyRecordDate() {
        // 测试recordDate只包含日期的情况
        Map<String, Object> sourceData = new HashMap<>();
        sourceData.put("id", 1655016L);
        sourceData.put("employeeName", "李四");
        sourceData.put("recordDate", "2025-06-23");  // 只有日期
        sourceData.put("updatetime", null);

        EmployeeInOut result = mapper.mapToEntity(sourceData);

        assertNotNull(result);
        // 纯日期会被解析为当天的开始时间
        assertNull(result.getRecordDate());  // parseDateTime对纯日期返回null
        assertNull(result.getUpdatetime());
    }

    @Test
    void testMapWithTimezoneFormat() {
        // 测试带时区的格式
        Map<String, Object> sourceData = new HashMap<>();
        sourceData.put("id", 1655017L);
        sourceData.put("employeeName", "王五");
        sourceData.put("recordDate", "2025-06-23 +08");  // 带时区
        sourceData.put("updatetime", null);

        EmployeeInOut result = mapper.mapToEntity(sourceData);

        assertNotNull(result);
        assertEquals(LocalDateTime.of(2025, 6, 23, 0, 0, 0), result.getRecordDate());  // 转换为当天开始时间
        assertNull(result.getUpdatetime());
    }
}

package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionDefectAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionDefectAnalysisRepository extends JpaRepository<InjectionDefectAnalysis, Long> {
    
    @Query("SELECT d FROM InjectionDefectAnalysis d WHERE d.workshopName = :workshopName ORDER BY d.reservedField1 DESC, d.id DESC")
    List<InjectionDefectAnalysis> findByWorkshopName(@Param("workshopName") String workshopName);

    @Query("SELECT d FROM InjectionDefectAnalysis d ORDER BY d.workshopName, d.recordDate DESC, d.id DESC")
    List<InjectionDefectAnalysis> findAllDefectAnalyses();

    @Query("SELECT d FROM InjectionDefectAnalysis d WHERE d.recordDate = (SELECT MAX(d2.recordDate) FROM InjectionDefectAnalysis d2)")
    List<InjectionDefectAnalysis> findLatestDefectAnalyses();

    @Query("SELECT d FROM InjectionDefectAnalysis d WHERE d.recordDate = (SELECT MAX(d2.recordDate) FROM InjectionDefectAnalysis d2) AND d.workshopName = :workshopName")
    List<InjectionDefectAnalysis> findLatestDefectAnalysesByWorkshopName(@Param("workshopName") String workshopName);
} 
package com.vfdata.pujiang_vfd.sync.mapper.safety;

import com.vfdata.pujiang_vfd.modules.safety.entity.VehicleRecord;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class VehicleRecordMapper implements DataMapper<VehicleRecord> {

    @Override
    public VehicleRecord mapToEntity(Map<String, Object> sourceData) {
        try {
            VehicleRecord entity = new VehicleRecord();

            // 映射基础字段
            entity.setId(DateTimeParseUtil.getLongValue(sourceData, "id"));
            entity.setLicensePlate(DateTimeParseUtil.getStringValue(sourceData, "licensePlate"));
            entity.setChannelName(DateTimeParseUtil.getStringValue(sourceData, "channelName"));
            entity.setStatus(DateTimeParseUtil.getStringValue(sourceData, "status"));
            entity.setUpdatedBy(DateTimeParseUtil.getStringValue(sourceData, "updatedBy"));

            // 映射时间字段
            String entryTimeStr = DateTimeParseUtil.getStringValue(sourceData, "entryTime");
            entity.setEntryTime(DateTimeParseUtil.parseDateTime(entryTimeStr));

            // 映射更新日期
            String updateDateStr = DateTimeParseUtil.getStringValue(sourceData, "updateDate");
            entity.setUpdateDate(DateTimeParseUtil.parseDate(updateDateStr));

            // 映射预留字段
            entity.setReservedField1(DateTimeParseUtil.getStringValue(sourceData, "reservedField1"));
            entity.setReservedField2(DateTimeParseUtil.getStringValue(sourceData, "reservedField2"));
            entity.setReservedField3(DateTimeParseUtil.getStringValue(sourceData, "reservedField3"));

            return entity;

        } catch (Exception e) {
            throw new RuntimeException("映射VehicleRecord数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getUniqueField() {
        return "id";
    }
}

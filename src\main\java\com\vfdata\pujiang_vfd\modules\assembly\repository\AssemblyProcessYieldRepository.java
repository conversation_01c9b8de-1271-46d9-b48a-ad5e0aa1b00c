package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyProcessYield;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface AssemblyProcessYieldRepository extends JpaRepository<AssemblyProcessYield, Long> {
    
    @Query("SELECT p FROM AssemblyProcessYield p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM AssemblyProcessYield p2)")
    List<AssemblyProcessYield> findLatest();

    @Query("SELECT p FROM AssemblyProcessYield p WHERE p.workshopName = :workshopName AND p.recordDate = (SELECT MAX(p2.recordDate) FROM AssemblyProcessYield p2 WHERE p2.workshopName = :workshopName)")
    List<AssemblyProcessYield> findLatestByWorkshopName(@Param("workshopName") String workshopName);

    // 获取最新7条记录（所有车间）
    @Query(value = "SELECT * FROM ioc_zzcj_process_yield_current_info ORDER BY record_date DESC LIMIT 7", nativeQuery = true)
    List<AssemblyProcessYield> findTop7ByOrderByRecordDateDesc();

    // 获取指定车间最新7条记录
    @Query(value = "SELECT * FROM ioc_zzcj_process_yield_current_info WHERE workshop_name = :workshopName ORDER BY record_date DESC LIMIT 7", nativeQuery = true)
    List<AssemblyProcessYield> findTop7ByWorkshopNameOrderByRecordDateDesc(@Param("workshopName") String workshopName);
} 
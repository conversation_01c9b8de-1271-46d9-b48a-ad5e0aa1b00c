package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncWorkshopStatus;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncWorkshopStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncWorkshopStatusService {

    private final CncWorkshopStatusRepository workshopStatusRepository;

    /**
     * 获取每个车间最新的设备状况信息
     */
    public List<CncWorkshopStatusDTO> getLatestWorkshopStatus() {
        List<CncWorkshopStatus> workshopStatuses = workshopStatusRepository.findLatestRecords();
        return workshopStatuses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncWorkshopStatusDTO convertToDTO(CncWorkshopStatus entity) {
        CncWorkshopStatusDTO dto = new CncWorkshopStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setTotal_equipment(entity.getTotalEquipmentCount());
        dto.setOperating_equipment(entity.getOperatingEquipmentCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

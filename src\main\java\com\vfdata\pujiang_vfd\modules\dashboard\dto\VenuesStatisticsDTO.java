package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "娱乐会议场所统计DTO")
public class VenuesStatisticsDTO {

    @Schema(description = "统计数据列表")
    private List<StatisticItem> statistics;

    @Data
    @Schema(description = "统计项")
    public static class StatisticItem {
        @Schema(description = "场所类型")
        private String type;

        @Schema(description = "总数量")
        private Integer total;

        @Schema(description = "剩余数量")
        private Integer remaining;
    }
}

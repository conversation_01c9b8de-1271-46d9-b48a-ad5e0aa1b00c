from sqlalchemy import Column, Integer, String, Date, DECIMAL, TIMESTAMP, Text, Float, DateTime, ForeignKey, Sequence
from sqlalchemy.sql import func
from database import Base
from datetime import datetime

class InjectionBase(Base):
    """注塑车间基础模型"""
    __abstract__ = True
    
    id = Column(Integer, Sequence('ioc_zscj_base_id_seq'), primary_key=True, index=True)
    record_date = Column(DateTime, nullable=False, comment="记录日期")
    updated_by = Column(String(50), nullable=False, comment="更新人")
    updated_at = Column(DateTime, nullable=False, default=datetime.now, comment="更新时间")
    reserved_field1 = Column(String(255), nullable=True, comment="预留字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="预留字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="预留字段3")

class PersonnelInfo(InjectionBase):
    """人员信息表"""
    __tablename__ = "ioc_zscj_personnel_info"
    
    personnel_type = Column(String(20))
    staff_count = Column(Integer)
    general_worker_count = Column(Integer)
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class AttendanceRate(InjectionBase):
    """出勤率表"""
    __tablename__ = "ioc_zscj_attendance_rate"
    
    clerk_attendance_rate = Column(DECIMAL(5, 2))
    worker_attendance_rate = Column(DECIMAL(5, 2))
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class EnvironmentInfo(InjectionBase):
    """环境信息表"""
    __tablename__ = "ioc_zscj_environment_info"
    
    dust_free_workshop_level = Column(String(20))
    average_humidity = Column(DECIMAL(5, 2))
    average_temperature = Column(DECIMAL(5, 2))
    workshop_name = Column(String(20))
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class EquipmentInfo(InjectionBase):
    """设备信息表"""
    __tablename__ = "ioc_zscj_equipment_info"
    
    total_equipment_count = Column(Integer)
    operating_equipment_count = Column(Integer)
    equipment_overall_efficiency = Column(DECIMAL(5, 2))
    operating_rate = Column(DECIMAL(5, 2))

class ProductionPlan(InjectionBase):
    """生产计划表"""
    __tablename__ = "injection_production_plan"
    
    product_name = Column(String(100), nullable=False, comment="产品名称")
    plan_quantity = Column(Integer, nullable=False, comment="计划数量")
    actual_quantity = Column(Integer, nullable=False, comment="实际数量")
    completion_rate = Column(Float, nullable=False, comment="完成率")

class EquipmentStatus(InjectionBase):
    """设备状态表"""
    __tablename__ = "injection_equipment_status"
    
    equipment_name = Column(String(100), nullable=False, comment="设备名称")
    status = Column(String(50), nullable=False, comment="设备状态")
    runtime = Column(Float, nullable=False, comment="运行时间")

class QualityRecord(InjectionBase):
    """质量记录表"""
    __tablename__ = "injection_quality_record"
    
    product_name = Column(String(100), nullable=False, comment="产品名称")
    inspection_quantity = Column(Integer, nullable=False, comment="检验数量")
    defect_quantity = Column(Integer, nullable=False, comment="不良品数量")
    defect_rate = Column(Float, nullable=False, comment="不良率")

class MaterialConsumption(InjectionBase):
    """物料消耗表"""
    __tablename__ = "injection_material_consumption"
    
    material_name = Column(String(100), nullable=False, comment="物料名称")
    consumption_quantity = Column(Float, nullable=False, comment="消耗数量")
    unit = Column(String(20), nullable=False, comment="单位")

class EquipmentStatusDistribution(InjectionBase):
    """设备状态分布表"""
    __tablename__ = "ioc_zscj_equipment_status_distribution"

    status_name = Column(String(50))
    status_count = Column(Integer)
    workshop_name = Column(String(20))

class QualityDistribution(InjectionBase):
    """质量分布表"""
    __tablename__ = "ioc_quality_distribution_chart"

    workshop_name = Column(String(20))
    quality_name = Column(String(50))
    quality_rate = Column(DECIMAL(5, 2))
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class StopTypeDistribution(InjectionBase):
    """停机类型分布表"""
    __tablename__ = "ioc_zscj_stop_type_distribution"

    workshop_name = Column(String(20))
    stop_type_name = Column(String(50))
    type_count = Column(Integer)
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class InspectionDefectTypeAnalysis(InjectionBase):
    """抽检不良类型分析表"""
    __tablename__ = "ioc_zscj_inspection_defect_type_analysis"

    workshop_name = Column(String(20))
    defect_type_name = Column(String(50))
    type_count = Column(Integer)
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class ProjectStatus(InjectionBase):
    __tablename__ = "ioc_zscj_project_status"
    
    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))
    project_name = Column(String(20))
    product_name = Column(String(20))
    machine_id = Column(String(20))
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class DailyAchievementRate(InjectionBase):
    __tablename__ = "ioc_zscj_daily_achievement_rate"
    
    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))
    planned_quantity = Column(Integer)
    actual_quantity = Column(Integer)
    achievement_rate = Column(Float)
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class PlanCompletionRate7Days(InjectionBase):
    __tablename__ = "ioc_zscj_plan_completion_rate_7days"
    
    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))
    completion_rate = Column(Float)
    record_date = Column(Date)
    updated_by = Column(String(20))
    update_date = Column(Date)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class SamplingInspectionPassRate(InjectionBase):
    __tablename__ = "ioc_zscj_sampling_inspection_pass_rate"
    
    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))
    pass_rate = Column(DECIMAL(5, 2))
    record_date = Column(Date)
    updated_by = Column(String(20))
    updated_at = Column(DateTime)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255)) 
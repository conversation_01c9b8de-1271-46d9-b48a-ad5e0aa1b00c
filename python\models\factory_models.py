from sqlalchemy import Column, Integer, String, Date, DateTime, Numeric, Text
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class FactoryPersonnelInfo(Base):
    """全厂人员信息表"""
    __tablename__ = "ioc_qc_personnel_info"

    id = Column(Integer, primary_key=True, index=True)
    total_worker_count = Column(Integer)  # 普工人数
    total_clerical_count = Column(Integer)  # 职员人数
    on_duty_worker_count = Column(Integer)  # 普工出勤人数
    on_duty_clerical_count = Column(Integer)  # 职员出勤人数
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class WorkshopPersonnelInfo(Base):
    """车间人员信息表"""
    __tablename__ = "ioc_qc_workshop_personnel_info"

    id = Column(Integer, primary_key=True, index=True)
    total_worker_count = Column(Integer)  # 普工人数
    total_clerical_count = Column(Integer)  # 职员人数
    workshop_name = Column(String(20))  # 车间名称
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class NotificationInfo(Base):
    """通知信息表"""
    __tablename__ = "ioc_qc_notification_info"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text)  # 内容
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class EquipmentInfo(Base):
    """设备信息表"""
    __tablename__ = "ioc_qc_equipment_info"

    id = Column(Integer, primary_key=True, index=True)
    total_equipment_count = Column(Integer)  # 设备总数
    operating_equipment_count = Column(Integer)  # 运行设备数
    operating_rate = Column(Numeric(5, 2))  # 运行率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class WorkshopEquipmentStatus(Base):
    """车间设备状态表"""
    __tablename__ = "ioc_qc_workshop_equipment_status"

    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))  # 车间名称
    operating_rate = Column(Numeric(5, 2))  # 运行率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class ProjectClassification(Base):
    """项目分类表"""
    __tablename__ = "ioc_qc_project_classification"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    project_name = Column(String(50), comment="项目名称")
    mass_production_count = Column(Integer, comment="量产数量")
    development_count = Column(Integer, comment="开发数量")
    record_date = Column(String(20), comment="记录日期")  # 修改为 String 类型以支持年份格式
    updated_by = Column(String(50), comment="更新人")
    updated_at = Column(DateTime, default=datetime.utcnow, comment="更新时间")
    reserved_field1 = Column(String(50), comment="预留字段1")
    reserved_field2 = Column(String(50), comment="预留字段2")
    reserved_field3 = Column(String(50), comment="预留字段3")

class AnnualProjectCategory(Base):
    """年度项目分类表"""
    __tablename__ = "ioc_qc_annual_project_category"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    project_name = Column(String(50), comment="项目名称")
    project_count = Column(Integer, comment="项目数量")
    record_date = Column(String(20), comment="记录日期")  # 修改为 String 类型以支持年份格式
    updated_by = Column(String(50), comment="更新人")
    updated_at = Column(DateTime, default=datetime.utcnow, comment="更新时间")
    reserved_field1 = Column(String(50), comment="预留字段1")
    reserved_field2 = Column(String(50), comment="预留字段2")
    reserved_field3 = Column(String(50), comment="预留字段3")

class InjectionSamplingQualityRate(Base):
    """注塑抽检合格率表"""
    __tablename__ = "ioc_qc_injection_sampling_quality_rate"

    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))  # 车间名称
    quality_rate = Column(Numeric(5, 2))  # 合格率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class AsmFocInspectionPassRate(Base):
    """组装FOC检验合格率表"""
    __tablename__ = "ioc_asm_foc_inspection_pass_rate"

    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))  # 所属车间
    pass_rate = Column(Numeric(5, 2))  # 合格率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class CncInspectionPassRate(Base):
    """CNC巡检检验合格率表"""
    __tablename__ = "ioc_zzcj_cnc_inspection_pass_rate"

    id = Column(Integer, primary_key=True, index=True)
    pass_rate = Column(Numeric(5, 2))  # 合格率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class CoatingInspectionPassRate(Base):
    """涂装巡检检验合格率表"""
    __tablename__ = "ioc_qc_coating_inspection_pass_rate"

    id = Column(Integer, primary_key=True, index=True)
    pass_rate = Column(Numeric(5, 2))  # 合格率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class WorkOrderCloseRate6Months(Base):
    """近6个月工单关闭率表"""
    __tablename__ = "ioc_qc_work_order_close_rate_6months"

    id = Column(Integer, primary_key=True, index=True)
    workshop_name = Column(String(20))  # 车间名称
    completed_count = Column(Integer)  # 已完成数
    not_closed_count = Column(Integer)  # 未关闭数
    close_rate = Column(Numeric(5, 2))  # 工单关闭率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class ShipmentCount12Months(Base):
    """近12个月出货数量表"""
    __tablename__ = "ioc_qc_shipment_count_12months"

    id = Column(Integer, primary_key=True, index=True)
    shipment_count = Column(Numeric(10, 2))  # 出货量
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3

class MaterialPassRateLast6Months(Base):
    """近六个月品料合格率表"""
    __tablename__ = "ioc_qc_material_pass_rate_last_6_months"

    id = Column(Integer, primary_key=True, index=True)
    pass_rate = Column(Numeric(7, 4))  # 合格率
    record_date = Column(Date)  # 日期
    updated_by = Column(String(20))  # 更新人
    updated_at = Column(DateTime)  # 更新时间
    reserved_field1 = Column(String(255))  # 备用字段1
    reserved_field2 = Column(String(255))  # 备用字段2
    reserved_field3 = Column(String(255))  # 备用字段3 
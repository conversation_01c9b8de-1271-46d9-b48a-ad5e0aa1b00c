from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, text
from typing import List, Dict, Optional
from database import get_db
from models import dashboard_models
from schemas import dashboard_schemas
from schemas.common_schemas import Response, ListResponse, ErrorResponse
from datetime import datetime, date, timedelta

router = APIRouter(
    prefix="/dashboard",
    tags=["大屏展示"],
    responses={404: {"description": "未找到数据"}},
)

# 企业信息相关接口
@router.post("/company/", response_model=Response[dashboard_schemas.CompanyInfo],
    summary="创建企业信息",
    description="创建新的企业基本信息记录",
    response_description="返回创建的企业信息")
def create_company_info(info: dashboard_schemas.CompanyInfoCreate, db: Session = Depends(get_db)):
    """
    创建企业信息
    
    - **serial_num**: 序号
    - **company_info**: 企业信息内容
    - **record_date**: 记录日期
    - **updateman**: 更新人
    - **updatetime**: 更新时间
    """
    db_info = dashboard_models.CompanyInfo(**info.dict())
    db.add(db_info)
    db.commit()
    db.refresh(db_info)
    return Response(code=200, message="创建成功", data=db_info)

@router.get("/company/latest/", response_model=ListResponse[dashboard_schemas.CompanyInfo],
    summary="获取最新企业信息",
    description="获取按记录日期排序的最新企业信息列表",
    response_description="返回企业信息列表")
def read_latest_company_info(db: Session = Depends(get_db)):
    """获取最新的企业信息列表，按记录日期降序排序"""
    result = db.query(dashboard_models.CompanyInfo).order_by(
        dashboard_models.CompanyInfo.record_date.desc()
    ).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 设备相关接口
@router.post("/device/", response_model=Response[dashboard_schemas.Device],
    summary="创建设备信息",
    description="创建新的设备信息记录",
    response_description="返回创建的设备信息")
def create_device(device: dashboard_schemas.DeviceCreate, db: Session = Depends(get_db)):
    """
    创建设备信息
    
    - **device_type**: 设备类型
    - **device_num**: 设备编号
    - **record_date**: 记录日期
    - **updateman**: 更新人
    - **updatetime**: 更新时间
    """
    db_device = dashboard_models.Device(**device.dict())
    db.add(db_device)
    db.commit()
    db.refresh(db_device)
    return Response(code=200, message="创建成功", data=db_device)

@router.get("/device/", response_model=ListResponse[dashboard_schemas.Device],
    summary="获取设备列表",
    description="获取所有设备信息列表",
    response_description="返回设备信息列表")
def read_devices(db: Session = Depends(get_db)):
    """获取所有设备信息"""
    result = db.query(dashboard_models.Device).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 员工进出相关接口
@router.post("/employee-inout/", response_model=Response[dashboard_schemas.EmployeeInOut],
    summary="创建员工进出记录",
    description="创建新的员工进出记录",
    response_description="返回创建的员工进出记录")
def create_employee_inout(inout: dashboard_schemas.EmployeeInOutCreate, db: Session = Depends(get_db)):
    db_inout = dashboard_models.EmployeeInOut(**inout.dict())
    db.add(db_inout)
    db.commit()
    db.refresh(db_inout)
    return Response(code=200, message="创建成功", data=db_inout)

@router.get("/employee-inout/", response_model=ListResponse[dashboard_schemas.EmployeeInOut],
    summary="获取员工进出记录列表",
    description="获取所有员工进出记录",
    response_description="返回员工进出记录列表")
def read_employee_inout(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.EmployeeInOut).order_by(
        dashboard_models.EmployeeInOut.record_date.desc()
    ).limit(20).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 宿舍相关接口
@router.post("/hostel/", response_model=Response[dashboard_schemas.Hostel],
    summary="创建宿舍信息",
    description="创建新的宿舍信息记录",
    response_description="返回创建的宿舍信息")
def create_hostel(hostel: dashboard_schemas.HostelCreate, db: Session = Depends(get_db)):
    db_hostel = dashboard_models.Hostel(**hostel.dict())
    db.add(db_hostel)
    db.commit()
    db.refresh(db_hostel)
    return Response(code=200, message="创建成功", data=db_hostel)

@router.get("/hostel/latest/", response_model=Response[dashboard_schemas.Hostel],
    summary="获取最新宿舍信息",
    description="获取最新的宿舍使用情况记录",
    response_description="返回最新宿舍信息")
def read_latest_hostel(db: Session = Depends(get_db)):
    latest = db.query(dashboard_models.Hostel).order_by(
        dashboard_models.Hostel.record_date.desc()
    ).first()
    if latest is None:
        raise HTTPException(status_code=404, detail="没有找到宿舍信息")
    return Response(code=200, message="查询成功", data=latest)

# 宿舍文字介绍相关接口
@router.post("/hostel/info/", 
    response_model=Response[dashboard_schemas.HostelInfo],
    summary="创建宿舍文字介绍",
    description="创建新的宿舍文字介绍记录",
    response_description="返回创建的宿舍文字介绍")
def create_hostel_info(hostel: dashboard_schemas.HostelInfoCreate, db: Session = Depends(get_db)):
    """
    创建宿舍文字介绍
    
    参数说明：
    - **hostel_info**: 文字介绍
    - **record_date**: 记录日期
    - **updateman**: 更新人
    - **updatetime**: 更新时间
    - **reserved_field1**: 备用字段1
    - **reserved_field2**: 备用字段2
    - **reserved_field3**: 备用字段3
    """
    try:
        db_hostel = dashboard_models.HostelInfo(**hostel.dict())
        db.add(db_hostel)
        db.commit()
        db.refresh(db_hostel)
        return Response(data=db_hostel)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/hostel/info/latest", 
    response_model=ListResponse[dashboard_schemas.HostelInfo],
    summary="获取宿舍文字介绍",
    description="获取所有宿舍文字介绍记录",
    response_description="返回宿舍文字介绍列表")
def read_latest_hostel_info(db: Session = Depends(get_db)):
    """获取所有宿舍文字介绍列表"""
    result = db.query(dashboard_models.HostelInfo).order_by(
        dashboard_models.HostelInfo.record_date.desc()
    ).all()
    
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

@router.get("/hostel/info", 
    response_model=ListResponse[dashboard_schemas.HostelInfo],
    summary="获取宿舍文字介绍列表",
    description="获取所有宿舍文字介绍记录，支持日期范围过滤",
    response_description="返回宿舍文字介绍列表")
def get_hostel_info(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """
    获取宿舍文字介绍列表
    
    参数说明：
    - **start_date**: 开始日期（可选）
    - **end_date**: 结束日期（可选）
    """
    query = db.query(dashboard_models.HostelInfo)
    
    if start_date:
        query = query.filter(dashboard_models.HostelInfo.record_date >= start_date)
    if end_date:
        query = query.filter(dashboard_models.HostelInfo.record_date <= end_date)
        
    result = query.order_by(dashboard_models.HostelInfo.record_date.desc()).all()
    total = len(result)
    
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 园区建设相关接口
@router.post("/park-construct/", response_model=Response[dashboard_schemas.ParkConstruct],
    summary="创建园区建设信息",
    description="创建新的园区建设信息记录",
    response_description="返回创建的园区建设信息")
def create_park_construct(construct: dashboard_schemas.ParkConstructCreate, db: Session = Depends(get_db)):
    db_construct = dashboard_models.ParkConstruct(**construct.dict())
    db.add(db_construct)
    db.commit()
    db.refresh(db_construct)
    return Response(code=200, message="创建成功", data=db_construct)

@router.get("/park-construct/", response_model=ListResponse[dashboard_schemas.ParkConstruct],
    summary="获取园区建设信息列表",
    description="获取所有园区建设信息",
    response_description="返回园区建设信息列表")
def read_park_constructs(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.ParkConstruct).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 园区信息相关接口
@router.post("/park-info/", response_model=Response[dashboard_schemas.ParkInfo],
    summary="创建园区信息",
    description="创建新的园区信息记录",
    response_description="返回创建的园区信息")
def create_park_info(info: dashboard_schemas.ParkInfoCreate, db: Session = Depends(get_db)):
    db_info = dashboard_models.ParkInfo(**info.dict())
    db.add(db_info)
    db.commit()
    db.refresh(db_info)
    return Response(code=200, message="创建成功", data=db_info)

@router.get("/park-info/", response_model=ListResponse[dashboard_schemas.ParkInfo],
    summary="获取园区信息列表",
    description="获取所有园区信息",
    response_description="返回园区信息列表")
def read_park_info(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.ParkInfo).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 单间相关接口
@router.post("/private-room/", response_model=Response[dashboard_schemas.PrivateRoom],
    summary="创建单间信息",
    description="创建新的单间信息记录",
    response_description="返回创建的单间信息")
def create_private_room(room: dashboard_schemas.PrivateRoomCreate, db: Session = Depends(get_db)):
    db_room = dashboard_models.PrivateRoom(**room.dict())
    db.add(db_room)
    db.commit()
    db.refresh(db_room)
    return Response(code=200, message="创建成功", data=db_room)

@router.get("/private-room/latest/", response_model=Response[dashboard_schemas.PrivateRoom],
    summary="获取最新单间信息",
    description="获取最新的单间使用情况记录",
    response_description="返回最新单间信息")
def read_latest_private_room(db: Session = Depends(get_db)):
    latest = db.query(dashboard_models.PrivateRoom).order_by(
        dashboard_models.PrivateRoom.id.desc()
    ).first()
    if latest is None:
        raise HTTPException(status_code=404, detail="没有找到单间信息")
    return Response(code=200, message="查询成功", data=latest)

# 实时停车相关接口
@router.post("/real-parking/", response_model=Response[dashboard_schemas.RealParking],
    summary="创建实时停车信息",
    description="创建新的实时停车信息记录",
    response_description="返回创建的实时停车信息")
def create_real_parking(parking: dashboard_schemas.RealParkingCreate, db: Session = Depends(get_db)):
    db_parking = dashboard_models.RealParking(**parking.dict())
    db.add(db_parking)
    db.commit()
    db.refresh(db_parking)
    return Response(code=200, message="创建成功", data=db_parking)

@router.get("/real-parking/", response_model=ListResponse[dashboard_schemas.RealParking],
    summary="获取实时停车信息列表",
    description="获取所有实时停车信息",
    response_description="返回实时停车信息列表")
def read_real_parking(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.RealParking).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 技术人员相关接口
@router.post("/technical/", response_model=Response[dashboard_schemas.Technical],
    summary="创建技术人员信息",
    description="创建新的技术人员信息记录",
    response_description="返回创建的技术人员信息")
def create_technical(technical: dashboard_schemas.TechnicalCreate, db: Session = Depends(get_db)):
    db_technical = dashboard_models.Technical(**technical.dict())
    db.add(db_technical)
    db.commit()
    db.refresh(db_technical)
    return Response(code=200, message="创建成功", data=db_technical)

@router.get("/technical/", response_model=ListResponse[dashboard_schemas.Technical],
    summary="获取技术人员信息列表",
    description="获取所有技术人员信息",
    response_description="返回技术人员信息列表")
def read_technical(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.Technical).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 技术人员学历相关接口
@router.post("/technical-edu/", response_model=Response[dashboard_schemas.TechnicalEdu],
    summary="创建技术人员学历信息",
    description="创建新的技术人员学历信息记录",
    response_description="返回创建的技术人员学历信息")
def create_technical_edu(edu: dashboard_schemas.TechnicalEduCreate, db: Session = Depends(get_db)):
    db_edu = dashboard_models.TechnicalEdu(**edu.dict())
    db.add(db_edu)
    db.commit()
    db.refresh(db_edu)
    return Response(code=200, message="创建成功", data=db_edu)

@router.get("/technical-edu/", response_model=ListResponse[dashboard_schemas.TechnicalEdu],
    summary="获取技术人员学历信息列表",
    description="获取所有技术人员学历信息",
    response_description="返回技术人员学历信息列表")
def read_technical_edu(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.TechnicalEdu).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 技术专利相关接口
@router.post("/technical-patent/", response_model=Response[dashboard_schemas.TechnicalPatent],
    summary="创建技术专利信息",
    description="创建新的技术专利信息记录",
    response_description="返回创建的技术专利信息")
def create_technical_patent(patent: dashboard_schemas.TechnicalPatentCreate, db: Session = Depends(get_db)):
    db_patent = dashboard_models.TechnicalPatent(**patent.dict())
    db.add(db_patent)
    db.commit()
    db.refresh(db_patent)
    return Response(code=200, message="创建成功", data=db_patent)

@router.get("/technical-patent/", response_model=ListResponse[dashboard_schemas.TechnicalPatent],
    summary="获取技术专利信息列表",
    description="获取所有技术专利信息",
    response_description="返回技术专利信息列表")
def read_technical_patent(db: Session = Depends(get_db)):
    result = db.query(dashboard_models.TechnicalPatent).order_by(
        dashboard_models.TechnicalPatent.patent_year.asc()
    ).all()
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))

# 统计相关接口

@router.get("/statistics/parking/", response_model=Response[Dict],
    summary="获取停车场使用统计",
    description="获取停车场使用情况的统计数据，包括总车位数、已使用数量、剩余数量和使用率",
    response_description="返回停车场使用统计数据")
def get_parking_statistics(db: Session = Depends(get_db)):
    """
    获取停车场使用统计数据
    
    返回数据包括：
    - **parking_addr**: 停车场地址
    - **total_parking**: 总车位数
    - **total_used**: 已使用车位数
    - **total_remaining**: 剩余车位数
    - **usage_rate**: 使用率（百分比）
    """
    # 简化后的SQL查询
    sql = """
    SELECT parking_addr, parking_num AS total_parking, parking_use AS total_used, parking_remaining AS total_remaining,
           CASE WHEN parking_num > 0 THEN ROUND(parking_use::decimal / parking_num * 100, 2) ELSE 0 END AS usage_rate
    FROM ioc_dp_realparking
    WHERE id IN (
        SELECT max(id)
        FROM ioc_dp_realparking
        GROUP BY parking_addr
    )
    UNION ALL
    SELECT '总车位' AS parking_addr,
        SUM(parking_num) AS total_parking,
        SUM(parking_use) AS total_used,
        SUM(parking_remaining) AS total_remaining,
        CASE WHEN SUM(parking_num) > 0 THEN ROUND(SUM(parking_use)::decimal / SUM(parking_num) * 100, 2) ELSE 0 END AS usage_rate
    FROM ioc_dp_realparking
    WHERE id IN (
        SELECT max(id)
        FROM ioc_dp_realparking
        GROUP BY parking_addr
    )
    """
    result = db.execute(text(sql)).fetchall()

    # 将查询结果转换为字典列表
    data = []
    for row in result:
        data.append({
            "parking_addr": row.parking_addr,
            "total_parking": row.total_parking,
            "total_used": row.total_used,
            "total_remaining": row.total_remaining,
            "usage_rate": row.usage_rate
        })

    return Response(code=200, message="查询成功", data=data)


@router.get("/statistics/technical/education/", response_model=Response[Dict],
    summary="获取技术人员学历分布",
    description="获取技术人员的学历分布统计数据",
    response_description="返回各学历层次的人数统计")
def get_technical_education_statistics(db: Session = Depends(get_db)):
    """
    获取技术人员学历分布统计
    
    返回数据格式：
    {
        "学历类型": 人数,
        ...
    }
    """
    result = db.query(
        dashboard_models.TechnicalEdu.edu_background,
        func.sum(dashboard_models.TechnicalEdu.edu_num).label('total')
    ).group_by(dashboard_models.TechnicalEdu.edu_background).all()
    
    data = {item[0]: item[1] for item in result}
    return Response(code=200, message="查询成功", data=data)

@router.get("/statistics/technical/patent/trend/", response_model=Response[Dict],
    summary="获取技术专利趋势",
    description="获取技术专利数量的年度趋势数据",
    response_description="返回各年度的专利数量统计")
def get_technical_patent_trend(db: Session = Depends(get_db)):
    """获取技术专利趋势统计"""
    result = db.query(
        dashboard_models.TechnicalPatent.patent_year,
        func.sum(dashboard_models.TechnicalPatent.patent_num).label('total')
    ).group_by(dashboard_models.TechnicalPatent.patent_year)\
     .order_by(dashboard_models.TechnicalPatent.patent_year.asc()).all()
    
    data = {item[0]: item[1] for item in result}
    return Response(code=200, message="查询成功", data=data)

@router.get("/statistics/employee/inout/today/", response_model=Response[Dict],
    summary="获取今日员工进出统计",
    description="获取今日员工进出园区的统计数据",
    response_description="返回今日进出人数统计")
def get_employee_inout_today(db: Session = Depends(get_db)):
    """获取今日员工进出统计"""
    today = date.today()
    result = db.query(
        dashboard_models.EmployeeInOut.employee_state,
        func.count(dashboard_models.EmployeeInOut.id).label('count')
    ).filter(dashboard_models.EmployeeInOut.record_date == today)\
     .group_by(dashboard_models.EmployeeInOut.employee_state).all()
    
    data = {item[0]: item[1] for item in result}
    return Response(code=200, message="查询成功", data=data)

@router.get("/statistics/device/type/", response_model=Response[Dict],
    summary="获取设备类型统计",
    description="获取各类型设备的数量统计",
    response_description="返回各类型设备数量")
def get_device_type_statistics(db: Session = Depends(get_db)):
    """获取设备类型统计"""
    result = db.query(
        dashboard_models.Device.device_type,
        func.count(dashboard_models.Device.id).label('count')
    ).group_by(dashboard_models.Device.device_type).all()
    
    data = {item[0]: item[1] for item in result}
    return Response(code=200, message="查询成功", data=data)

@router.get("/statistics/technical/distribution/", response_model=Response[Dict],
    summary="获取技术人员分布统计",
    description="获取技术人员在各部门/类型的分布统计",
    response_description="返回技术人员分布数据")
def get_technical_distribution(db: Session = Depends(get_db)):
    """获取技术人员分布统计"""
    result = db.query(
        dashboard_models.Technical.personnel_type,
        func.sum(dashboard_models.Technical.personnel_num).label('total')
    ).group_by(dashboard_models.Technical.personnel_type).all()
    
    data = {item[0]: item[1] for item in result}
    return Response(code=200, message="查询成功", data=data)

@router.get("/overview/", response_model=Response[dashboard_schemas.DashboardOverview],
    summary="获取大屏总览数据",
    description="获取大屏展示所需的所有概览数据，包括园区信息、企业信息、技术人员统计等",
    response_description="返回大屏总览数据")
def get_dashboard_overview(db: Session = Depends(get_db)):
    """
    获取大屏总览数据
    
    返回数据包括：
    - **park_info**: 园区信息（类型和数量）
    - **company_info**: 企业信息（序号和详情）
    - **total_technical**: 技术人员总数
    - **total_patents**: 专利总数
    - **today_inout**: 今日进出人数
    """
    # 获取最新的园区信息
    latest_park_info = db.query(dashboard_models.ParkInfo).order_by(
        dashboard_models.ParkInfo.record_date.desc()
    ).first()
    
    # 获取最新的企业信息
    latest_company_info = db.query(dashboard_models.CompanyInfo).order_by(
        dashboard_models.CompanyInfo.record_date.desc()
    ).first()
    
    # 获取技术人员总数
    total_technical = db.query(func.sum(dashboard_models.Technical.personnel_num)).scalar() or 0
    
    # 获取专利总数
    total_patents = db.query(func.sum(dashboard_models.TechnicalPatent.patent_num)).scalar() or 0
    
    # 获取今日进出人数
    today = date.today()
    today_inout = db.query(func.count(dashboard_models.EmployeeInOut.id))\
        .filter(dashboard_models.EmployeeInOut.record_date == today).scalar() or 0
    
    data = {
        "park_info": {
            "type": latest_park_info.type if latest_park_info else None,
            "num": latest_park_info.num if latest_park_info else 0
        } if latest_park_info else None,
        "company_info": {
            "serial_num": latest_company_info.serial_num,
            "info": latest_company_info.company_info
        } if latest_company_info else None,
        "total_technical": total_technical,
        "total_patents": total_patents,
        "today_inout": today_inout
    }
    return Response(code=200, message="查询成功", data=data)

@router.get("/statistics/venues/", response_model=Response[Dict],
    summary="获取娱乐会议场所统计",
    description="获取娱乐会议场所的统计数据，包括各类场所的数量统计",
    response_description="返回各类场所数量统计")
def get_venues_statistics(db: Session = Depends(get_db)):
    """获取娱乐会议场所统计"""
    
    subquery = db.query(
        func.max(dashboard_models.Venues.id).label('max_id')
    ).group_by(dashboard_models.Venues.venues_type).subquery()
    
    result = db.query(
        dashboard_models.Venues.venues_type,
        dashboard_models.Venues.venues_num.label('total'),
        dashboard_models.Venues.venues_remaining.label('remaining')
    ).join(subquery, dashboard_models.Venues.id == subquery.c.max_id).all()
    
    # 转换结果为字典格式
    data = {
        "statistics": [
            {
                "type": row.venues_type,
                "total": row.total,
                "remaining": row.remaining
            }
            for row in result
        ]
    }
    
    return Response(
        code=200,
        msg="获取娱乐会议场所统计成功",
        data=data
    )


@router.get("/statistics/staff-visitor/", response_model=Response[Dict],
    summary="获取员工访客统计",
    description="获取员工访客情况的统计数据，包括各类访客的数量统计",
    response_description="返回各类访客数量统计")
def get_staff_visitor_statistics(db: Session = Depends(get_db)):
    """获取员工访客统计"""
    try:
        # 使用指定的SQL查询
        sql = """
        SELECT * FROM ioc_dp_staff_visitor WHERE id IN (
            SELECT MAX(id) FROM ioc_dp_staff_visitor WHERE type = '在园员工'
            UNION ALL
            SELECT MAX(id) FROM ioc_dp_staff_visitor WHERE type = '访客数'
            UNION ALL
            SELECT MAX(id) FROM ioc_dp_staff_visitor WHERE type = '离园员工'
        )
        """
        
        result = db.execute(text(sql)).all()
        
        # 转换结果为字典格式
        statistics = []
        for row in result:
            # 将行转换为字典
            row_dict = {column: value for column, value in zip(
                ['id', 'type', 'num', 'record_date', 'updateman', 'updatetime', 
                 'reserved_field1', 'reserved_field2', 'reserved_field3'], 
                row
            )}
            
            statistics.append({
                "type": row_dict['type'],
                "total": row_dict['num']
            })
        
        data = {"statistics": statistics}
        
        return Response(
            code=200,
            msg="获取员工访客统计成功",
            data=data
        )
    except Exception as e:
        logger.error(f"获取员工访客统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
        
@router.get("/park/brief/", 
    response_model=ListResponse[dashboard_schemas.ParkBriefInfo],
    summary="获取园区简介",
    description="获取园区简介信息，包括基本信息、园区人数和安全运营天数",
    response_description="返回园区简介信息列表")
def get_park_brief(db: Session = Depends(get_db)):
    """获取园区简介信息"""
    sql = """
    select id,type,num,reserved_field1
    from ioc_dp_parkinfo where type not in ('园区人数','安全运营天数')
    union all
    select 4 as id,'园区人数' as type,sum(num) as num,'人' as reserved_field1 
    from ioc_dp_staff_visitor 
    where type != '离园员工' 
    and id = (select max(id) from ioc_dp_staff_visitor)
    union all
    SELECT 5 as id, '安全运营天数' as type,
           CURRENT_DATE-to_date(reserved_field1,'YYYY-MM-DD') as num,
           '天' as reserved_field1
    FROM ioc_dp_parkinfo where type='安全运营天数'
    """
    
    try:
        result = db.execute(text(sql)).all()
        # 将结果转换为字典列表
        data = [
            {
                "id": row[0],
                "type": row[1],
                "num": float(row[2]) if row[2] is not None else 0.0,
                "reserved_field1": row[3]
            }
            for row in result
        ]
        
        return ListResponse(
            data=data,
            total=len(data),
            message=f"成功获取{len(data)}条记录",
            success=True
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
        
@router.get("/personnel-distribution/latest/", 
    response_model=ListResponse[dashboard_schemas.PersonnelDistribution],
    summary="获取最新厂区人员分布数据",
    description="获取record_date最新日期的所有厂区人员分布数据",
    response_description="返回厂区人员分布数据列表")
def get_latest_personnel_distribution(db: Session = Depends(get_db)):
    """
    获取最新日期的厂区人员分布数据
    
    返回record_date字段值最大（最新）的所有厂区人员分布数据
    """
    # 查询最大日期
    latest_date = db.query(func.max(dashboard_models.PersonnelDistribution.record_date)).scalar()
    
    if not latest_date:
        return ListResponse(code=200, message="暂无数据", data=[], total=0)
    
    # 查询最新日期的所有数据
    result = db.query(dashboard_models.PersonnelDistribution).filter(
        dashboard_models.PersonnelDistribution.record_date == latest_date
    ).all()
    
    return ListResponse(code=200, message="查询成功", data=result, total=len(result))
        
@router.post("/personnel-distribution/", 
    response_model=Response[dashboard_schemas.PersonnelDistribution],
    summary="创建厂区人员分布数据",
    description="创建新的厂区人员分布数据记录",
    response_description="返回创建的厂区人员分布数据")
def create_personnel_distribution(
    data: dashboard_schemas.PersonnelDistributionCreate, 
    db: Session = Depends(get_db)
):
    """
    创建厂区人员分布数据
    
    - **park_name**: 园区名称
    - **total_num**: 园区总人数
    - **manager_num**: 主管人数
    - **employee_num**: 职员人数
    - **general_worker_num**: 普工人数
    - **record_date**: 记录日期
    - **updateman**: 更新人
    - **updatetime**: 更新时间
    """
    # 如果没有提供更新时间，则使用当前时间
    if not data.updatetime:
        data.updatetime = datetime.now()
        
    db_data = dashboard_models.PersonnelDistribution(**data.dict())
    db.add(db_data)
    db.commit()
    db.refresh(db_data)
    return Response(code=200, message="创建成功", data=db_data)
        
@router.get("/visitor/", 
    response_model=ListResponse[dashboard_schemas.Visitor],
    summary="获取访客信息列表",
    description="获取所有访客信息记录，支持日期范围过滤",
    response_description="返回访客信息列表")
def get_visitor_list(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """
    获取访客信息列表
    
    参数说明：
    - **start_date**: 开始日期（可选）
    - **end_date**: 结束日期（可选）
    """
    query = db.query(dashboard_models.Visitor)
    
    if start_date:
        query = query.filter(dashboard_models.Visitor.record_date >= start_date)
    if end_date:
        query = query.filter(dashboard_models.Visitor.record_date <= end_date)
        
    result = query.order_by(dashboard_models.Visitor.record_date.desc()).all()
    total = len(result)
    
    # 转换结果，使用 schema 进行字段映射
    mapped_result = []
    for item in result:
        mapped_item = {
            "id": item.id,
            "visitor_type": item.visitor_type,
            "visitor_date": item.visitor_date,
            "visitor_num": item.visitor_num,
            "record_date": item.record_date,
            "updateman": item.updateman,
            "updatetime": item.updatetime,
            "reserved_field1": item.reserved_field1,
            "reserved_field2": item.reserved_field2,
            "reserved_field3": item.reserved_field3
        }
        mapped_result.append(dashboard_schemas.Visitor(**mapped_item))
    
    return ListResponse(
        data=mapped_result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )
        

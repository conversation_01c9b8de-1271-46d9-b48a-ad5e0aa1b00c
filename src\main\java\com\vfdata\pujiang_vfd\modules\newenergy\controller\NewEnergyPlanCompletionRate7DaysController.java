package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyPlanCompletionRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/plan-completion-rate-7days")
@RequiredArgsConstructor
public class NewEnergyPlanCompletionRate7DaysController {

    private final NewEnergyPlanCompletionRate7DaysService planCompletionRate7DaysService;

    @GetMapping
    @Operation(summary = "获取计划达成率近7天数据", description = "根据车间名称获取该车间最新7条计划达成率数据")
    public ResponseUtils.Result<List<NewEnergyPlanCompletionRate7DaysDTO>> getPlanCompletionRate7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam("workshop_name") String workshopName) {
        try {
            if (workshopName == null || workshopName.trim().isEmpty()) {
                return ResponseUtils.error("车间名称不能为空");
            }
            
            List<NewEnergyPlanCompletionRate7DaysDTO> data = planCompletionRate7DaysService.getPlanCompletionRate7DaysByWorkshop(workshopName.trim());
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取计划达成率近7天数据失败：" + e.getMessage());
        }
    }
}

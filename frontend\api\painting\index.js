import request from '@/utils/request'

/**
 * 获取喷涂车间人员信息
 * @returns {Promise} 返回人员信息数据
 */
export const getPersonnelInfo = async () => {
  return await request.get('/painting/personnel-info/latest')
}

/**
 * 获取喷涂车间设备信息
 * @returns {Promise} 返回设备信息数据
 */
export const getEquipmentInfo = async () => {
  return await request.get('/painting/equipment-info/latest')
}

/**
 * 获取喷涂车间出勤率信息
 * @returns {Promise} 返回出勤率数据
 */
export const getAttendanceRate = async () => {
  return await request.get('/painting/attendance-rate/latest')
}

/**
 * 获取喷涂车间环境信息
 * @returns {Promise} 返回环境信息数据
 */
export const getEnvironmentInfo = async () => {
  return await request.get('/painting/environment-info/latest')
}

/**
 * 获取喷涂车间当日计划达成率
 * @param {string} workshopName - 车间名称（可选）
 * @returns {Promise} 返回计划达成率数据
 */
export const getDailyAchievementRate = async (workshopName = '') => {
  const params = workshopName ? { workshop_name: workshopName } : {}
  return await request.get('/painting/daily-achievement-rate', { params })
}

/**
 * 获取喷涂车间品质检验良率
 * @param {string} workshopName - 车间名称（可选）
 * @returns {Promise} 返回品质检验良率数据
 */
export const getQualityRate = async (workshopName = '') => {
  const params = workshopName ? { workshop_name: workshopName } : {}
  return await request.get('/painting/quality-rate', { params })
}

/**
 * 获取喷涂车间油漆用量信息
 * @param {string} workshopName - 车间名称（可选）
 * @returns {Promise} 返回油漆用量数据
 */
export const getPaintUsageInfo = async (workshopName = '') => {
  const params = workshopName ? { workshop_name: workshopName } : {}
  return await request.get('/painting/paint-usage/latest', { params })
}

/**
 * 获取喷涂车间计划达成率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天计划达成率数据
 */
export const getPlanCompletionRate7Days = async (workshopName) => {
  return await request.get('/painting/plan-completion-rate-7days', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取喷涂车间品质检验良率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天品质检验良率数据
 */
export const getQualityRate7Days = async (workshopName) => {
  return await request.get('/painting/quality-rate-7days', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取喷涂车间设备状态分布
 * @param {string} workshopName - 车间名称（可选）
 * @returns {Promise} 返回设备状态分布数据
 */
export const getEquipmentStatus = async (workshopName = '') => {
  const params = workshopName ? { workshop_name: workshopName } : {}
  return await request.get('/painting/equipment-status', { params })
}

/**
 * 获取喷涂车间项目状态
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间项目状态数据
 */
export const getProjectStatus = async (workshopName) => {
  return await request.get('/painting/project-status', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取喷涂车间品质不良分类
 * @param {string} workshopName - 车间名称（可选）
 * @returns {Promise} 返回品质不良分类数据
 */
export const getDefectClassification = async (workshopName = '') => {
  const params = workshopName ? { workshop_name: workshopName } : {}
  return await request.get('/painting/defect-classification', { params })
}

/**
 * 获取喷涂车间RTO运行状态
 * @returns {Promise} 返回RTO运行状态数据
 */
export const getRtoStatus = async () => {
  return await request.get('/painting/rto-status/latest')
}

/**
 * 获取喷涂车间设备状况
 * @returns {Promise} 返回每个车间最新的设备状况数据
 */
export const getWorkshopStatus = async () => {
  return await request.get('/painting/workshop-status/latest')
}

/**
 * 喷涂车间相关API集合
 */
export default {
  // 基础信息接口（返回所有车间或单条数据）
  getPersonnelInfo,
  getEquipmentInfo,
  getAttendanceRate,
  getEnvironmentInfo,
  getRtoStatus,
  getWorkshopStatus,
  
  // 可选车间参数的接口
  getDailyAchievementRate,
  getQualityRate,
  getPaintUsageInfo,
  getEquipmentStatus,
  getDefectClassification,
  
  // 需要车间参数的接口
  getPlanCompletionRate7Days,
  getQualityRate7Days,
  getProjectStatus
}

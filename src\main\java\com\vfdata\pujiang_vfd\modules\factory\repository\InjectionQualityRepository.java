package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.InjectionQuality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface InjectionQualityRepository extends JpaRepository<InjectionQuality, Long> {

    /**
     * 根据车间名称查询注塑抽检合格率
     */
    List<InjectionQuality> findByWorkshopName(String workshopName);

    /**
     * 根据车间名称和日期范围查询注塑抽检合格率
     */
    List<InjectionQuality> findByWorkshopNameAndRecordDateBetween(
            String workshopName, LocalDate startDate, LocalDate endDate);

    /**
     * 根据日期范围查询注塑抽检合格率
     */
    List<InjectionQuality> findByRecordDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 获取最新的注塑抽检合格率
     */
    @Query("SELECT i FROM InjectionQuality i WHERE i.recordDate = (SELECT MAX(i2.recordDate) FROM InjectionQuality i2)")
    List<InjectionQuality> findLatestQualityInfo();

    @Query("SELECT i FROM InjectionQuality i WHERE i.workshopName = :workshopName AND i.recordDate BETWEEN :startDate AND :endDate ORDER BY i.recordDate DESC")
    List<InjectionQuality> findByWorkshopNameAndDateRange(
            @Param("workshopName") String workshopName,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    @Query("SELECT i FROM InjectionQuality i WHERE i.recordDate BETWEEN :startDate AND :endDate ORDER BY i.recordDate DESC")
    List<InjectionQuality> findByDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    @Query("SELECT i FROM InjectionQuality i WHERE i.id IN " +
           "(SELECT MAX(i2.id) FROM InjectionQuality i2 GROUP BY i2.workshopName)")
    List<InjectionQuality> findLatestByWorkshopName();
} 
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class AssemblyBase(BaseModel):
    """组装车间基础模型"""
    id: Optional[int] = None
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    class Config:
        from_attributes = True

# 生产进度模型
class ProductionProgressBase(AssemblyBase):
    """生产进度基础模型"""
    product_name: str
    planned_quantity: int
    completed_quantity: int
    completion_rate: float

class ProductionProgressCreate(ProductionProgressBase):
    """创建生产进度模型"""
    pass

class ProductionProgress(ProductionProgressBase):
    """生产进度模型"""
    id: int

# 装配线状态模型
class LineStatusBase(AssemblyBase):
    """装配线状态基础模型"""
    line_name: str
    status: str
    efficiency: float

class LineStatusCreate(LineStatusBase):
    """创建装配线状态模型"""
    pass

class LineStatus(LineStatusBase):
    """装配线状态模型"""
    id: int

# 质量检验模型
class QualityInspectionBase(AssemblyBase):
    """质量检验基础模型"""
    product_name: str
    batch_number: str
    inspection_quantity: int
    defect_quantity: int
    pass_rate: float

class QualityInspectionCreate(QualityInspectionBase):
    """创建质量检验模型"""
    pass

class QualityInspection(QualityInspectionBase):
    """质量检验模型"""
    id: int

# 人员效率模型
class StaffEfficiencyBase(AssemblyBase):
    """人员效率基础模型"""
    staff_name: str
    position: str
    output_quantity: int
    working_hours: float
    efficiency_rate: float

class StaffEfficiencyCreate(StaffEfficiencyBase):
    """创建人员效率模型"""
    pass

class StaffEfficiency(StaffEfficiencyBase):
    """人员效率模型"""
    id: int

# 人员信息模型
class PersonnelCount(AssemblyBase):
    """人员信息模型"""
    personnel_type: Optional[str] = None
    count: Optional[int] = None
    general_worker_count: Optional[int] = None

class AssemblyPersonnelInfoBase(AssemblyBase):
    """人员信息基础模型"""
    personnel_type: str
    staff_count: int
    general_worker_count: int

class AssemblyPersonnelInfoCreate(AssemblyPersonnelInfoBase):
    """创建人员信息模型"""
    pass

class AssemblyPersonnelInfo(AssemblyPersonnelInfoBase):
    """人员信息模型"""
    id: int

# 出勤率模型
class AttendanceRate(AssemblyBase):
    """出勤率模型"""
    clerk_attendance_rate: Optional[float] = None
    worker_attendance_rate: Optional[float] = None

class AssemblyAttendanceRateBase(AssemblyBase):
    """出勤率基础模型"""
    clerk_attendance_rate: float
    worker_attendance_rate: float

class AssemblyAttendanceRateCreate(AssemblyAttendanceRateBase):
    """创建出勤率模型"""
    pass

class AssemblyAttendanceRate(AssemblyAttendanceRateBase):
    """出勤率模型"""
    id: int

# FQC质量模型
class FQCQualityRate(AssemblyBase):
    """FQC合格率模型"""
    workshop_name: Optional[str] = None
    quality_rate: Optional[float] = None

class AssemblyFQCQualityRateBase(AssemblyBase):
    """FQC质量基础模型"""
    workshop_name: str
    quality_rate: float

class AssemblyFQCQualityRateCreate(AssemblyFQCQualityRateBase):
    """创建FQC质量模型"""
    pass

class AssemblyFQCQualityRate(AssemblyFQCQualityRateBase):
    """FQC质量模型"""
    id: int

# FQC 7天质量趋势模型
class AssemblyFQCQualityRate7DaysBase(AssemblyBase):
    """FQC 7天质量趋势基础模型"""
    workshop_name: str
    quality_rate: float

class AssemblyFQCQualityRate7DaysCreate(AssemblyFQCQualityRate7DaysBase):
    """创建FQC 7天质量趋势模型"""
    pass

class AssemblyFQCQualityRate7Days(AssemblyFQCQualityRate7DaysBase):
    """FQC 7天质量趋势模型"""
    id: int

# 环境信息模型
class EnvironmentInfo(AssemblyBase):
    """环境信息模型"""
    dust_free_workshop_level: Optional[str] = None
    average_humidity: Optional[float] = None
    average_temperature: Optional[float] = None
    workshop_name: Optional[str] = None

class AssemblyEnvironmentInfoBase(AssemblyBase):
    """环境信息基础模型"""
    dust_free_workshop_level: str
    average_humidity: float
    average_temperature: float
    workshop_name: str

class AssemblyEnvironmentInfoCreate(AssemblyEnvironmentInfoBase):
    """创建环境信息模型"""
    pass

class AssemblyEnvironmentInfo(AssemblyEnvironmentInfoBase):
    """环境信息模型"""
    id: int

# 设备信息模型
class EquipmentInfo(AssemblyBase):
    """设备信息模型"""
    total_equipment_count: Optional[int] = None
    operating_equipment_count: Optional[int] = None
    equipment_overall_efficiency: Optional[float] = None
    operating_rate: Optional[float] = None

# 车间设备状态模型
class WorkshopStatus(AssemblyBase):
    """车间设备状态模型"""
    workshop_name: Optional[str] = None
    total_equipment: Optional[int] = None
    operating_equipment: Optional[int] = None
    operating_rate: Optional[float] = None

# 当天计划达成率模型
class DailyAchievementRate(AssemblyBase):
    """当天计划达成率模型"""
    workshop_name: Optional[str] = None
    planned_quantity: Optional[int] = None
    actual_quantity: Optional[int] = None
    achievement_rate: Optional[float] = None

# 7天计划达成率模型
class PlanCompletionRate7Days(AssemblyBase):
    """7天计划达成率模型"""
    workshop_name: Optional[str] = None
    completion_rate: Optional[float] = None

# 项目状态模型
class ProjectStatus(AssemblyBase):
    """项目状态模型"""
    workshop_name: Optional[str] = None
    project_name: Optional[str] = None
    product_name: Optional[str] = None
    machine_id: Optional[str] = None

# FQC不良类型分析模型
class FQCDefectAnalysis(AssemblyBase):
    """FQC不良类型分析模型"""
    workshop_name: Optional[str] = None
    defect_type_name: Optional[str] = None
    defect_type_count: Optional[int] = None

# 测试不良分类模型
class TestDefectClassification(AssemblyBase):
    """测试不良分类模型"""
    workshop_name: Optional[str] = None
    classification_name: Optional[str] = None
    defect_count: Optional[int] = None

# 工序良率信息模型
class ProcessYieldInfo(AssemblyBase):
    """工序良率信息模型"""
    workshop_name: Optional[str] = None
    process_yield: Optional[float] = None 
package com.vfdata.pujiang_vfd.sync.mapper.safety;

import com.vfdata.pujiang_vfd.modules.safety.entity.FireEquipmentInfo;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class FireEquipmentInfoMapper implements DataMapper<FireEquipmentInfo> {

    @Override
    public FireEquipmentInfo mapToEntity(Map<String, Object> sourceData) {
        try {
            FireEquipmentInfo entity = new FireEquipmentInfo();

            // 映射基础字段
            entity.setId(DateTimeParseUtil.getLongValue(sourceData, "id"));
            entity.setFireExtinguisher(DateTimeParseUtil.getStringValue(sourceData, "fireExtinguisher"));
            entity.setFireSprinkler(DateTimeParseUtil.getStringValue(sourceData, "fireSprinkler"));
            entity.setFireHydrant(DateTimeParseUtil.getStringValue(sourceData, "fireHydrant"));
            entity.setFireAlarmDevice(DateTimeParseUtil.getStringValue(sourceData, "fireAlarmDevice"));
            entity.setUpdatedBy(DateTimeParseUtil.getStringValue(sourceData, "updatedBy"));

            // 映射时间字段
            String recordDateStr = DateTimeParseUtil.getStringValue(sourceData, "recordDate");
            entity.setRecordDate(DateTimeParseUtil.parseDateTime(recordDateStr));

            // 映射更新日期
            String updateDateStr = DateTimeParseUtil.getStringValue(sourceData, "updateDate");
            entity.setUpdateDate(DateTimeParseUtil.parseDate(updateDateStr));

            // 映射预留字段
            entity.setReservedField1(DateTimeParseUtil.getStringValue(sourceData, "reservedField1"));
            entity.setReservedField2(DateTimeParseUtil.getStringValue(sourceData, "reservedField2"));
            entity.setReservedField3(DateTimeParseUtil.getStringValue(sourceData, "reservedField3"));

            return entity;

        } catch (Exception e) {
            throw new RuntimeException("映射FireEquipmentInfo数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getUniqueField() {
        return "id";
    }
}

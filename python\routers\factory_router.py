from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from database import get_db
from models import factory_models
from schemas import factory_schemas
from schemas.common_schemas import Response, ListResponse, ErrorResponse
from datetime import datetime, date
from sqlalchemy import func
from sqlalchemy.sql import text

router = APIRouter(
    prefix="/factory",
    tags=["全厂"]
)

# 全厂人员信息相关接口
@router.post("/personnel/", 
    response_model=Response[factory_schemas.FactoryPersonnelInfo],
    summary="创建全厂人员信息",
    description="创建新的全厂人员信息记录",
    response_description="返回创建的人员信息")
def create_personnel_info(personnel: factory_schemas.FactoryPersonnelInfoCreate, db: Session = Depends(get_db)):
    """
    创建全厂人员信息
    
    参数说明：
    - **total_worker_count**: 工人总数
    - **total_clerical_count**: 职员总数
    - **on_duty_worker_count**: 在岗工人数
    - **on_duty_clerical_count**: 在岗职员数
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_personnel = factory_models.FactoryPersonnelInfo(**personnel.dict())
        db.add(db_personnel)
        db.commit()
        db.refresh(db_personnel)
        return Response(data=db_personnel)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/factory/personnel/latest", 
    response_model=Response[factory_schemas.FactoryPersonnelInfo],
    summary="获取最新全厂人员信息",
    description="获取最新的全厂人员信息记录",
    response_description="返回最新人员信息")
def read_latest_personnel_info(db: Session = Depends(get_db)):
    """获取最新全厂人员信息"""
    latest = db.query(factory_models.FactoryPersonnelInfo).order_by(
        factory_models.FactoryPersonnelInfo.record_date.desc()
    ).first()
    if latest is None:
        return ErrorResponse(
            code=404,
            message="没有找到人员信息",
            success=False
        )
    return Response(data=latest)

# 车间人员信息相关接口
@router.post("/workshop/personnel", 
    response_model=Response[factory_schemas.WorkshopPersonnelInfo],
    summary="创建车间人员信息",
    description="创建新的车间人员信息记录",
    response_description="返回创建的车间人员信息")
def create_workshop_personnel_info(personnel: factory_schemas.WorkshopPersonnelInfoCreate, db: Session = Depends(get_db)):
    """
    创建车间人员信息
    
    参数说明：
    - **workshop_name**: 车间名称
    - **worker_count**: 工人数量
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_personnel = factory_models.WorkshopPersonnelInfo(**personnel.dict())
        db.add(db_personnel)
        db.commit()
        db.refresh(db_personnel)
        return Response(data=db_personnel)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# 车间人员信息
@router.get("/workshop/personnel", response_model=ListResponse[factory_schemas.WorkshopPersonnelInfo])
def get_workshop_personnel(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取车间人员信息（最新日期）"""
    # 首先获取最新日期
    latest_date = db.query(factory_models.WorkshopPersonnelInfo.record_date).order_by(
        factory_models.WorkshopPersonnelInfo.record_date.desc()
    ).first()
    
    if not latest_date:
        return ListResponse(
            data=[],
            total=0,
            message="没有找到人员信息",
            success=True
        )
    
    # 查询最新日期的数据
    query = db.query(factory_models.WorkshopPersonnelInfo).filter(
        factory_models.WorkshopPersonnelInfo.record_date == latest_date[0]
    )
    
    if workshop_name:
        query = query.filter(factory_models.WorkshopPersonnelInfo.workshop_name == workshop_name)
        
    result = query.all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )
# 通知信息相关接口
@router.post("/notifications/", 
    response_model=Response[factory_schemas.NotificationInfo],
    summary="创建通知信息",
    description="创建新的通知信息记录",
    response_description="返回创建的通知信息")
def create_notification(notification: factory_schemas.NotificationInfoCreate, db: Session = Depends(get_db)):
    """
    创建通知信息
    
    参数说明：
    - **title**: 通知标题
    - **content**: 通知内容
    - **type**: 通知类型
    - **status**: 通知状态
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_notification = factory_models.NotificationInfo(**notification.dict())
        db.add(db_notification)
        db.commit()
        db.refresh(db_notification)
        return Response(data=db_notification)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/notifications/latest", 
    response_model=ListResponse[factory_schemas.NotificationInfo],
    summary="获取最新通知信息",
    description="获取最新日期的所有通知信息记录",
    response_description="返回最新日期的通知信息列表")
def read_latest_notification(db: Session = Depends(get_db)):
    """获取最新日期的通知信息列表"""
    # 首先获取最新日期
    latest_date = db.query(factory_models.NotificationInfo.record_date).order_by(
        factory_models.NotificationInfo.record_date.desc()
    ).first()
    
    if not latest_date:
        return ListResponse(
            data=[],
            total=0,
            message="没有找到通知信息",
            success=True
        )
    
    # 查询最新日期的所有数据
    result = db.query(factory_models.NotificationInfo).filter(
        factory_models.NotificationInfo.record_date == latest_date[0]
    ).all()
    
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 设备信息相关接口
@router.post("/equipment/", 
    response_model=Response[factory_schemas.EquipmentInfo],
    summary="创建设备信息",
    description="创建新的设备信息记录",
    response_description="返回创建的设备信息")
def create_equipment_info(equipment: factory_schemas.EquipmentInfoCreate, db: Session = Depends(get_db)):
    """
    创建设备信息
    
    参数说明：
    - **total_equipment_count**: 设备总数
    - **operating_equipment_count**: 运行设备数
    - **operating_rate**: 运行率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_equipment = factory_models.EquipmentInfo(**equipment.dict())
        db.add(db_equipment)
        db.commit()
        db.refresh(db_equipment)
        return Response(data=db_equipment)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/equipment/latest", 
    response_model=ListResponse[factory_schemas.EquipmentInfo],
    summary="获取最新设备信息",
    description="获取最新日期的所有设备信息记录",
    response_description="返回最新日期的设备信息列表")
def read_latest_equipment_info(db: Session = Depends(get_db)):
    """获取最新日期的设备信息列表"""
    # 首先获取最新日期
    latest_date = db.query(factory_models.EquipmentInfo.record_date).order_by(
        factory_models.EquipmentInfo.record_date.desc()
    ).first()
    
    if not latest_date:
        return ListResponse(
            data=[],
            total=0,
            message="没有找到设备信息",
            success=True
        )
    
    # 查询最新日期的所有数据
    result = db.query(factory_models.EquipmentInfo).filter(
        factory_models.EquipmentInfo.record_date == latest_date[0]
    ).all()
    
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 车间设备状态相关接口
@router.post("/workshop-equipment/", 
    response_model=Response[factory_schemas.WorkshopEquipmentStatus],
    summary="创建车间设备状态",
    description="创建新的车间设备状态记录",
    response_description="返回创建的车间设备状态")
def create_workshop_equipment_status(status: factory_schemas.WorkshopEquipmentStatusCreate, db: Session = Depends(get_db)):
    """
    创建车间设备状态
    
    参数说明：
    - **workshop_name**: 车间名称
    - **equipment_count**: 设备数量
    - **operating_count**: 运行数量
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_status = factory_models.WorkshopEquipmentStatus(**status.dict())
        db.add(db_status)
        db.commit()
        db.refresh(db_status)
        return Response(data=db_status)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/workshop-equipment", 
    response_model=ListResponse[factory_schemas.WorkshopEquipmentStatus],
    summary="获取车间设备状态列表",
    description="获取所有车间的设备状态记录",
    response_description="返回车间设备状态列表")
def read_workshop_equipment_status(db: Session = Depends(get_db)):
    """获取所有车间设备状态列表"""
    result = db.query(factory_models.WorkshopEquipmentStatus).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 项目分类相关接口
@router.post("/projects/", 
    response_model=Response[factory_schemas.ProjectClassification],
    summary="创建项目分类",
    description="创建新的项目分类记录",
    response_description="返回创建的项目分类")
def create_project_classification(project: factory_schemas.ProjectClassificationCreate, db: Session = Depends(get_db)):
    """
    创建项目分类
    
    参数说明：
    - **project_type**: 项目类型
    - **project_count**: 项目数量
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_project = factory_models.ProjectClassification(**project.dict())
        db.add(db_project)
        db.commit()
        db.refresh(db_project)
        return Response(data=db_project)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/projects/", 
    response_model=ListResponse[factory_schemas.ProjectClassification],
    summary="获取项目分类列表",
    description="获取所有项目分类记录",
    response_description="返回项目分类列表")
def read_project_classifications(db: Session = Depends(get_db)):
    """获取所有项目分类列表"""
    result = db.query(factory_models.ProjectClassification).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 工单关闭率相关接口
@router.post("/work-orders/", 
    response_model=Response[factory_schemas.WorkOrderCloseRate6Months],
    summary="创建工单关闭率记录",
    description="创建新的工单关闭率记录",
    response_description="返回创建的工单关闭率记录")
def create_work_order_close_rate(work_order: factory_schemas.WorkOrderCloseRate6MonthsCreate, db: Session = Depends(get_db)):
    """
    创建工单关闭率记录
    
    参数说明：
    - **workshop_name**: 车间名称
    - **completed_count**: 已完成工单数
    - **not_closed_count**: 未关闭工单数
    - **close_rate**: 关闭率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_work_order = factory_models.WorkOrderCloseRate6Months(**work_order.dict())
        db.add(db_work_order)
        db.commit()
        db.refresh(db_work_order)
        return Response(data=db_work_order)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/work-orders/trend/", 
    response_model=ListResponse[factory_schemas.WorkOrderCloseRate6Months],
    summary="获取工单关闭率趋势",
    description="获取最近6个日期的工单关闭率数据",
    response_description="返回工单关闭率趋势列表")
def read_work_order_trend(db: Session = Depends(get_db)):
    """获取最近6个日期的工单关闭率数据，按车间名称和日期排序"""
    try:
        # 使用指定的SQL语句获取数据
        sql = """
        SELECT * FROM ioc_qc_work_order_close_rate_6months 
        WHERE record_date IN (
            SELECT record_date FROM ioc_qc_work_order_close_rate_6months 
            GROUP BY record_date 
            ORDER BY record_date DESC 
            LIMIT 6
        ) 
        ORDER BY workshop_name, record_date DESC
        """
        
        result = db.execute(text(sql)).all()
        
        # 将结果转换为模型对象列表
        data = []
        
        for row in result:
            # 将行转换为字典
            row_dict = {column: value for column, value in zip(
                ['id', 'workshop_name', 'completed_count', 'not_closed_count', 'close_rate', 
                 'record_date', 'updated_by', 'updated_at', 'reserved_field1', 'reserved_field2', 'reserved_field3'], 
                row
            )}
            
            # 创建模型对象
            model_obj = factory_models.WorkOrderCloseRate6Months(**row_dict)
            data.append(model_obj)
        
        total = len(data)
        return ListResponse(
            data=data,
            total=total,
            message=f"成功获取{total}条记录"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# 出货量相关接口
@router.post("/shipments/", 
    response_model=Response[factory_schemas.ShipmentCount12Months],
    summary="创建出货量记录",
    description="创建新的出货量记录",
    response_description="返回创建的出货量记录")
def create_shipment_count(shipment: factory_schemas.ShipmentCount12MonthsCreate, db: Session = Depends(get_db)):
    """
    创建出货量记录
    
    参数说明：
    - **shipment_count**: 出货数量
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_shipment = factory_models.ShipmentCount12Months(**shipment.dict())
        db.add(db_shipment)
        db.commit()
        db.refresh(db_shipment)
        return Response(data=db_shipment)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/shipments/trend/", 
    response_model=ListResponse[factory_schemas.ShipmentCount12Months],
    summary="获取出货量趋势",
    description="获取最近12个月的出货量趋势数据",
    response_description="返回出货量趋势列表")
def read_shipment_trend(db: Session = Depends(get_db)):
    """获取最近12个月出货量趋势"""
    result = db.query(factory_models.ShipmentCount12Months).order_by(
        factory_models.ShipmentCount12Months.record_date.desc()
    ).limit(12).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 全厂人员信息
@router.get("/personnel", response_model=ListResponse[factory_schemas.FactoryPersonnelInfo])
def get_factory_personnel(
    db: Session = Depends(get_db)
):
    """获取全厂人员信息（最新日期）"""
    # 首先获取最新日期
    latest_date = db.query(factory_models.FactoryPersonnelInfo.record_date).order_by(
        factory_models.FactoryPersonnelInfo.record_date.desc()
    ).first()
    
    if not latest_date:
        return ListResponse(
            data=[],
            total=0,
            message="没有找到人员信息",
            success=True
        )
    
    # 查询最新日期的数据
    result = db.query(factory_models.FactoryPersonnelInfo).filter(
        factory_models.FactoryPersonnelInfo.record_date == latest_date[0]
    ).all()
    
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 通知信息
@router.get("/notifications", response_model=ListResponse[factory_schemas.NotificationInfo])
def get_notifications(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取通知信息"""
    query = db.query(factory_models.NotificationInfo)
    if start_date:
        query = query.filter(factory_models.NotificationInfo.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.NotificationInfo.record_date <= end_date)
    result = query.order_by(factory_models.NotificationInfo.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 设备信息
@router.get("/equipment", response_model=ListResponse[factory_schemas.EquipmentInfo])
def get_equipment_info(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取设备信息"""
    query = db.query(factory_models.EquipmentInfo)
    if start_date:
        query = query.filter(factory_models.EquipmentInfo.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.EquipmentInfo.record_date <= end_date)
    result = query.order_by(factory_models.EquipmentInfo.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 车间设备状态
@router.get("/workshop/equipment", response_model=ListResponse[factory_schemas.WorkshopEquipmentStatus])
def get_workshop_equipment(
    workshop_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取车间设备状态"""
    query = db.query(factory_models.WorkshopEquipmentStatus)
    if workshop_name:
        query = query.filter(factory_models.WorkshopEquipmentStatus.workshop_name == workshop_name)
    if start_date:
        query = query.filter(factory_models.WorkshopEquipmentStatus.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.WorkshopEquipmentStatus.record_date <= end_date)
    result = query.order_by(factory_models.WorkshopEquipmentStatus.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 项目分类
@router.get("/projects/classification", response_model=ListResponse[factory_schemas.ProjectClassification])
def get_project_classification(
    project_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取项目分类"""
    query = db.query(factory_models.ProjectClassification)
    if project_name:
        query = query.filter(factory_models.ProjectClassification.project_name == project_name)
    if start_date:
        query = query.filter(factory_models.ProjectClassification.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.ProjectClassification.record_date <= end_date)
    result = query.order_by(factory_models.ProjectClassification.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 年度项目分类
@router.get("/projects/annual", response_model=ListResponse[factory_schemas.AnnualProjectCategory])
def get_annual_project_category(
    project_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取年度项目分类"""
    query = db.query(factory_models.AnnualProjectCategory)
    if project_name:
        query = query.filter(factory_models.AnnualProjectCategory.project_name == project_name)
    if start_date:
        query = query.filter(factory_models.AnnualProjectCategory.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.AnnualProjectCategory.record_date <= end_date)
    result = query.order_by(factory_models.AnnualProjectCategory.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 注塑抽检合格率
@router.get("/quality/injection", response_model=ListResponse[factory_schemas.InjectionSamplingQualityRate])
def get_injection_sampling_quality_rate(
    workshop_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取注塑抽检合格率"""
    query = db.query(factory_models.InjectionSamplingQualityRate)
    if workshop_name:
        query = query.filter(factory_models.InjectionSamplingQualityRate.workshop_name == workshop_name)
    if start_date:
        query = query.filter(factory_models.InjectionSamplingQualityRate.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.InjectionSamplingQualityRate.record_date <= end_date)
    result = query.order_by(factory_models.InjectionSamplingQualityRate.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 组装FOC检验合格率
@router.get("/quality/asm-foc", response_model=ListResponse[factory_schemas.AsmFocInspectionPassRate])
def get_asm_foc_inspection_pass_rate(
    workshop_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取组装FOC检验合格率"""
    query = db.query(factory_models.AsmFocInspectionPassRate)
    if workshop_name:
        query = query.filter(factory_models.AsmFocInspectionPassRate.workshop_name == workshop_name)
    if start_date:
        query = query.filter(factory_models.AsmFocInspectionPassRate.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.AsmFocInspectionPassRate.record_date <= end_date)
    result = query.order_by(factory_models.AsmFocInspectionPassRate.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# CNC巡检检验合格率
@router.get("/quality/cnc", response_model=ListResponse[factory_schemas.CncInspectionPassRate])
def get_cnc_inspection_pass_rate(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取CNC巡检检验合格率"""
    query = db.query(factory_models.CncInspectionPassRate)
    if start_date:
        query = query.filter(factory_models.CncInspectionPassRate.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.CncInspectionPassRate.record_date <= end_date)
    result = query.order_by(factory_models.CncInspectionPassRate.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 涂装巡检检验合格率
@router.get("/quality/coating", response_model=ListResponse[factory_schemas.CoatingInspectionPassRate])
def get_coating_inspection_pass_rate(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取涂装巡检检验合格率"""
    query = db.query(factory_models.CoatingInspectionPassRate)
    if start_date:
        query = query.filter(factory_models.CoatingInspectionPassRate.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.CoatingInspectionPassRate.record_date <= end_date)
    result = query.order_by(factory_models.CoatingInspectionPassRate.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 工单关闭率6个月趋势
@router.get("/work-orders/close-rate", response_model=ListResponse[factory_schemas.WorkOrderCloseRate6Months])
def get_work_order_close_rate(
    workshop_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取工单关闭率6个月趋势"""
    query = db.query(factory_models.WorkOrderCloseRate6Months)
    if workshop_name:
        query = query.filter(factory_models.WorkOrderCloseRate6Months.workshop_name == workshop_name)
    if start_date:
        query = query.filter(factory_models.WorkOrderCloseRate6Months.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.WorkOrderCloseRate6Months.record_date <= end_date)
    result = query.order_by(factory_models.WorkOrderCloseRate6Months.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 出货量12个月趋势
@router.get("/shipments/trend", response_model=ListResponse[factory_schemas.ShipmentCount12Months])
def get_shipment_count_trend(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取出货量12个月趋势"""
    query = db.query(factory_models.ShipmentCount12Months)
    if start_date:
        query = query.filter(factory_models.ShipmentCount12Months.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.ShipmentCount12Months.record_date <= end_date)
    result = query.order_by(factory_models.ShipmentCount12Months.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

# 近六个月品料合格率相关接口
@router.post("/material-pass-rate/", 
    response_model=Response[factory_schemas.MaterialPassRateLast6Months],
    summary="创建近六个月品料合格率记录",
    description="创建新的近六个月品料合格率记录",
    response_description="返回创建的近六个月品料合格率记录")
def create_material_pass_rate(material_pass_rate: factory_schemas.MaterialPassRateLast6MonthsCreate, db: Session = Depends(get_db)):
    """
    创建近六个月品料合格率记录
    
    参数说明：
    - **pass_rate**: 合格率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_material_pass_rate = factory_models.MaterialPassRateLast6Months(**material_pass_rate.dict())
        db.add(db_material_pass_rate)
        db.commit()
        db.refresh(db_material_pass_rate)
        return Response(data=db_material_pass_rate)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/material-pass-rate/trend", 
    response_model=ListResponse[factory_schemas.MaterialPassRateLast6Months],
    summary="获取近六个月品料合格率趋势",
    description="获取最近6个月的品料合格率趋势数据",
    response_description="返回近六个月品料合格率趋势列表")
def read_material_pass_rate_trend(db: Session = Depends(get_db)):
    """获取最近6个月品料合格率趋势"""
    result = db.query(factory_models.MaterialPassRateLast6Months).order_by(
        factory_models.MaterialPassRateLast6Months.record_date.desc()
    ).limit(6).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    )

@router.get("/material-pass-rate", 
    response_model=ListResponse[factory_schemas.MaterialPassRateLast6Months],
    summary="获取近六个月品料合格率",
    description="获取近六个月品料合格率数据",
    response_description="返回近六个月品料合格率列表")
def get_material_pass_rate(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    db: Session = Depends(get_db)
):
    """获取近六个月品料合格率"""
    query = db.query(factory_models.MaterialPassRateLast6Months)
    if start_date:
        query = query.filter(factory_models.MaterialPassRateLast6Months.record_date >= start_date)
    if end_date:
        query = query.filter(factory_models.MaterialPassRateLast6Months.record_date <= end_date)
    result = query.order_by(factory_models.MaterialPassRateLast6Months.record_date.desc()).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录",
        success=True
    ) 
package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.InjectionQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.InjectionQuality;
import com.vfdata.pujiang_vfd.modules.factory.repository.InjectionQualityRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionQualityService {

    private final InjectionQualityRepository injectionQualityRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取注塑质量信息
     * 返回每个车间最新记录日期的数据
     */
    public List<InjectionQualityDTO> getInjectionQualityInfo() {
        List<InjectionQuality> entities = injectionQualityRepository.findLatestByWorkshopName();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private InjectionQualityDTO convertToDTO(InjectionQuality entity) {
        InjectionQualityDTO dto = new InjectionQualityDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        if (entity.getRecordDate() != null) {
            dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        }
        dto.setUpdated_by(entity.getUpdatedBy());
        if (entity.getUpdatedAt() != null) {
            dto.setUpdated_at(entity.getUpdatedAt().toString());
        }
        return dto;
    }
} 
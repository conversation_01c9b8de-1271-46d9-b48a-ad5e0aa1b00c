package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "宿舍信息DTO")
public class HostelDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "宿舍总数")
    private Integer hostel_num;

    @Schema(description = "已使用数量")
    private Integer hostel_use;

    @Schema(description = "剩余数量")
    private Integer hostel_remaining;

    @Schema(description = "宿舍信息")
    private String hostel_info;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

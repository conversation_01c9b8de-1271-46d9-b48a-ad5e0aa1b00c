#!/bin/bash

# 设置变量
APP_NAME="pujiang"
IMAGE_NAME="pujiang_api"
CONTAINER_NAME="pujiang_api"

# 加载环境变量
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 设置镜像源
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 打印信息
info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# 构建镜像
build() {
    info "开始构建 $APP_NAME 镜像..."
    docker compose build \
        --build-arg PIP_INDEX_URL=${PIP_INDEX_URL} \
        --build-arg PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
        --build-arg API_PORT=${API_PORT} \
        app
    if [ $? -eq 0 ]; then
        info "镜像构建成功"
    else
        error "镜像构建失败"
        exit 1
    fi
}

# 启动服务
start() {
    info "启动 $APP_NAME 服务..."
    docker compose up -d
    if [ $? -eq 0 ]; then
        info "服务启动成功"
        info "API服务地址: http://${API_HOST}:${API_PORT}"
        if [ ! -z "${POSTGRES_HOST}" ]; then
            info "数据库地址: ${POSTGRES_HOST}:${POSTGRES_PORT}"
        fi
    else
        error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop() {
    info "停止 $APP_NAME 服务..."
    docker compose down
    if [ $? -eq 0 ]; then
        info "服务已停止"
    else
        error "服务停止失败"
        exit 1
    fi
}

# 重启服务
restart() {
    stop
    start
}

# 查看日志
logs() {
    info "查看 $APP_NAME 服务日志..."
    docker compose logs -f
}

# 查看状态
status() {
    info "服务状态："
    docker compose ps
}

# 清理
clean() {
    warn "即将清理所有相关容器和镜像..."
    read -p "确定要继续吗？[y/N] " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker compose down -v
        docker rmi ${IMAGE_NAME}
        info "清理完成"
    fi
}

# 同步数据
sync() {
    echo "开始同步数据..."
    docker exec -it pujiang-app python run_sync.py
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {build|start|stop|restart|logs|status|clean|sync}"
    echo
    echo "命令:"
    echo "  build    构建Docker镜像"
    echo "  start    启动服务"
    echo "  stop     停止服务"
    echo "  restart  重启服务"
    echo "  logs     查看服务日志"
    echo "  status   查看服务状态"
    echo "  clean    清理容器和镜像"
    echo "  sync     同步数据"
}

# 主程序
case "$1" in
    build)
        build
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    sync)
        sync
        ;;
    *)
        show_help
        exit 1
        ;;
esac

exit 0 
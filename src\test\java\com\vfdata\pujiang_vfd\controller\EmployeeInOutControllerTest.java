package com.vfdata.pujiang_vfd.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
class EmployeeInOutControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testEmployeeInOutApiResponse() throws Exception {
        // 调用实际的API接口
        MvcResult result = mockMvc.perform(get("/dashboard/employee-inout/"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("API响应内容: " + responseContent);

        // 验证响应不包含时区信息
        assertFalse(responseContent.contains("+08"), "API响应不应包含+08时区信息");
        assertFalse(responseContent.contains("+0800"), "API响应不应包含+0800时区信息");
        
        // 验证响应格式正确
        assertTrue(responseContent.contains("\"code\":200"), "响应应包含成功状态码");
        assertTrue(responseContent.contains("\"data\":["), "响应应包含数据数组");
        
        // 如果有数据，验证时间格式
        if (responseContent.contains("recordDate")) {
            // 检查是否使用了正确的时间格式
            assertTrue(responseContent.matches(".*\"recordDate\":\"\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\".*"), 
                "recordDate应使用yyyy-MM-dd HH:mm:ss格式");
        }
        
        if (responseContent.contains("updatetime")) {
            // 检查是否使用了正确的时间格式
            assertTrue(responseContent.matches(".*\"updatetime\":\"\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\".*"), 
                "updatetime应使用yyyy-MM-dd HH:mm:ss格式");
        }
    }

    @Test
    void testJsonParsing() throws Exception {
        // 测试JSON解析是否正确
        MvcResult result = mockMvc.perform(get("/dashboard/employee-inout/"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        
        // 尝试解析JSON，确保格式正确
        try {
            objectMapper.readTree(responseContent);
            System.out.println("JSON解析成功");
        } catch (Exception e) {
            fail("JSON格式错误: " + e.getMessage());
        }
    }
}

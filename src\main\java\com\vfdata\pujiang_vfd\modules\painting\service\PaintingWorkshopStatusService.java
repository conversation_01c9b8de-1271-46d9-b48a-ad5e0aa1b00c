package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingWorkshopStatus;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingWorkshopStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingWorkshopStatusService {

    private final PaintingWorkshopStatusRepository workshopStatusRepository;

    /**
     * 获取最新的车间设备状况数据
     */
    public List<PaintingWorkshopStatusDTO> getLatestWorkshopStatus() {
        List<PaintingWorkshopStatus> entities = workshopStatusRepository.findLatestRecords();
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingWorkshopStatusDTO convertToDTO(PaintingWorkshopStatus entity) {
        PaintingWorkshopStatusDTO dto = new PaintingWorkshopStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setTotal_equipment_count(entity.getTotalEquipmentCount());
        dto.setOperating_equipment_count(entity.getOperatingEquipmentCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

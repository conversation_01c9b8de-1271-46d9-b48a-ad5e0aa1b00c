package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyTestDefectClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface AssemblyTestDefectClassificationRepository extends JpaRepository<AssemblyTestDefectClassification, Long> {
    
    @Query("SELECT t FROM AssemblyTestDefectClassification t WHERE t.recordDate = (SELECT MAX(t2.recordDate) FROM AssemblyTestDefectClassification t2)")
    List<AssemblyTestDefectClassification> findLatest();
    
    @Query("SELECT t FROM AssemblyTestDefectClassification t WHERE t.workshopName = :workshopName AND t.recordDate = (SELECT MAX(t2.recordDate) FROM AssemblyTestDefectClassification t2 WHERE t2.workshopName = :workshopName)")
    List<AssemblyTestDefectClassification> findLatestByWorkshopName(@Param("workshopName") String workshopName);
} 
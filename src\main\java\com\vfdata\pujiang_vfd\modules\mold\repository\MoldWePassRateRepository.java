package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldWePassRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldWePassRateRepository extends JpaRepository<MoldWePassRate, Long> {

    /**
     * 获取WE合格率数据，根据period参数返回不同数量
     * @param period 周期类型：month返回6条，day返回7条
     */
    @Query("SELECT m FROM MoldWePassRate m WHERE m.period = :period ORDER BY m.recordDate DESC LIMIT :limit")
    List<MoldWePassRate> findLatestRecordsByPeriod(@Param("period") String period, @Param("limit") int limit);
}

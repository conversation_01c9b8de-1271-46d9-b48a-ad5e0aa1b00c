package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.FqcQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyFqcQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyFqcQualityRateService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/fqc/quality-rate")
@RequiredArgsConstructor
public class AssemblyFqcQualityRateController {

    private final AssemblyFqcQualityRateService fqcQualityRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新FQC质量率")
    public ResponseUtils.Result<List<FqcQualityRateDTO>> getLatestFqcQualityRate() {
        try {
            List<FqcQualityRateDTO> rates = fqcQualityRateService.getLatestFqcQualityRate();
            return ResponseUtils.success(rates);
        } catch (Exception e) {
            return ResponseUtils.error("获取FQC质量率失败：" + e.getMessage());
        }
    }

    @GetMapping("/week")
    @Operation(summary = "获取近7天FQC质量率", description = "获取组装车间近7天的FQC质量率记录")
    public ResponseUtils.Result<List<AssemblyFqcQualityRate7DaysDTO>> getWeekFqcQualityRate(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<AssemblyFqcQualityRate7DaysDTO> rates = fqcQualityRateService.getWeekFqcQualityRate(workshop_name);
            return ResponseUtils.success(rates);
        } catch (Exception e) {
            return ResponseUtils.error("获取近7天FQC质量率失败：" + e.getMessage());
        }
    }
} 
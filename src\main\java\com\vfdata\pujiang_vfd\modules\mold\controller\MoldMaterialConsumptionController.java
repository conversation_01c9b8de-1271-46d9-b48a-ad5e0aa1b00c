package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldMaterialConsumptionDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldMaterialConsumptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/material-consumption")
@RequiredArgsConstructor
public class MoldMaterialConsumptionController {

    private final MoldMaterialConsumptionService materialConsumptionService;

    @GetMapping("/latest")
    @Operation(summary = "获取模具车间物料消耗", description = "获取模具车间最新的物料消耗信息")
    public ResponseUtils.Result<List<MoldMaterialConsumptionDTO>> getLatestMaterialConsumption() {
        try {
            List<MoldMaterialConsumptionDTO> data = materialConsumptionService.getLatestMaterialConsumption();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间物料消耗失败：" + e.getMessage());
        }
    }
}

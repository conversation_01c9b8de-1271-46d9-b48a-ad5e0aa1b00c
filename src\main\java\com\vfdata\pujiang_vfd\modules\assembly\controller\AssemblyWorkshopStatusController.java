package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.WorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyWorkshopStatusService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/workshop-status")
@RequiredArgsConstructor
public class AssemblyWorkshopStatusController {

    private final AssemblyWorkshopStatusService workshopStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新车间设备状态", description = "获取组装车间最新的设备状态记录，可选择指定车间")
    public ResponseUtils.Result<List<WorkshopStatusDTO>> getLatestWorkshopStatus(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<WorkshopStatusDTO> statusList = workshopStatusService.getLatestWorkshopStatus(workshop_name);
            return ResponseUtils.success(statusList);
        } catch (Exception e) {
            return ResponseUtils.error("获取车间设备状态失败：" + e.getMessage());
        }
    }
} 
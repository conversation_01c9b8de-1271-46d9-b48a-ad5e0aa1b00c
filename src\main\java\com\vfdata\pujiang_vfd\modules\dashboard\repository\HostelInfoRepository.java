package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.HostelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface HostelInfoRepository extends JpaRepository<HostelInfo, Long> {
    
    List<HostelInfo> findAllByOrderByRecordDateDesc();
}

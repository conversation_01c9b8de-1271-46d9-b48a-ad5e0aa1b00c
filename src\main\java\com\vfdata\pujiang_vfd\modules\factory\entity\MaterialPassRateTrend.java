package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Table(name = "ioc_qc_material_pass_rate_last_6_months")
@Schema(description = "物料合格率趋势信息")
@Getter
@Setter
public class MaterialPassRateTrend extends BaseEntity {
    
    @Schema(description = "合格率")
    @Column(name = "pass_rate")
    private BigDecimal passRate;

    @Schema(description = "预留字段1")
    @Column(name = "reserved_field1")
    private String reservedField1;

    @Schema(description = "预留字段2")
    @Column(name = "reserved_field2")
    private String reservedField2;

    @Schema(description = "预留字段3")
    @Column(name = "reserved_field3")
    private String reservedField3;
} 
package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.PersonnelCountDTO;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionPersonnelInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionPersonnelInfoService {

    private final InjectionPersonnelInfoRepository injectionPersonnelInfoRepository;

    public List<PersonnelCountDTO> getLatestPersonnelInfo() {
        List<Object[]> rawResults = injectionPersonnelInfoRepository.findLatestPersonnelCountsRaw();

        return rawResults.stream()
                .map(row -> {
                    PersonnelCountDTO dto = new PersonnelCountDTO();
                    dto.setCount((Integer) row[0]);
                    dto.setPersonnel_type((String) row[1]);
                    return dto;
                })
                .collect(Collectors.toList());
    }
} 
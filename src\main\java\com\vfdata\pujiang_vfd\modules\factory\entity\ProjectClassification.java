package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "ioc_qc_project_classification")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目分类信息")
public class ProjectClassification extends BaseEntity {

    @Schema(description = "项目名称")
    @Column(name = "project_name", length = 50)
    private String projectName;

    @Schema(description = "量产数量")
    @Column(name = "mass_production_count")
    private Integer massProductionCount;

    @Schema(description = "开发数量")
    @Column(name = "development_count")
    private Integer developmentCount;
} 
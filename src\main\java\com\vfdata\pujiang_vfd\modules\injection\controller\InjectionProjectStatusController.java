package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionProjectStatusService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/project-status")
@RequiredArgsConstructor
public class InjectionProjectStatusController {

    private final InjectionProjectStatusService injectionProjectStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取车间项目状态数据")
    public ResponseUtils.Result<List<InjectionProjectStatusDTO>> getProjectStatus(
            @RequestParam(required = false) String workshop_name) {
        List<InjectionProjectStatusDTO> statusList = injectionProjectStatusService.getProjectStatus(workshop_name);
        if (statusList == null) {
            return ResponseUtils.error("未找到项目状态数据");
        }
        return ResponseUtils.success(statusList);
    }
} 
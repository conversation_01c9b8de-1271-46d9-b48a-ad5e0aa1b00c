package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface InjectionPersonnelInfoRepository extends JpaRepository<InjectionPersonnelInfo, Long> {

    @Query("SELECT i FROM InjectionPersonnelInfo i WHERE i.recordDate = (SELECT MAX(i2.recordDate) FROM InjectionPersonnelInfo i2 WHERE i2.personnelType = :type) AND i.personnelType = :type")
    Optional<InjectionPersonnelInfo> findLatestByType(@Param("type") String type);

    @Query(value = """
        SELECT general_worker_count as count, personnel_type
        FROM ioc_zscj_personnel_info
        WHERE record_date = (
            SELECT max(record_date)
            FROM ioc_zscj_personnel_info
        )
        UNION ALL
        SELECT staff_count as count, '总人数' as personnel_type
        FROM ioc_zscj_personnel_info
        WHERE record_date = (
            SELECT max(record_date)
            FROM ioc_zscj_personnel_info
        )
        AND personnel_type = '职员'
        """, nativeQuery = true)
    List<Object[]> findLatestPersonnelCountsRaw();
}
package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyQualityRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyQualityRateRepository extends JpaRepository<NewEnergyQualityRate, Long> {
    
    /**
     * 获取每个车间最新记录日期的抽检检验合格率数据
     * 取每个车间record_date最新的一条数据
     */
    @Query("SELECT n FROM NewEnergyQualityRate n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyQualityRate n2 WHERE n2.workshopName = n.workshopName)")
    List<NewEnergyQualityRate> findLatestByEachWorkshop();
}

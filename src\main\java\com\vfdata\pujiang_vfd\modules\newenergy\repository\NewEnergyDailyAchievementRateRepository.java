package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyDailyAchievementRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyDailyAchievementRateRepository extends JpaRepository<NewEnergyDailyAchievementRate, Long> {
    
    /**
     * 获取每个车间最新记录日期的日计划达成率数据
     * 取每个车间record_date最新的一条数据
     */
    @Query("SELECT n FROM NewEnergyDailyAchievementRate n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyDailyAchievementRate n2 WHERE n2.workshopName = n.workshopName)")
    List<NewEnergyDailyAchievementRate> findLatestByEachWorkshop();
}

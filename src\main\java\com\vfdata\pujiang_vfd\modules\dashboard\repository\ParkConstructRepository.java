package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.ParkConstruct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ParkConstructRepository extends JpaRepository<ParkConstruct, Long> {
    
    List<ParkConstruct> findAllByOrderByRecordDateDesc();
}

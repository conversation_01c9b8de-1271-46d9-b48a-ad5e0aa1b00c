package com.vfdata.pujiang_vfd.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.EmployeeInOut;
import com.vfdata.pujiang_vfd.modules.dashboard.repository.EmployeeInOutRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
class EmployeeInOutRepositoryTest {

    @Autowired
    private EmployeeInOutRepository employeeInOutRepository;

    @Test
    void testEmployeeInOutData() {
        // 查询前5条记录
        List<EmployeeInOut> records = employeeInOutRepository.findTop20ByOrderByRecordDateDesc();
        
        System.out.println("数据库中的记录数量: " + records.size());
        
        for (int i = 0; i < Math.min(5, records.size()); i++) {
            EmployeeInOut record = records.get(i);
            System.out.println("记录 " + (i + 1) + ":");
            System.out.println("  ID: " + record.getId());
            System.out.println("  员工姓名: " + record.getEmployeeName());
            System.out.println("  记录时间: " + record.getRecordDate());
            System.out.println("  更新时间: " + record.getUpdatetime());
            System.out.println("  记录时间类型: " + (record.getRecordDate() != null ? record.getRecordDate().getClass().getSimpleName() : "null"));
            System.out.println("  记录时间toString: " + (record.getRecordDate() != null ? record.getRecordDate().toString() : "null"));
            System.out.println("---");
        }
    }
}

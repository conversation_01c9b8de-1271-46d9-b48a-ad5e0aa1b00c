package com.vfdata.pujiang_vfd.modules.safety.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_szhaq_fire_equipment_info")
@Schema(description = "消防设备信息")
public class FireEquipmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "fire_extinguisher", length = 255)
    @Schema(description = "灭火器状态")
    @JsonProperty("fire_extinguisher")
    private String fireExtinguisher;

    @Column(name = "fire_sprinkler", length = 255)
    @Schema(description = "喷淋系统状态")
    @JsonProperty("fire_sprinkler")
    private String fireSprinkler;

    @Column(name = "fire_hydrant", length = 255)
    @Schema(description = "消防栓状态")
    @JsonProperty("fire_hydrant")
    private String fireHydrant;

    @Column(name = "fire_alarm_device", length = 255)
    @Schema(description = "火警报警器状态")
    @JsonProperty("fire_alarm_device")
    private String fireAlarmDevice;

    @Column(name = "record_date")
    @Schema(description = "记录时间")
    @JsonProperty("record_date")
    private LocalDateTime recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    @JsonProperty("updated_by")
    private String updatedBy;

    @Column(name = "update_date")
    @Schema(description = "更新日期")
    @JsonProperty("update_date")
    private LocalDate updateDate;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    @JsonProperty("reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    @JsonProperty("reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    @JsonProperty("reserved_field3")
    private String reservedField3;
}

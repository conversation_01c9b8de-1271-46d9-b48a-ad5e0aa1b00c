package com.vfdata.pujiang_vfd.modules.painting.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "喷涂车间设备状态分布DTO")
public class PaintingEquipmentStatusDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "设备状态")
    private String status_name;

    @Schema(description = "该状态下的设备数量")
    private Integer status_count;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldMaterialConsumption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldMaterialConsumptionRepository extends JpaRepository<MoldMaterialConsumption, Long> {

    /**
     * 获取最新记录日期的物料消耗数据
     */
    @Query("SELECT m FROM MoldMaterialConsumption m WHERE m.recordDate = (SELECT MAX(m2.recordDate) FROM MoldMaterialConsumption m2)")
    List<MoldMaterialConsumption> findLatestRecords();
}

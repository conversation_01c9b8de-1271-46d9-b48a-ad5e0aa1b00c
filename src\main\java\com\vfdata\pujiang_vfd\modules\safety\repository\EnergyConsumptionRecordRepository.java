package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.EnergyConsumptionRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface EnergyConsumptionRecordRepository extends JpaRepository<EnergyConsumptionRecord, Long> {
    
    List<EnergyConsumptionRecord> findAllByOrderByRecordTimeDesc();
}

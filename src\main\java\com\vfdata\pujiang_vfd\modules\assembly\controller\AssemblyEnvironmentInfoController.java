package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyEnvironmentInfoService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/environment")
@RequiredArgsConstructor
public class AssemblyEnvironmentInfoController {

    private final AssemblyEnvironmentInfoService environmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新环境信息")
    public ResponseUtils.Result<AssemblyEnvironmentInfoDTO> getLatestEnvironmentInfo() {
        try {
            AssemblyEnvironmentInfoDTO info = environmentInfoService.getLatestEnvironmentInfo();
            return ResponseUtils.success(info);
        } catch (Exception e) {
            return ResponseUtils.error("获取环境信息失败：" + e.getMessage());
        }
    }
} 
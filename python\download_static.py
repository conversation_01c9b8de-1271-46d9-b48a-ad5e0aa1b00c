import os
import requests

def download_file(url, filename):
    """下载文件到指定位置"""
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # 保存文件
        with open(filename, 'wb') as f:
            f.write(response.content)
        print(f"成功下载: {filename}")
    except Exception as e:
        print(f"下载失败 {filename}: {str(e)}")

def main():
    # 创建static目录
    os.makedirs("static", exist_ok=True)
    
    # 要下载的文件列表
    files = {
        "swagger-ui-bundle.js": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.11.8/swagger-ui-bundle.js",
        "swagger-ui.css": "https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.11.8/swagger-ui.css",
        "redoc.standalone.js": "https://cdn.jsdelivr.net/npm/redoc@2.1.3/bundles/redoc.standalone.js",
    }
    
    # 下载所有文件
    for filename, url in files.items():
        download_file(url, os.path.join("static", filename))

if __name__ == "__main__":
    main() 
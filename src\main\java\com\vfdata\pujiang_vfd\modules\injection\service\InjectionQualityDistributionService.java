package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionQualityDistributionDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionQualityDistribution;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionQualityDistributionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionQualityDistributionService {
    
    private final InjectionQualityDistributionRepository qualityDistributionRepository;

    public List<InjectionQualityDistributionDTO> getLatestQualityDistribution(String workshopName) {
        List<InjectionQualityDistribution> distributions;
        if (workshopName == null || workshopName.trim().isEmpty()) {
            distributions = qualityDistributionRepository.findLatestAll();
        } else {
            distributions = qualityDistributionRepository.findLatestByWorkshopName(workshopName);
        }
        
        return distributions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private InjectionQualityDistributionDTO convertToDTO(InjectionQualityDistribution distribution) {
        InjectionQualityDistributionDTO dto = new InjectionQualityDistributionDTO();
        dto.setId(distribution.getId());
        dto.setWorkshop_name(distribution.getWorkshopName());
        dto.setQuality_name(distribution.getQualityName());
        dto.setQuality_rate(distribution.getQualityRate());
        dto.setRecord_date(distribution.getRecordDate());
        dto.setUpdated_by(distribution.getUpdatedBy());
        dto.setUpdated_at(distribution.getUpdatedAt());
        dto.setReserved_field1(distribution.getReservedField1());
        dto.setReserved_field2(distribution.getReservedField2());
        dto.setReserved_field3(distribution.getReservedField3());
        return dto;
    }
} 
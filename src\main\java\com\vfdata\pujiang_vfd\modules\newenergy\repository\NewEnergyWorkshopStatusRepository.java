package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyWorkshopStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyWorkshopStatusRepository extends JpaRepository<NewEnergyWorkshopStatus, Long> {
    
    /**
     * 获取每个车间最新记录日期的数据
     * SQL: SELECT * from ioc_xnycj_workshop_status 取每个车间record_date最新的一条数据
     */
    @Query("SELECT n FROM NewEnergyWorkshopStatus n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyWorkshopStatus n2 WHERE n2.workshopName = n.workshopName)")
    List<NewEnergyWorkshopStatus> findLatestByEachWorkshop();
}

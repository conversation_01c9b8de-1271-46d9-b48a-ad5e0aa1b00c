package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncAttendanceRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CncAttendanceRateRepository extends JpaRepository<CncAttendanceRate, Long> {

    /**
     * 获取最新的出勤率记录
     */
    @Query("SELECT c FROM CncAttendanceRate c WHERE c.recordDate = (SELECT MAX(c2.recordDate) FROM CncAttendanceRate c2)")
    CncAttendanceRate findLatestRecord();
}

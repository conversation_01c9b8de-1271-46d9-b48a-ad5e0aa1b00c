package com.vfdata.pujiang_vfd.modules.assembly.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zzcj_process_yield_info")
@Schema(description = "设备测试工序良率信息")
public class AssemblyProcessYieldInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "process_yield", precision = 5, scale = 2)
    @Schema(description = "工序良率")
    private BigDecimal processYield;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "所属车间")
    private String workshopName;

    @Column(name = "record_date")
    @Schema(description = "日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyTestDefectClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyTestDefectClassificationRepository extends JpaRepository<NewEnergyTestDefectClassification, Long> {
    
    /**
     * 获取最新记录日期的所有测试不良分类数据
     * 先找到最新的record_date，然后返回该日期的所有记录
     */
    @Query("SELECT n FROM NewEnergyTestDefectClassification n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyTestDefectClassification n2)")
    List<NewEnergyTestDefectClassification> findLatestRecords();
}

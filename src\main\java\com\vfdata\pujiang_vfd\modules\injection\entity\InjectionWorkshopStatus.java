package com.vfdata.pujiang_vfd.modules.injection.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zscj_workshop_status")
@Schema(description = "注塑车间状态")
public class InjectionWorkshopStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "车间名称")
    private String workshopName;

    @Column(name = "total_equipment")
    @Schema(description = "设备总数")
    private Integer totalEquipment;

    @Column(name = "operating_equipment")
    @Schema(description = "运行设备数")
    private Integer operatingEquipment;

    @Column(name = "operating_rate", precision = 5, scale = 2)
    @Schema(description = "运行率")
    private BigDecimal operatingRate;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
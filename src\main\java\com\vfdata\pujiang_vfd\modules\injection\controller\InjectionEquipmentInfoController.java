package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionEquipmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/injection/equipment-info")
@RequiredArgsConstructor
@Tag(name = "注塑车间页面接口")
public class InjectionEquipmentInfoController {

    private final InjectionEquipmentInfoService injectionEquipmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新设备信息", description = "获取注塑车间最新的设备信息记录")
    public ResponseUtils.Result<InjectionEquipmentInfoDTO> getLatestEquipmentInfo() {
        return ResponseUtils.success(injectionEquipmentInfoService.getLatestEquipmentInfo());
    }
} 
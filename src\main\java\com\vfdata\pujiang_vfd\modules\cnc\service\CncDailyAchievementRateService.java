package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncDailyAchievementRate;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncDailyAchievementRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncDailyAchievementRateService {

    private final CncDailyAchievementRateRepository dailyAchievementRateRepository;

    /**
     * 获取最新的计划达成率信息
     */
    public List<CncDailyAchievementRateDTO> getLatestDailyAchievementRate() {
        List<CncDailyAchievementRate> achievementRates = dailyAchievementRateRepository.findLatestRecords();
        return achievementRates.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncDailyAchievementRateDTO convertToDTO(CncDailyAchievementRate entity) {
        CncDailyAchievementRateDTO dto = new CncDailyAchievementRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setPlanned_quantity(entity.getPlannedQuantity());
        dto.setActual_quantity(entity.getActualQuantity());
        dto.setAchievement_rate(entity.getAchievementRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

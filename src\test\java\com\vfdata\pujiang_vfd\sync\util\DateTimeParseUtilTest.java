package com.vfdata.pujiang_vfd.sync.util;

import org.junit.jupiter.api.Test;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class DateTimeParseUtilTest {

    @Test
    void testParseDateWithTimezone() {
        // 测试带时区的日期格式
        LocalDate result = DateTimeParseUtil.parseDate("2025-06-23 +08");
        assertNotNull(result);
        assertEquals(LocalDate.of(2025, 6, 23), result);
    }

    @Test
    void testParseDateTimeWithTimezone() {
        // 测试带时区的日期时间格式
        LocalDateTime result = DateTimeParseUtil.parseDateTime("2025-06-23 +08");
        assertNotNull(result);
        assertEquals(LocalDate.of(2025, 6, 23), result.toLocalDate());
    }

    @Test
    void testParseStandardDateTime() {
        // 测试标准日期时间格式
        LocalDateTime result = DateTimeParseUtil.parseDateTime("2025-06-23 00:00:13");
        assertNotNull(result);
        assertEquals(LocalDateTime.of(2025, 6, 23, 0, 0, 13), result);
    }

    @Test
    void testParseStandardDate() {
        // 测试标准日期格式
        LocalDate result = DateTimeParseUtil.parseDate("2025-06-23");
        assertNotNull(result);
        assertEquals(LocalDate.of(2025, 6, 23), result);
    }

    @Test
    void testParseNullAndEmpty() {
        // 测试null和空字符串
        assertNull(DateTimeParseUtil.parseDate(null));
        assertNull(DateTimeParseUtil.parseDate(""));
        assertNull(DateTimeParseUtil.parseDate("   "));
        
        assertNull(DateTimeParseUtil.parseDateTime(null));
        assertNull(DateTimeParseUtil.parseDateTime(""));
        assertNull(DateTimeParseUtil.parseDateTime("   "));
    }

    @Test
    void testParseInvalidFormat() {
        // 测试无效格式
        assertNull(DateTimeParseUtil.parseDate("invalid-date"));
        assertNull(DateTimeParseUtil.parseDateTime("invalid-datetime"));
    }

    @Test
    void testParseVariousFormats() {
        // 测试各种可能的格式

        // 测试从实际错误中看到的格式
        LocalDate result1 = DateTimeParseUtil.parseDate("2025-06-23 00:00:13");
        assertNotNull(result1);
        assertEquals(LocalDate.of(2025, 6, 23), result1);

        // 测试带时区的格式
        LocalDate result2 = DateTimeParseUtil.parseDate("2025-06-23 +08");
        assertNotNull(result2);
        assertEquals(LocalDate.of(2025, 6, 23), result2);

        // 测试ISO格式
        LocalDateTime result3 = DateTimeParseUtil.parseDateTime("2025-06-23T00:00:13");
        assertNotNull(result3);
        assertEquals(LocalDateTime.of(2025, 6, 23, 0, 0, 13), result3);

        // 测试带毫秒的格式
        LocalDateTime result4 = DateTimeParseUtil.parseDateTime("2025-06-23 00:00:13.123");
        assertNotNull(result4);
        assertEquals(LocalDateTime.of(2025, 6, 23, 0, 0, 13, 123000000), result4);
    }
}

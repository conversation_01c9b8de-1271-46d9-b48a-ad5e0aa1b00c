package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.common.exception.BusinessException;
import com.vfdata.pujiang_vfd.modules.factory.dto.ProjectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.ProjectClassification;
import com.vfdata.pujiang_vfd.modules.factory.repository.ProjectClassificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProjectClassificationService {

    private final ProjectClassificationRepository projectClassificationRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public List<ProjectClassificationDTO> getProjectClassifications(String projectName) {
        List<ProjectClassification> classifications;
        
        if (projectName != null && !projectName.isEmpty()) {
            classifications = projectClassificationRepository.findByProjectName(projectName);
        } else {
            classifications = projectClassificationRepository.findAllOrderByRecordDateDesc();
        }
        
        if (classifications.isEmpty()) {
            throw new BusinessException("未找到项目分类记录");
        }
        
        return classifications.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private ProjectClassificationDTO convertToDTO(ProjectClassification entity) {
        ProjectClassificationDTO dto = new ProjectClassificationDTO();
        dto.setId(entity.getId());
        dto.setProject_name(entity.getProjectName());
        dto.setMass_production_count(entity.getMassProductionCount());
        dto.setDevelopment_count(entity.getDevelopmentCount());
        
        if (entity.getRecordDate() != null) {
            dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        }
        if (entity.getUpdatedAt() != null) {
            dto.setUpdated_at(entity.getUpdatedAt().toString());
        }
        
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
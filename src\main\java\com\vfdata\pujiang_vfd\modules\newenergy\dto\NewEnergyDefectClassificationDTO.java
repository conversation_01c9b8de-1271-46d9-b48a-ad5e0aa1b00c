package com.vfdata.pujiang_vfd.modules.newenergy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "新能源车间抽检不良分类DTO")
public class NewEnergyDefectClassificationDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "分类名称")
    private String classification_name;

    @Schema(description = "分类数量")
    private Integer defect_count;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

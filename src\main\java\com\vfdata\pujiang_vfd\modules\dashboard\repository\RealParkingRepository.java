package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.RealParking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RealParkingRepository extends JpaRepository<RealParking, Long> {
    
    @Query("SELECT r FROM RealParking r ORDER BY r.recordDate DESC, r.id DESC")
    List<RealParking> findAllOrderByRecordDateDesc();
    
    @Query("SELECT r FROM RealParking r WHERE r.recordDate = (SELECT MAX(r2.recordDate) FROM RealParking r2)")
    List<RealParking> findLatestRecords();
}

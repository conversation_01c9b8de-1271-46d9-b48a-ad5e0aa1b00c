package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "ioc_qc_workshop_personnel_info")
@EqualsAndHashCode(callSuper = true)
public class WorkshopPersonnelInfo extends BaseEntity {
    
    @Column(name = "workshop_name", length = 20)
    private String workshopName;
    
    @Column(name = "total_worker_count")
    private Integer totalWorkerCount;
    
    @Column(name = "total_clerical_count")
    private Integer totalClericalCount;
} 
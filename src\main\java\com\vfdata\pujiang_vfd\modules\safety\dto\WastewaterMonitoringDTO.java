package com.vfdata.pujiang_vfd.modules.safety.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Schema(description = "废水监控DTO")
public class WastewaterMonitoringDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "COD值")
    private BigDecimal cod;

    @Schema(description = "氨氮值")
    private BigDecimal ammonia_nitrogen;

    @Schema(description = "记录时间")
    private String record_time;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新日期")
    private String update_date;
}

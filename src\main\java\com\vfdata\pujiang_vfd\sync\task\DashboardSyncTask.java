package com.vfdata.pujiang_vfd.sync.task;

import com.vfdata.pujiang_vfd.modules.dashboard.repository.*;
import com.vfdata.pujiang_vfd.sync.mapper.dashboard.*;
import com.vfdata.pujiang_vfd.sync.service.DataSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "data-sync.enabled", havingValue = "true")
public class DashboardSyncTask {

    private final DataSyncService dataSyncService;

    // Dashboard repositories
    private final EmployeeInOutRepository employeeInOutRepository;
    private final RealParkingRepository realParkingRepository;
    private final PrivateRoomRepository privateRoomRepository;
    private final HostelRepository hostelRepository;
    private final VenuesRepository venuesRepository;
    private final StaffVisitorRepository staffVisitorRepository;

    // Dashboard mappers
    private final EmployeeInOutMapper employeeInOutMapper;
    private final RealParkingMapper realParkingMapper;
    private final PrivateRoomMapper privateRoomMapper;
    private final HostelMapper hostelMapper;
    private final VenuesMapper venuesMapper;
    private final StaffVisitorMapper staffVisitorMapper;

    /**
     * 同步员工进出记录
     */
    public DataSyncService.SyncResult syncEmployeeInOut() {
        log.info("开始同步员工进出记录");
        return dataSyncService.syncData(
            "/ioc/bigScreen/employeeInout",
            employeeInOutRepository,
            employeeInOutMapper
        );
    }

    /**
     * 同步实时停车信息
     */
    public DataSyncService.SyncResult syncRealParking() {
        log.info("开始同步实时停车信息");
        return dataSyncService.syncData(
            "/ioc/bigScreen/realParking",
            realParkingRepository,
            realParkingMapper
        );
    }

    /**
     * 同步包间信息
     */
    public DataSyncService.SyncResult syncPrivateRoom() {
        log.info("开始同步包间信息");
        return dataSyncService.syncData(
            "/ioc/bigScreen/privateRoom",
            privateRoomRepository,
            privateRoomMapper
        );
    }

    /**
     * 同步宿舍信息
     */
    public DataSyncService.SyncResult syncHostel() {
        log.info("开始同步宿舍信息");
        return dataSyncService.syncData(
            "/ioc/bigScreen/hostel",
            hostelRepository,
            hostelMapper
        );
    }

    /**
     * 同步娱乐会议场所信息
     */
    public DataSyncService.SyncResult syncVenues() {
        log.info("开始同步娱乐会议场所信息");
        return dataSyncService.syncData(
            "/ioc/bigScreen/venues",
            venuesRepository,
            venuesMapper
        );
    }

    /**
     * 同步员工访客信息
     */
    public DataSyncService.SyncResult syncStaffVisitor() {
        log.info("开始同步员工访客信息");
        return dataSyncService.syncData(
            "/ioc/bigScreen/staffVisitor",
            staffVisitorRepository,
            staffVisitorMapper
        );
    }

    /**
     * 同步所有Dashboard模块数据
     */
    public List<DataSyncService.SyncResult> syncAll() {
        log.info("开始同步所有Dashboard模块数据");
        List<DataSyncService.SyncResult> results = new ArrayList<>();
        
        try {
            // 同步员工进出记录
            results.add(syncEmployeeInOut());

            // 同步实时停车信息
            results.add(syncRealParking());

            // 同步包间信息
            results.add(syncPrivateRoom());

            // 同步宿舍信息
            results.add(syncHostel());

            // 同步娱乐会议场所信息
            results.add(syncVenues());

            // 同步员工访客信息
            results.add(syncStaffVisitor());

        } catch (Exception e) {
            log.error("Dashboard模块数据同步失败: {}", e.getMessage(), e);
        }
        
        return results;
    }
}

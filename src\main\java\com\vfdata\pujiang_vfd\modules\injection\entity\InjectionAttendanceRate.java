package com.vfdata.pujiang_vfd.modules.injection.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zscj_attendance_rate")
@Schema(description = "注塑车间出勤率")
public class InjectionAttendanceRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "clerk_attendance_rate", nullable = false, precision = 5, scale = 2)
    @Schema(description = "职员出勤率")
    private BigDecimal clerkAttendanceRate;

    @Column(name = "worker_attendance_rate", nullable = false, precision = 5, scale = 2)
    @Schema(description = "普工出勤率")
    private BigDecimal workerAttendanceRate;

    @Column(name = "record_date", nullable = false)
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @UpdateTimestamp
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
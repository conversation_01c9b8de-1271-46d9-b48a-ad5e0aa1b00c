package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.FireEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface FireEquipmentInfoRepository extends JpaRepository<FireEquipmentInfo, Long> {
    
    List<FireEquipmentInfo> findAllByOrderByRecordDateDesc();
}

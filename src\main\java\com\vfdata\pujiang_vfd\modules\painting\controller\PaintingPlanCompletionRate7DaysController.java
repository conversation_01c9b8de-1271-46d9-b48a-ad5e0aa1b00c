package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingPlanCompletionRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/plan-completion-rate-7days")
@RequiredArgsConstructor
public class PaintingPlanCompletionRate7DaysController {

    private final PaintingPlanCompletionRate7DaysService planCompletionRate7DaysService;

    @GetMapping
    @Operation(summary = "获取喷涂车间计划达成率近7天数据", description = "根据车间名称获取指定喷涂车间计划达成率近7天数据")
    public ResponseUtils.Result<List<PaintingPlanCompletionRate7DaysDTO>> getPlanCompletionRate7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<PaintingPlanCompletionRate7DaysDTO> dataList = planCompletionRate7DaysService.getPlanCompletionRate7Days(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间计划达成率7天数据失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionEnvironmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface InjectionEnvironmentInfoRepository extends JpaRepository<InjectionEnvironmentInfo, Long> {
    
    @Query("SELECT i FROM InjectionEnvironmentInfo i WHERE i.recordDate = (SELECT MAX(i2.recordDate) FROM InjectionEnvironmentInfo i2)")
    Optional<InjectionEnvironmentInfo> findLatest();
} 
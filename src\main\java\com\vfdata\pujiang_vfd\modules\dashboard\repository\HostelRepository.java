package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.Hostel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface HostelRepository extends JpaRepository<Hostel, Long> {
    
    @Query("SELECT h FROM Hostel h WHERE h.id = (SELECT MAX(h2.id) FROM Hostel h2)")
    Optional<Hostel> findLatest();
}

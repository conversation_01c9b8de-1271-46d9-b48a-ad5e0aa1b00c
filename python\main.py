# -*- coding: utf-8 -*-

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from routers import injection_router, assembly_router, safety_router, factory_router, dashboard_router, data_collector
from middleware.logging import LoggingMiddleware
from utils.logger import logger
from config import settings  # 只导入 settings
import threading
import time
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
import subprocess
import sys
import os

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加日志中间件
app.add_middleware(LoggingMiddleware)

# 注册路由
app.include_router(injection_router.router, prefix=settings.API_V1_STR)
app.include_router(assembly_router.router, prefix=settings.API_V1_STR)
app.include_router(safety_router.router, prefix=settings.API_V1_STR)
app.include_router(factory_router.router, prefix=settings.API_V1_STR)
app.include_router(dashboard_router.router, prefix=settings.API_V1_STR)
app.include_router(data_collector.router, prefix=settings.API_V1_STR)

# 创建后台调度器
scheduler = BackgroundScheduler()

def run_sync_job():
    """执行同步任务"""
    try:
        logger.info(f"开始执行同步任务，时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 构建run_sync.py的完整路径
        sync_script = os.path.join(current_dir, "run_sync.py")
        
        # 检查脚本是否存在
        if not os.path.exists(sync_script):
            logger.error(f"同步脚本不存在: {sync_script}")
            return
            
        logger.info(f"执行脚本: {sync_script}")
        
        # 执行同步脚本
        result = subprocess.run(
            [sys.executable, sync_script], 
            capture_output=True, 
            text=True,
            timeout=1800  # 设置30分钟超时
        )
        
        if result.returncode == 0:
            logger.info("同步任务执行成功")
        else:
            logger.error(f"同步任务执行失败，返回码: {result.returncode}")
            if result.stderr:
                logger.error(f"错误信息: {result.stderr[:500]}...")
    except subprocess.TimeoutExpired:
        logger.error("同步任务执行超时（30分钟）")
    except Exception as e:
        logger.error(f"执行同步任务时发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

@app.on_event("startup")
async def startup_event():
    logger.info("应用启动")
    logger.info(f"数据库URL: {settings.SQLALCHEMY_DATABASE_URL}")
    
    # 添加定时任务 - 每10分钟执行一次
    scheduler.add_job(
        run_sync_job,
        IntervalTrigger(minutes=2),
        id='sync_job',
        replace_existing=True
    )
    
    # 启动调度器
    scheduler.start()
    logger.info("数据同步调度器已启动，同步间隔: 10分钟")
    
    # 立即执行一次同步任务（可选）
    # threading.Thread(target=run_sync_job).start()

@app.on_event("shutdown")
async def shutdown_event():
    # 关闭调度器
    scheduler.shutdown()
    logger.info("数据同步调度器已关闭")
    logger.info("应用关闭")

@app.get("/")
async def root():
    return JSONResponse(
        content={
            "message": "Welcome to FastAPI",
            "docs_url": f"{settings.API_V1_STR}/docs",
            "openapi_url": f"{settings.API_V1_STR}/openapi.json"
        }
    ) 

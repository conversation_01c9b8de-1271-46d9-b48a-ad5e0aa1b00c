package com.vfdata.pujiang_vfd.modules.injection.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zscj_environment_info")
@Schema(description = "注塑车间环境信息")
public class InjectionEnvironmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "无尘车间等级")
    @Column(name = "dust_free_workshop_level")
    private String dustFreeWorkshopLevel;

    @Schema(description = "平均湿度")
    @Column(name = "average_humidity")
    private BigDecimal averageHumidity;

    @Schema(description = "平均温度")
    @Column(name = "average_temperature")
    private BigDecimal averageTemperature;

    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;

    @Schema(description = "更新人")
    @Column(name = "updated_by")
    private String updatedBy;

    @Schema(description = "更新时间")
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Schema(description = "预留字段1")
    @Column(name = "reserved_field1")
    private String reservedField1;

    @Schema(description = "预留字段2")
    @Column(name = "reserved_field2")
    private String reservedField2;

    @Schema(description = "预留字段3")
    @Column(name = "reserved_field3")
    private String reservedField3;
} 
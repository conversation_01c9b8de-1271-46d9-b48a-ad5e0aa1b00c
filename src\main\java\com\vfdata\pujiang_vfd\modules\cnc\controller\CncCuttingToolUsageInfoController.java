package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncCuttingToolUsageInfoDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncCuttingToolUsageInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/cuttingtool-usage")
@RequiredArgsConstructor
public class CncCuttingToolUsageInfoController {

    private final CncCuttingToolUsageInfoService cuttingToolUsageInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间刀具消耗指数", description = "获取每个CNC车间最新的刀具消耗指数信息")
    public ResponseUtils.Result<List<CncCuttingToolUsageInfoDTO>> getLatestCuttingToolUsageInfo() {
        try {
            List<CncCuttingToolUsageInfoDTO> data = cuttingToolUsageInfoService.getLatestCuttingToolUsageInfo();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间刀具消耗信息失败：" + e.getMessage());
        }
    }
}

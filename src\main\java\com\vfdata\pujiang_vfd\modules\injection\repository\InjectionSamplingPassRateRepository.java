package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionSamplingPassRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionSamplingPassRateRepository extends JpaRepository<InjectionSamplingPassRate, Long> {
    
    @Query(value = "SELECT DISTINCT ON (workshop_name) * FROM ioc_zscj_sampling_inspection_pass_rate ORDER BY workshop_name, record_date DESC, id DESC", nativeQuery = true)
    List<InjectionSamplingPassRate> findLatestByWorkshop();
} 
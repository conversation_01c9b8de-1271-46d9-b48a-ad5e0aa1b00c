package com.vfdata.pujiang_vfd.modules.newenergy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "新能源车间出勤率DTO")
public class NewEnergyAttendanceRateDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "职员出勤率")
    private BigDecimal clerkAttendanceRate;

    @Schema(description = "普工出勤率")
    private BigDecimal workerAttendanceRate;

    @Schema(description = "记录日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate recordDate;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "备用字段1")
    private String reservedField1;

    @Schema(description = "备用字段2")
    private String reservedField2;

    @Schema(description = "备用字段3")
    private String reservedField3;

    @Schema(description = "职员出勤人数")
    private Integer clerkAttendanceNum;

    @Schema(description = "普工出勤人数")
    private Integer workerAttendanceNum;
}

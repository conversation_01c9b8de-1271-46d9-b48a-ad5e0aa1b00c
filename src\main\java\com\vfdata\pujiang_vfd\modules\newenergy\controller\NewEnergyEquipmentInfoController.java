package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyEquipmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/equipment-info")
@RequiredArgsConstructor
public class NewEnergyEquipmentInfoController {

    private final NewEnergyEquipmentInfoService equipmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新设备信息")
    public ResponseUtils.Result<NewEnergyEquipmentInfoDTO> getLatestEquipmentInfo() {
        try {
            NewEnergyEquipmentInfoDTO info = equipmentInfoService.getLatestEquipmentInfo();
            return ResponseUtils.success(info);
        } catch (Exception e) {
            return ResponseUtils.error("获取设备信息失败：" + e.getMessage());
        }
    }
}

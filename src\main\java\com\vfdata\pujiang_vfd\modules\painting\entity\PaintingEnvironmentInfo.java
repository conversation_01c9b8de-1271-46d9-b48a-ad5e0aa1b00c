package com.vfdata.pujiang_vfd.modules.painting.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_ptcj_environment_info")
@Schema(description = "喷涂车间环境信息")
public class PaintingEnvironmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "dust_free_workshop_level")
    @Schema(description = "无尘车间等级")
    private String dustFreeWorkshopLevel;

    @Column(name = "average_humidity")
    @Schema(description = "车间平均湿度")
    private BigDecimal averageHumidity;

    @Column(name = "average_temperature")
    @Schema(description = "车间平均温度")
    private BigDecimal averageTemperature;

    @Column(name = "workshop_name")
    @Schema(description = "车间名称")
    private String workshopName;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

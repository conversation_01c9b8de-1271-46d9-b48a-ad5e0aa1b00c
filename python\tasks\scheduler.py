from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from datetime import datetime
from utils.logger import logger
from .data_sync import run_sync

def start_scheduler():
    """启动定时任务调度器"""
    scheduler = AsyncIOScheduler()
    
    # 添加数据同步任务，每天凌晨2点执行
    scheduler.add_job(
        run_sync,
        trigger=CronTrigger(hour=2, minute=0),
        id='data_sync',
        name='数据同步任务',
        replace_existing=True
    )
    
    # 启动调度器
    try:
        scheduler.start()
        logger.info("定时任务调度器已启动")
    except Exception as e:
        logger.error(f"启动定时任务调度器失败: {str(e)}")
        raise 
package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.PrivateRoom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface PrivateRoomRepository extends JpaRepository<PrivateRoom, Long> {
    
    @Query("SELECT p FROM PrivateRoom p WHERE p.id = (SELECT MAX(p2.id) FROM PrivateRoom p2)")
    Optional<PrivateRoom> findLatest();
}

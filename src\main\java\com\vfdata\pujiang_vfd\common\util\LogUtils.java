package com.vfdata.pujiang_vfd.common.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志工具类
 */
@Slf4j
public class LogUtils {
    
    /**
     * 获取指定类的日志记录器
     */
    public static Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }

    /**
     * 获取指定名称的日志记录器
     */
    public static Logger getLogger(String name) {
        return LoggerFactory.getLogger(name);
    }

    /**
     * 记录调试日志
     */
    public static void debug(String message) {
        log.debug(message);
    }

    /**
     * 记录调试日志（带参数）
     */
    public static void debug(String format, Object... arguments) {
        log.debug(format, arguments);
    }

    /**
     * 记录信息日志
     */
    public static void info(String message) {
        log.info(message);
    }

    /**
     * 记录信息日志（带参数）
     */
    public static void info(String format, Object... arguments) {
        log.info(format, arguments);
    }

    /**
     * 记录警告日志
     */
    public static void warn(String message) {
        log.warn(message);
    }

    /**
     * 记录警告日志（带参数）
     */
    public static void warn(String format, Object... arguments) {
        log.warn(format, arguments);
    }

    /**
     * 记录错误日志
     */
    public static void error(String message) {
        log.error(message);
    }

    /**
     * 记录错误日志（带参数）
     */
    public static void error(String format, Object... arguments) {
        log.error(format, arguments);
    }

    /**
     * 记录错误日志（带异常）
     */
    public static void error(String message, Throwable throwable) {
        log.error(message, throwable);
    }

    /**
     * 记录错误日志（带异常和参数）
     */
    public static void error(String format, Throwable throwable, Object... arguments) {
        log.error(format, arguments, throwable);
    }

    public static void trace(String message) {
        log.trace(message);
    }

    public static void trace(String format, Object... args) {
        log.trace(format, args);
    }
} 
package com.vfdata.pujiang_vfd.modules.safety.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_szhaq_visitor_records")
@Schema(description = "访客记录")
public class VisitorRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "visitor_name", length = 100)
    @Schema(description = "访客姓名")
    @JsonProperty("visitor_name")
    private String visitorName;

    @Column(name = "channel_name", length = 100)
    @Schema(description = "通道名称")
    @JsonProperty("channel_name")
    private String channelName;

    @Column(name = "visit_time")
    @Schema(description = "访问时间")
    @JsonProperty("visit_time")
    private LocalDateTime visitTime;

    @Column(name = "status", length = 50)
    @Schema(description = "状态（进/出）")
    private String status;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    @JsonProperty("updated_by")
    private String updatedBy;

    @Column(name = "update_date")
    @Schema(description = "更新日期")
    @JsonProperty("update_date")
    private LocalDate updateDate;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
}

package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaintingEquipmentInfoService {

    private final PaintingEquipmentInfoRepository equipmentInfoRepository;

    /**
     * 获取最新的设备信息
     */
    public PaintingEquipmentInfoDTO getLatestEquipmentInfo() {
        PaintingEquipmentInfo equipmentInfo = equipmentInfoRepository.findLatestRecord();
        if (equipmentInfo == null) {
            return null;
        }
        return convertToDTO(equipmentInfo);
    }

    /**
     * 实体转DTO
     */
    private PaintingEquipmentInfoDTO convertToDTO(PaintingEquipmentInfo entity) {
        PaintingEquipmentInfoDTO dto = new PaintingEquipmentInfoDTO();
        dto.setId(entity.getId());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setTotal_equipment(entity.getTotalEquipmentCount());
        dto.setOperating_equipment(entity.getOperatingEquipmentCount());
        dto.setOee_rate(entity.getOeeRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

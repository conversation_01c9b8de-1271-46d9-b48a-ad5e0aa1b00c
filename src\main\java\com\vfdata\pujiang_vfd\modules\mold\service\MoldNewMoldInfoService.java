package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldNewMoldInfoDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldNewMoldInfo;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldNewMoldInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldNewMoldInfoService {

    private final MoldNewMoldInfoRepository newMoldInfoRepository;

    /**
     * 获取新模信息数据
     * @param period 周期类型：month返回6条，day返回7条
     */
    public List<MoldNewMoldInfoDTO> getNewMoldInfoByPeriod(String period) {
        int limit = "day".equals(period) ? 7 : 6;
        List<MoldNewMoldInfo> entities = newMoldInfoRepository.findLatestRecordsByPeriod(period, limit);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldNewMoldInfoDTO convertToDTO(MoldNewMoldInfo entity) {
        MoldNewMoldInfoDTO dto = new MoldNewMoldInfoDTO();
        dto.setId(entity.getId());
        dto.setPeriod(entity.getPeriod());
        dto.setPujian_count(entity.getPujiangCount());
        dto.setBoluo_count(entity.getBoluoCount());
        dto.setPujian_ratio(entity.getPujiangRatio());
        dto.setBoluo_ratio(entity.getBoluoRatio());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

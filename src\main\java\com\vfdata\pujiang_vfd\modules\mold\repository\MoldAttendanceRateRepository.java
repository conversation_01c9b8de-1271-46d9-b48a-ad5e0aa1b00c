package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldAttendanceRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MoldAttendanceRateRepository extends JpaRepository<MoldAttendanceRate, Long> {

    /**
     * 获取最新的出勤率记录
     */
    @Query("SELECT m FROM MoldAttendanceRate m WHERE m.recordDate = (SELECT MAX(m2.recordDate) FROM MoldAttendanceRate m2)")
    MoldAttendanceRate findLatestRecord();
}

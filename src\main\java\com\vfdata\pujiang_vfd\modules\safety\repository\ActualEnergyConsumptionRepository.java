package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.ActualEnergyConsumption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ActualEnergyConsumptionRepository extends JpaRepository<ActualEnergyConsumption, Long> {
    
    List<ActualEnergyConsumption> findAllByOrderByRecordTimeDesc();
}

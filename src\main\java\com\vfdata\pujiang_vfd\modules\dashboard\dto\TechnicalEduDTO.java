package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "技术人员学历信息DTO")
public class TechnicalEduDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "学历背景")
    private String edu_background;

    @Schema(description = "人数")
    private Integer edu_num;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyAttendanceRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface AssemblyAttendanceRateRepository extends JpaRepository<AssemblyAttendanceRate, Long> {
    
    @Query("SELECT a FROM AssemblyAttendanceRate a WHERE a.recordDate = (SELECT MAX(a2.recordDate) FROM AssemblyAttendanceRate a2)")
    Optional<AssemblyAttendanceRate> findLatest();
} 
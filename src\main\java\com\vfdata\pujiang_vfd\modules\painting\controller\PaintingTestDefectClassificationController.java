package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingTestDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingTestDefectClassificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/defect-classification")
@RequiredArgsConstructor
public class PaintingTestDefectClassificationController {

    private final PaintingTestDefectClassificationService defectClassificationService;

    @GetMapping
    @Operation(summary = "获取喷涂车间品质不良分类", description = "获取指定车间或所有车间的品质不良分类数据")
    public ResponseUtils.Result<List<PaintingTestDefectClassificationDTO>> getDefectClassification(
            @Parameter(description = "车间名称，可选参数") 
            @RequestParam(required = false) String workshop_name) {
        try {
            List<PaintingTestDefectClassificationDTO> dataList = defectClassificationService.getDefectClassification(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间品质不良分类数据失败：" + e.getMessage());
        }
    }
}

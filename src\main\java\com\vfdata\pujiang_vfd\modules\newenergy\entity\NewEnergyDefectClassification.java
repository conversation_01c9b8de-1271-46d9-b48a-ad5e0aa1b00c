package com.vfdata.pujiang_vfd.modules.newenergy.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_xnycj_cj_defect_classification")
@Schema(description = "新能源车间抽检不良分类")
public class NewEnergyDefectClassification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "classification_name", length = 50)
    @Schema(description = "分类名称")
    private String classificationName;

    @Column(name = "defect_count")
    @Schema(description = "分类数量")
    private Integer defectCount;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "所属车间")
    private String workshopName;

    @Column(name = "record_date")
    @Schema(description = "日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "备用字段3")
    private String reservedField3;
}

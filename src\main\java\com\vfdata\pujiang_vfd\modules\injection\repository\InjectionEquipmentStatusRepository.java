package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionEquipmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionEquipmentStatusRepository extends JpaRepository<InjectionEquipmentStatus, Long> {
    
    @Query("SELECT e FROM InjectionEquipmentStatus e WHERE e.workshopName = :workshopName ORDER BY e.recordDate DESC, e.id DESC")
    List<InjectionEquipmentStatus> findByWorkshopName(@Param("workshopName") String workshopName);

    @Query("SELECT e FROM InjectionEquipmentStatus e ORDER BY e.workshopName, e.recordDate DESC, e.id DESC")
    List<InjectionEquipmentStatus> findAllStatus();

    @Query("SELECT e FROM InjectionEquipmentStatus e WHERE e.recordDate = (SELECT MAX(e2.recordDate) FROM InjectionEquipmentStatus e2)")
    List<InjectionEquipmentStatus> findLatestDistribution();

    @Query("SELECT e FROM InjectionEquipmentStatus e WHERE e.recordDate = (SELECT MAX(e2.recordDate) FROM InjectionEquipmentStatus e2) AND e.workshopName = :workshopName")
    List<InjectionEquipmentStatus> findLatestDistributionByWorkshopName(@Param("workshopName") String workshopName);
} 
package com.vfdata.pujiang_vfd.modules.newenergy.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_xnycj_attendance_rate")
@Schema(description = "新能源车间出勤率")
public class NewEnergyAttendanceRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "clerk_attendance_rate", precision = 5, scale = 2)
    @Schema(description = "职员出勤率")
    private BigDecimal clerkAttendanceRate;

    @Column(name = "worker_attendance_rate", precision = 5, scale = 2)
    @Schema(description = "普工出勤率")
    private BigDecimal workerAttendanceRate;

    @Column(name = "record_date")
    @Schema(description = "日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;

    @Column(name = "clerk_attendance_num")
    @Schema(description = "职员出勤人数")
    private Integer clerkAttendanceNum;

    @Column(name = "worker_attendance_num")
    @Schema(description = "普工出勤人数")
    private Integer workerAttendanceNum;
}

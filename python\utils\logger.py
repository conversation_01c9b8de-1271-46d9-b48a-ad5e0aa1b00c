import logging
import os
from logging.handlers import RotatingFileHandler
from config import settings

class Logger:
    def __init__(self):
        # 创建日志目录
        log_dir = os.path.dirname(settings.LOG_FILE)
        os.makedirs(log_dir, exist_ok=True)

        # 创建logger
        self.logger = logging.getLogger('app')
        self.logger.setLevel(settings.LOG_LEVEL)

        # 创建处理器
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(settings.LOG_LEVEL)

        # 文件处理器
        file_handler = RotatingFileHandler(
            settings.LOG_FILE,
            maxBytes=settings.LOG_FILE_SIZE,
            backupCount=settings.LOG_FILE_COUNT,
            encoding='utf-8'
        )
        file_handler.setLevel(settings.LOG_LEVEL)

        # 错误日志处理器
        error_handler = RotatingFileHandler(
            settings.LOG_FILE_ERROR,
            maxBytes=settings.LOG_FILE_SIZE,
            backupCount=settings.LOG_FILE_COUNT,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)

        # 设置日志格式
        formatter = logging.Formatter(settings.LOG_FORMAT)
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)

    def get_logger(self):
        return self.logger

# 创建全局日志记录器实例
logger = Logger().get_logger() 
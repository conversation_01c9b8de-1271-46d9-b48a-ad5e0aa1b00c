package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncQualityRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/quality-rate")
@RequiredArgsConstructor
public class CncQualityRateController {

    private final CncQualityRateService qualityRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间巡检检验合格率", description = "获取每个CNC车间最新的巡检检验合格率信息")
    public ResponseUtils.Result<List<CncQualityRateDTO>> getLatestQualityRate() {
        try {
            List<CncQualityRateDTO> data = qualityRateService.getLatestQualityRate();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间质量率信息失败：" + e.getMessage());
        }
    }
}

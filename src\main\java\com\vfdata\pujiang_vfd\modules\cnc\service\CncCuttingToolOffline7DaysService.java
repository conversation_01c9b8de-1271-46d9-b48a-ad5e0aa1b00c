package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncCuttingToolOffline7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncCuttingToolOffline7Days;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncCuttingToolOffline7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncCuttingToolOffline7DaysService {

    private final CncCuttingToolOffline7DaysRepository cuttingToolOffline7DaysRepository;

    /**
     * 获取指定车间的刀具近7天下机原因数据
     */
    public List<CncCuttingToolOffline7DaysDTO> getCuttingToolOffline7Days(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<CncCuttingToolOffline7Days> entities = cuttingToolOffline7DaysRepository.findLatest7RecordsByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncCuttingToolOffline7DaysDTO convertToDTO(CncCuttingToolOffline7Days entity) {
        CncCuttingToolOffline7DaysDTO dto = new CncCuttingToolOffline7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setOffline_name(entity.getOfflineName());
        dto.setOffline_count(entity.getOfflineCount());
        dto.setOffline_rate(entity.getOfflineRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

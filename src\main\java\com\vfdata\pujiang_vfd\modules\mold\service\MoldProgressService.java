package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldProgressDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldProgress;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldProgressRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldProgressService {

    private final MoldProgressRepository moldProgressRepository;

    /**
     * 获取最新的模具进度数据
     */
    public List<MoldProgressDTO> getLatestMoldProgress() {
        List<MoldProgress> entities = moldProgressRepository.findLatestRecords();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldProgressDTO convertToDTO(MoldProgress entity) {
        MoldProgressDTO dto = new MoldProgressDTO();
        dto.setId(entity.getId());
        dto.setMold_number(entity.getMoldNumber());
        dto.setCurrent_process(entity.getCurrentProcess());
        dto.setProcessing_leadtime(entity.getProcessingLeadtime());
        dto.setComplete_date(entity.getCompleteDate());
        dto.setAssembly_delivery_time(entity.getAssemblyDeliveryTime());
        dto.setCurrent_progress(entity.getCurrentProgress());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "员工访客统计响应DTO")
public class StaffVisitorStatisticsDTO {

    @Schema(description = "统计数据列表")
    private List<StatisticItem> statistics;

    @Data
    @Schema(description = "统计项")
    public static class StatisticItem {
        @Schema(description = "类型")
        private String type;

        @Schema(description = "总数")
        private Integer total;
    }
}

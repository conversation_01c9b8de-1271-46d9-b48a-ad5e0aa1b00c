package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface InjectionEquipmentInfoRepository extends JpaRepository<InjectionEquipmentInfo, Long> {
    
    @Query("SELECT i FROM InjectionEquipmentInfo i WHERE i.recordDate = (SELECT MAX(i2.recordDate) FROM InjectionEquipmentInfo i2)")
    Optional<InjectionEquipmentInfo> findLatest();
} 
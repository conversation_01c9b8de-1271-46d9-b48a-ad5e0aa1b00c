package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.FactoryPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 全厂人员信息Repository
 */
@Repository
public interface FactoryPersonnelInfoRepository extends JpaRepository<FactoryPersonnelInfo, Long> {

    /**
     * 获取最新记录
     */
    @Query("SELECT f FROM FactoryPersonnelInfo f WHERE f.recordDate = (SELECT MAX(f2.recordDate) FROM FactoryPersonnelInfo f2)")
    List<FactoryPersonnelInfo> findLatestRecords();
} 
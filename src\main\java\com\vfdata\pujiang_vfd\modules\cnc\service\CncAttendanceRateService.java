package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncAttendanceRate;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncAttendanceRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CncAttendanceRateService {

    private final CncAttendanceRateRepository attendanceRateRepository;

    /**
     * 获取最新的出勤率信息
     */
    public CncAttendanceRateDTO getLatestAttendanceRate() {
        CncAttendanceRate attendanceRate = attendanceRateRepository.findLatestRecord();
        if (attendanceRate == null) {
            return null;
        }
        return convertToDTO(attendanceRate);
    }

    /**
     * 实体转DTO
     */
    private CncAttendanceRateDTO convertToDTO(CncAttendanceRate entity) {
        CncAttendanceRateDTO dto = new CncAttendanceRateDTO();
        dto.setId(entity.getId());
        dto.setClerk_attendance_rate(entity.getClerkAttendanceRate());
        dto.setWorker_attendance_rate(entity.getWorkerAttendanceRate());
        dto.setClerk_attendance_num(entity.getClerkAttendanceNum());
        dto.setWorker_attendance_num(entity.getWorkerAttendanceNum());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

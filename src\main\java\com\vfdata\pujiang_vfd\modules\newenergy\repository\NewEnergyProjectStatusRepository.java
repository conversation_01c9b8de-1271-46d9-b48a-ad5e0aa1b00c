package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyProjectStatusRepository extends JpaRepository<NewEnergyProjectStatus, Long> {
    
    /**
     * 根据车间名称获取该车间的所有项目开机分布数据
     */
    @Query("SELECT n FROM NewEnergyProjectStatus n WHERE n.workshopName = :workshopName")
    List<NewEnergyProjectStatus> findByWorkshopName(@Param("workshopName") String workshopName);
}

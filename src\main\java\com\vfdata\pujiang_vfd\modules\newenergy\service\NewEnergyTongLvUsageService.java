package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.TongLvUsageDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyTongLvUsage;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyTongLvUsageRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyTongLvUsageService {

    private final NewEnergyTongLvUsageRepository tongLvUsageRepository;

    /**
     * 获取最新的7条铜铝用量记录
     */
    public List<TongLvUsageDTO> getLatest7Records() {
        List<NewEnergyTongLvUsage> records = tongLvUsageRepository.findTop7ByOrderByRecordDateDesc();
        
        if (records.isEmpty()) {
            throw new RuntimeException("未找到铜铝用量记录");
        }
        
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有铜铝用量记录（按日期降序）
     */
    public List<TongLvUsageDTO> getAllRecords() {
        List<NewEnergyTongLvUsage> records = tongLvUsageRepository.findAllByOrderByRecordDateDesc();
        
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据日期范围查询铜铝用量记录
     */
    public List<TongLvUsageDTO> getRecordsByDateRange(LocalDate startDate, LocalDate endDate) {
        List<NewEnergyTongLvUsage> records = tongLvUsageRepository
                .findByRecordDateBetweenOrderByRecordDateDesc(startDate, endDate);
        
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取最新记录日期的所有数据
     */
    public List<TongLvUsageDTO> getLatestRecords() {
        List<NewEnergyTongLvUsage> records = tongLvUsageRepository.findLatestRecords();
        
        if (records.isEmpty()) {
            throw new RuntimeException("未找到铜铝用量记录");
        }
        
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据指定日期查询数据
     */
    public List<TongLvUsageDTO> getRecordsByDate(LocalDate recordDate) {
        List<NewEnergyTongLvUsage> records = tongLvUsageRepository
                .findByRecordDateOrderByIdDesc(recordDate);
        
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private TongLvUsageDTO convertToDTO(NewEnergyTongLvUsage entity) {
        TongLvUsageDTO dto = new TongLvUsageDTO();
        dto.setId(entity.getId());
        dto.setTongUsage(entity.getTongUsage());
        dto.setLvUsage(entity.getLvUsage());
        dto.setRecordDate(entity.getRecordDate());
        dto.setUpdatedBy(entity.getUpdatedBy());
        return dto;
    }
}

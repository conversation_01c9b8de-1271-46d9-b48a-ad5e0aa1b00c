package com.vfdata.pujiang_vfd.modules.factory.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Schema(description = "发货趋势DTO")
@Getter
@Setter
public class ShipmentTrendDTO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "发货数量")
    private BigDecimal shipment_count;
    
    @Schema(description = "记录日期")
    private String record_date;
    
    @Schema(description = "更新人")
    private String updated_by;
    
    @Schema(description = "更新时间")
    private String updated_at;
    
    @Schema(description = "预留字段1")
    private String reserved_field1;
    
    @Schema(description = "预留字段2")
    private String reserved_field2;
    
    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NewEnergyPersonnelInfoRepository extends JpaRepository<NewEnergyPersonnelInfo, Long> {
    
    @Query("SELECT p FROM NewEnergyPersonnelInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM NewEnergyPersonnelInfo p2 WHERE p2.personnelType = :type) AND p.personnelType = :type")
    Optional<NewEnergyPersonnelInfo> findLatestByType(@Param("type") String type);

    @Query("SELECT p FROM NewEnergyPersonnelInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM NewEnergyPersonnelInfo p2)")
    List<NewEnergyPersonnelInfo> findLatest();
}

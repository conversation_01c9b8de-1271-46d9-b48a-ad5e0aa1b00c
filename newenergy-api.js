import request from './request.js'

// 新能源车间设备信息
export const equipment_info = async () => {
    return await request.get(`/newenergy/equipment-info/latest`);
};

// 新能源车间抽检不良分析
export const defect_analysis = async (workshop_name = '') => {
    const params = workshop_name ? `?workshop_name=${encodeURIComponent(workshop_name)}` : '';
    return await request.get(`/newenergy/defect-analysis${params}`);
};

// 新能源车间状态
export const workshop_status = async () => {
    return await request.get(`/newenergy/workshop-status`);
};

// 新能源车间日计划达成率
export const daily_achievement_rate = async () => {
    return await request.get(`/newenergy/daily-achievement-rate`);
};

// 新能源车间抽检检验合格率
export const quality_rate = async () => {
    return await request.get(`/newenergy/quality-rate`);
};

// 新能源车间出勤率
export const attendance_rate = async () => {
    return await request.get(`/newenergy/attendance/latest`);
};

// 新能源车间测试不良分类
export const test_defect_classification = async () => {
    return await request.get(`/newenergy/test-defect-classification`);
};

// 新能源车间性能测试良率
export const pass_rate = async () => {
    return await request.get(`/newenergy/pass-rate/latest`);
};

// 新能源车间项目开机分布
export const project_status = async (workshop_name) => {
    return await request.get(`/newenergy/project-status?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// 新能源车间抽检检验合格率近7天
export const quality_rate_7days = async (workshop_name) => {
    return await request.get(`/newenergy/quality-rate-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// 新能源车间计划达成率近7天
export const plan_completion_rate_7days = async (workshop_name) => {
    return await request.get(`/newenergy/plan-completion-rate-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// 新能源车间性能测试良率近7天
export const pass_rate_7days = async () => {
    return await request.get(`/newenergy/pass-rate-7days`);
};

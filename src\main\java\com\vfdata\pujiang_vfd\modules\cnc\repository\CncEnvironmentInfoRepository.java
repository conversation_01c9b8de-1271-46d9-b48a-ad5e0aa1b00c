package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncEnvironmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CncEnvironmentInfoRepository extends JpaRepository<CncEnvironmentInfo, Long> {

    /**
     * 获取最新的环境信息记录
     */
    @Query("SELECT c FROM CncEnvironmentInfo c WHERE c.recordDate = (SELECT MAX(c2.recordDate) FROM CncEnvironmentInfo c2)")
    CncEnvironmentInfo findLatestRecord();
}

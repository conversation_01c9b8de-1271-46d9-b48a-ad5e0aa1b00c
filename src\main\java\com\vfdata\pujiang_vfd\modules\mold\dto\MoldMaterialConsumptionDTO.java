package com.vfdata.pujiang_vfd.modules.mold.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "模具车间物料消耗DTO")
public class MoldMaterialConsumptionDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "物料类型")
    private String material_type;

    @Schema(description = "浦江消耗量")
    private Integer pujian_consumption;

    @Schema(description = "博罗消耗量")
    private Integer boluo_consumption;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

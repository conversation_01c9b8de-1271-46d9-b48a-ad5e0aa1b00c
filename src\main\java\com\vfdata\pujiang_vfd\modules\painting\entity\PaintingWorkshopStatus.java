package com.vfdata.pujiang_vfd.modules.painting.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_ptcj_workshop_status")
@Schema(description = "喷涂车间设备状况")
public class PaintingWorkshopStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name")
    @Schema(description = "车间名称")
    private String workshopName;

    @Column(name = "operating_rate")
    @Schema(description = "车间设备开机率")
    private BigDecimal operatingRate;

    @Column(name = "total_equipment_count")
    @Schema(description = "车间设备总数")
    private Integer totalEquipmentCount;

    @Column(name = "operating_equipment_count")
    @Schema(description = "车间当前运行设备数")
    private Integer operatingEquipmentCount;

    @Column(name = "record_date")
    @Schema(description = "数据统计日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

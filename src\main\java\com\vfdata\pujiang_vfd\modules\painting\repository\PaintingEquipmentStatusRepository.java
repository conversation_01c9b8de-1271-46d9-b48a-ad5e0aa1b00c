package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingEquipmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingEquipmentStatusRepository extends JpaRepository<PaintingEquipmentStatus, Long> {

    /**
     * 获取最新记录日期的所有数据
     */
    @Query("SELECT p FROM PaintingEquipmentStatus p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingEquipmentStatus p2)")
    List<PaintingEquipmentStatus> findLatestRecords();

    /**
     * 获取指定车间最新记录日期的数据
     */
    @Query("SELECT p FROM PaintingEquipmentStatus p WHERE p.workshopName = :workshopName AND p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingEquipmentStatus p2 WHERE p2.workshopName = :workshopName)")
    List<PaintingEquipmentStatus> findLatestRecordsByWorkshop(@Param("workshopName") String workshopName);
}

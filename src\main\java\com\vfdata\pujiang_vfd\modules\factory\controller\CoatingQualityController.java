package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.CoatingQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.CoatingQualityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/quality")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class CoatingQualityController {

    private final CoatingQualityService coatingQualityService;

    @GetMapping("/coating")
    @Operation(summary = "获取涂装巡检检验合格率", description = "获取最新的7条涂装巡检检验合格率数据")
    public ResponseUtils.Result<List<CoatingQualityDTO>> getCoatingQualityInfo() {
        List<CoatingQualityDTO> qualityInfo = coatingQualityService.getCoatingQualityInfo();
        return ResponseUtils.success(qualityInfo);
    }
} 
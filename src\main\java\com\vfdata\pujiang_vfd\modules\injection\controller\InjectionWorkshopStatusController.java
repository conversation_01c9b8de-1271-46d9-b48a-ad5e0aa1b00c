package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionWorkshopStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/workshop-status")
@RequiredArgsConstructor
public class InjectionWorkshopStatusController {

    private final InjectionWorkshopStatusService workshopStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取车间状态数据", description = "获取指定车间或所有车间的状态数据")
    public ResponseUtils.Result<List<InjectionWorkshopStatusDTO>> getWorkshopStatus(
            @Parameter(description = "车间名称") @RequestParam(required = false) String workshop_name) {
        try {
            List<InjectionWorkshopStatusDTO> statusList = workshopStatusService.getWorkshopStatus(workshop_name);
            return ResponseUtils.success(statusList);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("没有找到")) {
                return ResponseUtils.error(404, e.getMessage());
            }
            return ResponseUtils.error("获取车间状态数据失败：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取车间状态数据失败：" + e.getMessage());
        }
    }
} 
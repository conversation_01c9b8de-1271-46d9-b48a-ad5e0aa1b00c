package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncAttendanceRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/attendance-rate")
@RequiredArgsConstructor
public class CncAttendanceRateController {

    private final CncAttendanceRateService attendanceRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间出勤率", description = "获取CNC车间最新的职员和普工出勤率信息")
    public ResponseUtils.Result<CncAttendanceRateDTO> getLatestAttendanceRate() {
        try {
            CncAttendanceRateDTO data = attendanceRateService.getLatestAttendanceRate();
            if (data == null) {
                return ResponseUtils.success(new CncAttendanceRateDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间出勤率失败：" + e.getMessage());
        }
    }
}

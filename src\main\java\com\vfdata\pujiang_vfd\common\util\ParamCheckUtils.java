package com.vfdata.pujiang_vfd.common.util;

public class ParamCheckUtils {
    /**
     * 校验字符串参数是否为空，为空则返回错误信息。
     * @param value 参数值
     * @param paramName 参数名（用于提示）
     * @return 错误信息字符串，不为空表示校验失败
     */
    public static String checkRequired(String value, String paramName) {
        if (value == null || value.trim().isEmpty()) {
            return paramName + "不能为空";
        }
        return null;
    }
} 
package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingAttendanceRate;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingAttendanceRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaintingAttendanceRateService {

    private final PaintingAttendanceRateRepository attendanceRateRepository;

    /**
     * 获取最新的出勤率信息
     */
    public PaintingAttendanceRateDTO getLatestAttendanceRate() {
        PaintingAttendanceRate attendanceRate = attendanceRateRepository.findLatestRecord();
        if (attendanceRate == null) {
            return null;
        }
        return convertToDTO(attendanceRate);
    }

    /**
     * 实体转DTO
     */
    private PaintingAttendanceRateDTO convertToDTO(PaintingAttendanceRate entity) {
        PaintingAttendanceRateDTO dto = new PaintingAttendanceRateDTO();
        dto.setId(entity.getId());
        dto.setClerk_attendance_rate(entity.getClerkAttendanceRate());
        dto.setWorker_attendance_rate(entity.getWorkerAttendanceRate());
        dto.setClerk_attendance_num(entity.getClerkAttendanceNum());
        dto.setWorker_attendance_num(entity.getWorkerAttendanceNum());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

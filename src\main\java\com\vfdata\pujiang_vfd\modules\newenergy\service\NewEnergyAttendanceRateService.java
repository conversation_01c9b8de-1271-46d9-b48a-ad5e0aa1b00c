package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyAttendanceRate;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyAttendanceRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class NewEnergyAttendanceRateService {

    private final NewEnergyAttendanceRateRepository attendanceRateRepository;

    /**
     * 获取最新记录日期的出勤率数据
     */
    public NewEnergyAttendanceRateDTO getLatestAttendanceRate() {
        List<NewEnergyAttendanceRate> records = attendanceRateRepository.findLatestAttendanceRate();

        if (records.isEmpty()) {
            return null;
        }

        // 返回第一条记录（最新的）
        return convertToDTO(records.get(0));
    }

    /**
     * 实体转DTO
     */
    private NewEnergyAttendanceRateDTO convertToDTO(NewEnergyAttendanceRate entity) {
        NewEnergyAttendanceRateDTO dto = new NewEnergyAttendanceRateDTO();
        dto.setId(entity.getId());
        dto.setClerkAttendanceRate(entity.getClerkAttendanceRate());
        dto.setWorkerAttendanceRate(entity.getWorkerAttendanceRate());
        dto.setRecordDate(entity.getRecordDate());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setReservedField1(entity.getReservedField1());
        dto.setReservedField2(entity.getReservedField2());
        dto.setReservedField3(entity.getReservedField3());
        dto.setClerkAttendanceNum(entity.getClerkAttendanceNum());
        dto.setWorkerAttendanceNum(entity.getWorkerAttendanceNum());
        return dto;
    }
}

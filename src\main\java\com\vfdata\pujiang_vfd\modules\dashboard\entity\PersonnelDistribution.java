package com.vfdata.pujiang_vfd.modules.dashboard.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_dp_personnel_distribution")
@Schema(description = "厂区人员分布")
public class PersonnelDistribution {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "park_name", length = 20)
    @Schema(description = "园区名称")
    private String parkName;

    @Column(name = "total_num")
    @Schema(description = "园区总人数")
    private Integer totalNum;

    @Column(name = "manager_num")
    @Schema(description = "主管人数")
    private Integer managerNum;

    @Column(name = "employee_num")
    @Schema(description = "职员人数")
    private Integer employeeNum;

    @Column(name = "general_worker_num")
    @Schema(description = "普工人数")
    private Integer generalWorkerNum;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updateman", length = 20)
    @Schema(description = "更新人")
    private String updateman;

    @Column(name = "updatetime")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
}

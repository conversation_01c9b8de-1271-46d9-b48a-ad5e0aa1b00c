#!/bin/bash

APP_NAME="pujiang"
APP_PATH="/opt/pujiang"
VENV_PATH="$APP_PATH/venv"
PID_FILE="$APP_PATH/app.pid"

start() {
    echo "正在启动 $APP_NAME..."
    if [ -f $PID_FILE ]; then
        echo "$APP_NAME 已经在运行中，PID: $(cat $PID_FILE)"
        return 1
    fi
    
    source $VENV_PATH/bin/activate
    cd $APP_PATH
    nohup python3 main.py > app.log 2>&1 &
    echo $! > $PID_FILE
    echo "$APP_NAME 已启动，PID: $(cat $PID_FILE)"
}

stop() {
    echo "正在停止 $APP_NAME..."
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        kill $PID
        rm -f $PID_FILE
        echo "$APP_NAME 已停止"
    else
        echo "$APP_NAME 未在运行"
    fi
}

restart() {
    stop
    sleep 2
    start
}

status() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null; then
            echo "$APP_NAME 正在运行，PID: $PID"
        else
            echo "$APP_NAME 未在运行，但PID文件存在"
            rm -f $PID_FILE
        fi
    else
        echo "$APP_NAME 未在运行"
    fi
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac

exit 0 
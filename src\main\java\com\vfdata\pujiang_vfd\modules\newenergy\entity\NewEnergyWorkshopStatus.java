package com.vfdata.pujiang_vfd.modules.newenergy.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_xnycj_workshop_status")
@Schema(description = "新能源车间状态")
public class NewEnergyWorkshopStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "车间名称")
    private String workshopName;

    @Column(name = "operating_rate", precision = 5, scale = 2)
    @Schema(description = "开机率")
    private BigDecimal operatingRate;

    @Column(name = "total_equipment_count")
    @Schema(description = "设备总数")
    private Integer totalEquipmentCount;

    @Column(name = "operating_equipment_count")
    @Schema(description = "开机设备数")
    private Integer operatingEquipmentCount;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "备用字段3")
    private String reservedField3;
}

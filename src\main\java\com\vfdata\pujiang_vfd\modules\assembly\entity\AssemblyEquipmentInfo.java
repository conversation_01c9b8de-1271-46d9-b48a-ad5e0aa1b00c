package com.vfdata.pujiang_vfd.modules.assembly.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zzcj_equipment_info")
public class AssemblyEquipmentInfo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "total_equipment_count")
    private Integer totalEquipmentCount;
    
    @Column(name = "operating_equipment_count")
    private Integer operatingEquipmentCount;
    
    @Column(name = "equipment_overall_efficiency", precision = 5, scale = 2)
    private BigDecimal equipmentOverallEfficiency;
    
    @Column(name = "operating_rate", precision = 5, scale = 2)
    private BigDecimal operatingRate;
    
    @Column(name = "record_date")
    private LocalDate recordDate;
    
    @Column(name = "updated_by", length = 20)
    private String updatedBy;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "reserved_field1", length = 255)
    private String reservedField1;
    
    @Column(name = "reserved_field2", length = 255)
    private String reservedField2;
    
    @Column(name = "reserved_field3", length = 255)
    private String reservedField3;
} 
package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionStopTypeDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionStopTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/injection/stop-type")
@Tag(name = "注塑车间页面接口")
@RequiredArgsConstructor
public class InjectionStopTypeController {
    
    private final InjectionStopTypeService stopTypeService;
    
    @GetMapping("/latest")
    @Operation(summary = "获取停机类型数据", description = "获取指定车间或所有车间的停机类型数据")
    public ResponseUtils.Result<List<InjectionStopTypeDTO>> getStopTypes(
            @Parameter(description = "车间名称，不传则获取所有车间数据") 
            @RequestParam(required = false) String workshop_name) {
        try {
            List<InjectionStopTypeDTO> stopTypes = stopTypeService.getStopTypes(workshop_name);
            return ResponseUtils.success(stopTypes);
        } catch (Exception e) {
            return ResponseUtils.error("获取停机类型数据失败：" + e.getMessage());
        }
    }
} 
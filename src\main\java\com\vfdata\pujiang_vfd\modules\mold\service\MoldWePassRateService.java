package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldWePassRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldWePassRate;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldWePassRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldWePassRateService {

    private final MoldWePassRateRepository wePassRateRepository;

    /**
     * 获取WE合格率数据
     * @param period 周期类型：month返回6条，day返回7条
     */
    public List<MoldWePassRateDTO> getWePassRateByPeriod(String period) {
        int limit = "day".equals(period) ? 7 : 6;
        List<MoldWePassRate> entities = wePassRateRepository.findLatestRecordsByPeriod(period, limit);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldWePassRateDTO convertToDTO(MoldWePassRate entity) {
        MoldWePassRateDTO dto = new MoldWePassRateDTO();
        dto.setId(entity.getId());
        dto.setPeriod(entity.getPeriod());
        dto.setPujiang_pass_rate(entity.getPujiangPassRate());
        dto.setBoluo_pass_rate(entity.getBoluoPassRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyTongLvUsage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface NewEnergyTongLvUsageRepository extends JpaRepository<NewEnergyTongLvUsage, Long> {

    /**
     * 按记录日期降序查询所有数据
     */
    List<NewEnergyTongLvUsage> findAllByOrderByRecordDateDesc();

    /**
     * 获取最新的7条记录（按日期降序）
     */
    @Query("SELECT t FROM NewEnergyTongLvUsage t ORDER BY t.recordDate DESC LIMIT 7")
    List<NewEnergyTongLvUsage> findTop7ByOrderByRecordDateDesc();

    /**
     * 根据日期范围查询数据
     */
    List<NewEnergyTongLvUsage> findByRecordDateBetweenOrderByRecordDateDesc(
            LocalDate startDate, LocalDate endDate);

    /**
     * 获取最新记录日期的数据
     */
    @Query("SELECT t FROM NewEnergyTongLvUsage t WHERE t.recordDate = (SELECT MAX(t2.recordDate) FROM NewEnergyTongLvUsage t2)")
    List<NewEnergyTongLvUsage> findLatestRecords();

    /**
     * 根据指定日期查询数据
     */
    List<NewEnergyTongLvUsage> findByRecordDateOrderByIdDesc(LocalDate recordDate);
}

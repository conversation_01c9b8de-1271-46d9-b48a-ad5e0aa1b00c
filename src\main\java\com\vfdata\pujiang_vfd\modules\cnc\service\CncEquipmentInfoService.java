package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CncEquipmentInfoService {

    private final CncEquipmentInfoRepository equipmentInfoRepository;

    /**
     * 获取最新的设备信息
     */
    public CncEquipmentInfoDTO getLatestEquipmentInfo() {
        CncEquipmentInfo equipmentInfo = equipmentInfoRepository.findLatestRecord();
        if (equipmentInfo == null) {
            return null;
        }
        return convertToDTO(equipmentInfo);
    }

    /**
     * 实体转DTO
     */
    private CncEquipmentInfoDTO convertToDTO(CncEquipmentInfo entity) {
        CncEquipmentInfoDTO dto = new CncEquipmentInfoDTO();
        dto.setId(entity.getId());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setTotal_equipment_count(entity.getTotalEquipmentCount());
        dto.setOperating_equipment_count(entity.getOperatingEquipmentCount());
        dto.setOee_rate(entity.getOeeRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

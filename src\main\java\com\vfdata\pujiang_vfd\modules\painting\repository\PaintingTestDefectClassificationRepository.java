package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingTestDefectClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingTestDefectClassificationRepository extends JpaRepository<PaintingTestDefectClassification, Long> {

    /**
     * 获取最新记录日期的所有数据
     */
    @Query("SELECT p FROM PaintingTestDefectClassification p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingTestDefectClassification p2)")
    List<PaintingTestDefectClassification> findLatestRecords();

    /**
     * 获取指定车间最新记录日期的数据
     */
    @Query("SELECT p FROM PaintingTestDefectClassification p WHERE p.workshopName = :workshopName AND p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingTestDefectClassification p2 WHERE p2.workshopName = :workshopName)")
    List<PaintingTestDefectClassification> findLatestRecordsByWorkshop(@Param("workshopName") String workshopName);
}

DROP TABLE IF EXISTS "public"."ioc_mjcj_attendance_rate";
CREATE TABLE "public"."ioc_mjcj_attendance_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_attendance_rate_id_seq'::regclass),
  "clerk_attendance_rate" numeric(5,2),
  "worker_attendance_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default",
  "clerk_attendance_num" int4,
  "worker_attendance_num" int4
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."clerk_attendance_rate" IS '职员出勤率';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."worker_attendance_rate" IS '普工出勤率';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_attendance_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_attendance_rate" IS '模具车间一天出勤率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_attendance_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_attendance_rate" ADD CONSTRAINT "ioc_mjcj_attendance_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_change_mold_info";
CREATE TABLE "public"."ioc_mjcj_change_mold_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_change_mold_info_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "pujian_count" int4,
  "boluo_count" int4,
  "pujian_ratio" numeric(5,2),
  "boluo_ratio" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."pujian_count" IS '浦江改模数量';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."boluo_count" IS '博罗改模数量';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."pujian_ratio" IS '浦江改模占比';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."boluo_ratio" IS '博罗改模占比';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_change_mold_info"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_change_mold_info" IS '改模信息表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_change_mold_info
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_change_mold_info" ADD CONSTRAINT "ioc_mjcj_change_mold_info_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_cnc_pass_rate";
CREATE TABLE "public"."ioc_mjcj_cnc_pass_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_cnc_pass_rate_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "pujiang_pass_rate" numeric(5,2),
  "boluo_pass_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."pujiang_pass_rate" IS '一次CNC合格率';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."boluo_pass_rate" IS '最终CNC合格率';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_pass_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_cnc_pass_rate" IS 'CNC合格率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_cnc_pass_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_cnc_pass_rate" ADD CONSTRAINT "ioc_mjcj_cnc_pass_rate_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_mjcj_cnc_utilization_rate";
CREATE TABLE "public"."ioc_mjcj_cnc_utilization_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_cnc_utilization_rate_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "once_utilization_rate" numeric(5,2),
  "end_utilization_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."once_utilization_rate" IS '浦江CNC稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."end_utilization_rate" IS '博罗CNC稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_cnc_utilization_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_cnc_utilization_rate" IS 'CNC稼动率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_cnc_utilization_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_cnc_utilization_rate" ADD CONSTRAINT "ioc_mjcj_cnc_utilization_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_edm_pass_rate";
CREATE TABLE "public"."ioc_mjcj_edm_pass_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_edm_pass_rate_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "pujiang_pass_rate" numeric(5,2),
  "boluo_pass_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."pujiang_pass_rate" IS '一次EDM合格率';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."boluo_pass_rate" IS '最终EDM合格率';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_pass_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_edm_pass_rate" IS 'EDM合格率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_edm_pass_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_edm_pass_rate" ADD CONSTRAINT "ioc_mjcj_edm_pass_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_edm_utilization_rate";
CREATE TABLE "public"."ioc_mjcj_edm_utilization_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_edm_utilization_rate_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "once_utilization_rate" numeric(5,2),
  "end_utilization_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."once_utilization_rate" IS '浦江EDM稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."end_utilization_rate" IS '博罗EDM稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_edm_utilization_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_edm_utilization_rate" IS 'EDM稼动率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_edm_utilization_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_edm_utilization_rate" ADD CONSTRAINT "ioc_mjcj_edm_utilization_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_equipment_info";
CREATE TABLE "public"."ioc_mjcj_equipment_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_equipment_info_id_seq'::regclass),
  "operating_rate" numeric(5,2),
  "total_equipment_count" int4,
  "operating_equipment_count" int4,
  "oee_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" numeric(5,2),
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."operating_rate" IS '开机率';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."total_equipment_count" IS '总数';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."operating_equipment_count" IS '运行数量';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."oee_rate" IS 'OEE率（设备综合效率）';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."reserved_field1" IS '稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_equipment_info"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_equipment_info" IS '设备信息表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_equipment_info
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_equipment_info" ADD CONSTRAINT "ioc_mjcj_equipment_info_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_material_consumption";
CREATE TABLE "public"."ioc_mjcj_material_consumption" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_material_consumption_id_seq'::regclass),
  "material_type" varchar(50) COLLATE "pg_catalog"."default",
  "pujian_consumption" int4,
  "boluo_consumption" int4,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."material_type" IS '物料类型';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."pujian_consumption" IS '浦江消耗量';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."boluo_consumption" IS '博罗消耗量';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_material_consumption"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_material_consumption" IS '模具车间物料消耗表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_material_consumption
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_material_consumption" ADD CONSTRAINT "ioc_mjcj_material_consumption_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_mold_progress";
CREATE TABLE "public"."ioc_mjcj_mold_progress" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_mold_progress_id_seq'::regclass),
  "mold_number" varchar(50) COLLATE "pg_catalog"."default",
  "current_process" varchar(50) COLLATE "pg_catalog"."default",
  "processing_leadtime" date,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "complete_date" date,
  "Assembly _delivery_time" date,
  "Current progress" varchar(20) COLLATE "pg_catalog"."default",
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."mold_number" IS '模具号';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."current_process" IS '当前工序';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."processing_leadtime" IS '加工交期';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."complete_date" IS '完成时间';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."Assembly _delivery_time" IS '装配交期';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."Current progress" IS '当前进度';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_mold_progress"."reserved_field3" IS '备用字段2';
COMMENT ON TABLE "public"."ioc_mjcj_mold_progress" IS '模具进度表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_mold_progress
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_mold_progress" ADD CONSTRAINT "ioc_mjcj_mold_progress_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_new_mold_info";
CREATE TABLE "public"."ioc_mjcj_new_mold_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_new_mold_info_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "pujian_count" int4,
  "boluo_count" int4,
  "pujian_ratio" numeric(5,2),
  "boluo_ratio" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."pujian_count" IS '浦江新模数量';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."boluo_count" IS '博罗新模数量';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."pujian_ratio" IS '浦江新模占比';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."boluo_ratio" IS '博罗新模占比';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_new_mold_info"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_new_mold_info" IS '新模信息表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_new_mold_info
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_new_mold_info" ADD CONSTRAINT "ioc_mjcj_new_mold_info_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_mjcj_personnel_info";
CREATE TABLE "public"."ioc_mjcj_personnel_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_personnel_info_id_seq'::regclass),
  "personnel_type" varchar(20) COLLATE "pg_catalog"."default",
  "staff_count" int4,
  "general_worker_count" int4,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_personnel_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_personnel_info"."staff_count" IS '人员总数';
COMMENT ON COLUMN "public"."ioc_mjcj_personnel_info"."general_worker_count" IS '类型下人员数';
COMMENT ON TABLE "public"."ioc_mjcj_personnel_info" IS '人员信息表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_personnel_info
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_personnel_info" ADD CONSTRAINT "ioc_mjcj_personnel_info_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_repair_mold_info";
CREATE TABLE "public"."ioc_mjcj_repair_mold_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_repair_mold_info_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "pujian_count" int4,
  "boluo_count" int4,
  "pujian_ratio" numeric(5,2),
  "boluo_ratio" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."pujian_count" IS '浦江修模数量';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."boluo_count" IS '博罗修模数量';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."pujian_ratio" IS '浦江修模占比';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."boluo_ratio" IS '博罗修模占比';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_repair_mold_info"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_repair_mold_info" IS '修模信息表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_repair_mold_info
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_repair_mold_info" ADD CONSTRAINT "ioc_mjcj_repair_mold_info_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_we_pass_rate";
CREATE TABLE "public"."ioc_mjcj_we_pass_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_we_pass_rate_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "pujiang_pass_rate" numeric(5,2),
  "boluo_pass_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."pujiang_pass_rate" IS '一次WE合格率';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."boluo_pass_rate" IS '最终WE合格率';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_we_pass_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_we_pass_rate" IS 'WE合格率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_we_pass_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_we_pass_rate" ADD CONSTRAINT "ioc_mjcj_we_pass_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_we_utilization_rate";
CREATE TABLE "public"."ioc_mjcj_we_utilization_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_we_utilization_rate_id_seq'::regclass),
  "period" varchar(255) COLLATE "pg_catalog"."default",
  "once_utilization_rate" numeric(5,2),
  "end_utilization_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."period" IS '周期';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."once_utilization_rate" IS '浦江WE稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."end_utilization_rate" IS '博罗WE稼动率';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."reserved_field1" IS '备用字段1';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."reserved_field2" IS '备用字段2';
COMMENT ON COLUMN "public"."ioc_mjcj_we_utilization_rate"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_we_utilization_rate" IS 'WE稼动率表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_we_utilization_rate
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_we_utilization_rate" ADD CONSTRAINT "ioc_mjcj_we_utilization_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_mjcj_workshop_status";
CREATE TABLE "public"."ioc_mjcj_workshop_status" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_mjcj_workshop_status_id_seq'::regclass),
  "workshop_name" varchar(50) COLLATE "pg_catalog"."default",
  "workshop_rate" numeric(5,2),
  "park_name" varchar(50) COLLATE "pg_catalog"."default",
  "park_status_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "total_equipment_count" int4,
  "operating_equipment_count" int4,
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."workshop_rate" IS '车间状态率';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."park_name" IS '园区名称';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."park_status_rate" IS '园区状态率';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."record_date" IS '日期';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."updated_by" IS '更新人';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."total_equipment_count" IS '设备总数';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."operating_equipment_count" IS '总开机数';
COMMENT ON COLUMN "public"."ioc_mjcj_workshop_status"."reserved_field3" IS '备用字段3';
COMMENT ON TABLE "public"."ioc_mjcj_workshop_status" IS '车间设备状况表';

-- ----------------------------
-- Primary Key structure for table ioc_mjcj_workshop_status
-- ----------------------------
ALTER TABLE "public"."ioc_mjcj_workshop_status" ADD CONSTRAINT "ioc_mjcj_workshop_status_pkey" PRIMARY KEY ("id");


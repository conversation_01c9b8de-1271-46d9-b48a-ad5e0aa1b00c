package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncCuttingToolOffline7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncCuttingToolOffline7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/cuttingtool-offline-7days")
@RequiredArgsConstructor
public class CncCuttingToolOffline7DaysController {

    private final CncCuttingToolOffline7DaysService cuttingToolOffline7DaysService;

    @GetMapping
    @Operation(summary = "获取CNC车间刀具近7天下机原因", description = "获取指定CNC车间刀具近7天下机原因数据")
    public ResponseUtils.Result<List<CncCuttingToolOffline7DaysDTO>> getCuttingToolOffline7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<CncCuttingToolOffline7DaysDTO> dataList = cuttingToolOffline7DaysService.getCuttingToolOffline7Days(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间刀具下机原因7天数据失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class InjectionEquipmentInfoService {

    private final InjectionEquipmentInfoRepository injectionEquipmentInfoRepository;

    public InjectionEquipmentInfoDTO getLatestEquipmentInfo() {
        InjectionEquipmentInfo latestInfo = injectionEquipmentInfoRepository.findLatest()
                .orElseThrow(() -> new RuntimeException("未找到最新的设备信息记录"));

        InjectionEquipmentInfoDTO dto = new InjectionEquipmentInfoDTO();
        dto.setId(latestInfo.getId());
        dto.setTotal_equipment_count(latestInfo.getTotalEquipmentCount());
        dto.setOperating_equipment_count(latestInfo.getOperatingEquipmentCount());
        dto.setEquipment_overall_efficiency(latestInfo.getEquipmentOverallEfficiency());
        dto.setOperating_rate(latestInfo.getOperatingRate());
        dto.setRecord_date(latestInfo.getRecordDate());
        dto.setUpdated_by(latestInfo.getUpdatedBy());
        dto.setUpdated_at(latestInfo.getUpdatedAt());
        dto.setReserved_field1(latestInfo.getReservedField1());
        dto.setReserved_field2(latestInfo.getReservedField2());
        dto.setReserved_field3(latestInfo.getReservedField3());

        return dto;
    }
} 
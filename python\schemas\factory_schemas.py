from pydantic import BaseModel
from datetime import date, datetime
from typing import Optional
from decimal import Decimal

class FactoryBase(BaseModel):
    """工厂基础模型"""
    record_date: Optional[date] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

    class Config:
        from_attributes = True

class FactoryPersonnelInfoBase(FactoryBase):
    """
    全厂人员信息基础模型
    """
    total_worker_count: Optional[int] = None  # 普工人数
    total_clerical_count: Optional[int] = None  # 职员人数
    on_duty_worker_count: Optional[int] = None  # 普工出勤人数
    on_duty_clerical_count: Optional[int] = None  # 职员出勤人数

    @property
    def total_count(self) -> Optional[int]:
        """计算总人数"""
        if self.total_worker_count is not None and self.total_clerical_count is not None:
            return self.total_worker_count + self.total_clerical_count
        return None

class FactoryPersonnelInfoCreate(FactoryPersonnelInfoBase):
    """创建全厂人员信息的请求模型"""
    pass

class FactoryPersonnelInfo(FactoryPersonnelInfoBase):
    """全厂人员信息的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class WorkshopPersonnelInfoBase(FactoryBase):
    """
    车间人员信息基础模型
    """
    workshop_name: Optional[str] = None  # 车间名称
    total_worker_count: Optional[int] = None  # 普工人数
    total_clerical_count: Optional[int] = None  # 职员人数

class WorkshopPersonnelInfoCreate(WorkshopPersonnelInfoBase):
    """创建车间人员信息的请求模型"""
    pass

class WorkshopPersonnelInfo(WorkshopPersonnelInfoBase):
    """车间人员信息的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class NotificationInfoBase(FactoryBase):
    """
    通知信息基础模型
    """
    title: Optional[str] = None  # 通知标题
    content: Optional[str] = None  # 通知内容
    publish_time: Optional[datetime] = None  # 发布时间
    status: Optional[str] = None  # 状态

class NotificationInfoCreate(NotificationInfoBase):
    """创建通知信息的请求模型"""
    pass

class NotificationInfo(NotificationInfoBase):
    """通知信息的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class EquipmentInfoBase(FactoryBase):
    """
    设备信息基础模型
    """
    total_equipment_count: Optional[int] = None  # 设备总数
    operating_equipment_count: Optional[int] = None  # 运行设备数
    operating_rate: Optional[Decimal] = None  # 运行率

class EquipmentInfoCreate(EquipmentInfoBase):
    """创建设备信息的请求模型"""
    pass

class EquipmentInfo(EquipmentInfoBase):
    """设备信息的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class WorkshopEquipmentStatusBase(FactoryBase):
    """
    车间设备状态基础模型
    """
    workshop_name: Optional[str] = None  # 车间名称
    operating_rate: Optional[Decimal] = None  # 运行率

class WorkshopEquipmentStatusCreate(WorkshopEquipmentStatusBase):
    """创建车间设备状态的请求模型"""
    pass

class WorkshopEquipmentStatus(WorkshopEquipmentStatusBase):
    """车间设备状态的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class ProjectClassificationBase(FactoryBase):
    """
    项目分类基础模型
    """
    project_name: Optional[str] = None  # 项目名称
    mass_production_count: Optional[int] = None  # 量产数量
    development_count: Optional[int] = None  # 开发数量
    record_date: Optional[str] = None  # 修改为 str 类型以支持年份格式
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None


class ProjectClassificationCreate(ProjectClassificationBase):
    """创建项目分类的请求模型"""
    pass

class ProjectClassification(ProjectClassificationBase):
    """项目分类的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class AnnualProjectCategoryBase(FactoryBase):
    """
    年度项目分类基础模型
    """
    project_name: Optional[str] = None  # 项目名称
    project_count: Optional[int] = None  # 项目数量
    record_date: Optional[str] = None  # 修改为 str 类型以支持年份格式
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

class AnnualProjectCategoryCreate(AnnualProjectCategoryBase):
    """创建年度项目分类的请求模型"""
    pass

class AnnualProjectCategory(AnnualProjectCategoryBase):
    """年度项目分类的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class InjectionSamplingQualityRateBase(FactoryBase):
    """
    注塑抽检合格率基础模型
    """
    workshop_name: Optional[str] = None  # 车间名称
    quality_rate: Optional[Decimal] = None  # 合格率

class InjectionSamplingQualityRateCreate(InjectionSamplingQualityRateBase):
    """创建注塑抽检合格率的请求模型"""
    pass

class InjectionSamplingQualityRate(InjectionSamplingQualityRateBase):
    """注塑抽检合格率的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class AsmFocInspectionPassRateBase(FactoryBase):
    """
    组装FOC检验合格率基础模型
    """
    workshop_name: Optional[str] = None  # 所属车间
    pass_rate: Optional[Decimal] = None  # 合格率

class AsmFocInspectionPassRateCreate(AsmFocInspectionPassRateBase):
    """创建组装FOC检验合格率的请求模型"""
    pass

class AsmFocInspectionPassRate(AsmFocInspectionPassRateBase):
    """组装FOC检验合格率的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class CncInspectionPassRateBase(FactoryBase):
    """
    CNC巡检检验合格率基础模型
    """
    pass_rate: Optional[Decimal] = None  # 合格率

class CncInspectionPassRateCreate(CncInspectionPassRateBase):
    """创建CNC巡检检验合格率的请求模型"""
    pass

class CncInspectionPassRate(CncInspectionPassRateBase):
    """CNC巡检检验合格率的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class CoatingInspectionPassRateBase(FactoryBase):
    """
    涂装巡检检验合格率基础模型
    """
    pass_rate: Optional[Decimal] = None  # 合格率

class CoatingInspectionPassRateCreate(CoatingInspectionPassRateBase):
    """创建涂装巡检检验合格率的请求模型"""
    pass

class CoatingInspectionPassRate(CoatingInspectionPassRateBase):
    """涂装巡检检验合格率的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class WorkOrderCloseRate6MonthsBase(FactoryBase):
    """
    工单关闭率6个月趋势基础模型
    """
    workshop_name: Optional[str] = None  # 车间名称
    completed_count: Optional[int] = None  # 已完成工单数
    not_closed_count: Optional[int] = None  # 未关闭工单数
    close_rate: Optional[Decimal] = None  # 关闭率

class WorkOrderCloseRate6MonthsCreate(WorkOrderCloseRate6MonthsBase):
    """创建工单关闭率6个月趋势的请求模型"""
    pass

class WorkOrderCloseRate6Months(WorkOrderCloseRate6MonthsBase):
    """工单关闭率6个月趋势的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class ShipmentCount12MonthsBase(FactoryBase):
    """
    出货量12个月趋势基础模型
    """
    shipment_count: Optional[Decimal] = None  # 出货数量

class ShipmentCount12MonthsCreate(ShipmentCount12MonthsBase):
    """创建出货量12个月趋势的请求模型"""
    pass

class ShipmentCount12Months(ShipmentCount12MonthsBase):
    """出货量12个月趋势的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class MaterialPassRateLast6MonthsBase(FactoryBase):
    """
    近六个月品料合格率基础模型
    """
    pass_rate: Optional[Decimal] = None  # 合格率

class MaterialPassRateLast6MonthsCreate(MaterialPassRateLast6MonthsBase):
    """创建近六个月品料合格率的请求模型"""
    pass

class MaterialPassRateLast6Months(MaterialPassRateLast6MonthsBase):
    """近六个月品料合格率的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True 
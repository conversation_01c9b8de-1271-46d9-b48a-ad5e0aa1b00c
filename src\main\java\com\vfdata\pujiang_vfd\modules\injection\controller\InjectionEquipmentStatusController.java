package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionEquipmentStatusDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionEquipmentStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/equipment-status")
@RequiredArgsConstructor
public class InjectionEquipmentStatusController {

    private final InjectionEquipmentStatusService equipmentStatusService;

    @Operation(summary = "获取注塑车间设备状态", description = "获取指定车间或所有车间的设备状态数据")
    @GetMapping("/workshop")
    public ResponseUtils.Result<List<InjectionEquipmentStatusDTO>> getEquipmentStatus(
            @Parameter(description = "车间名称") @RequestParam(required = false) String workshop_name) {
        try {
            List<InjectionEquipmentStatusDTO> statusList = equipmentStatusService.getEquipmentStatus(workshop_name);
            return ResponseUtils.success(statusList);
        } catch (Exception e) {
            return ResponseUtils.error("获取设备状态数据失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取最新设备状态分布", description = "获取注塑车间最新的设备状态分布记录，可选择指定车间")
    @GetMapping("/distribution")
    public ResponseUtils.Result<List<InjectionEquipmentStatusDTO>> getLatestEquipmentStatusDistribution(
            @Parameter(description = "车间名称") @RequestParam(required = false) String workshop_name) {
        try {
            List<InjectionEquipmentStatusDTO> statusList = equipmentStatusService.getLatestEquipmentStatusDistribution(workshop_name);
            return ResponseUtils.success(statusList);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("没有找到")) {
                return ResponseUtils.error(404, e.getMessage());
            }
            return ResponseUtils.error("获取设备状态分布数据失败：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取设备状态分布数据失败：" + e.getMessage());
        }
    }
} 
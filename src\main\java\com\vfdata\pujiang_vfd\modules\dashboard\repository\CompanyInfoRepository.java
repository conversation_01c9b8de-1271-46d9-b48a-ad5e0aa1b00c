package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.CompanyInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyInfoRepository extends JpaRepository<CompanyInfo, Long> {
    
    @Query("SELECT c FROM CompanyInfo c ")
    Optional<CompanyInfo> findLatest();
    
    List<CompanyInfo> findAllByOrderByRecordDateDesc();
}

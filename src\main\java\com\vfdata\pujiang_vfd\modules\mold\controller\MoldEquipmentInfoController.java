package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldEquipmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/equipment-info")
@RequiredArgsConstructor
public class MoldEquipmentInfoController {

    private final MoldEquipmentInfoService equipmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取模具车间设备信息", description = "获取模具车间最新的设备运行状态信息")
    public ResponseUtils.Result<MoldEquipmentInfoDTO> getLatestEquipmentInfo() {
        try {
            MoldEquipmentInfoDTO data = equipmentInfoService.getLatestEquipmentInfo();
            if (data == null) {
                return ResponseUtils.success(new MoldEquipmentInfoDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间设备信息失败：" + e.getMessage());
        }
    }
}

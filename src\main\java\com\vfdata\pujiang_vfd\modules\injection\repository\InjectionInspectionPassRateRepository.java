package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionInspectionPassRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionInspectionPassRateRepository extends JpaRepository<InjectionInspectionPassRate, Long> {
    
    @Query(value = "SELECT * FROM ioc_zscj_inspection_pass_rate WHERE workshop_name = :workshopName ORDER BY record_date DESC, id DESC", nativeQuery = true)
    List<InjectionInspectionPassRate> findByWorkshopName(@Param("workshopName") String workshopName);

    @Query(value = "SELECT * FROM ioc_zscj_inspection_pass_rate ORDER BY workshop_name, record_date DESC, id DESC", nativeQuery = true)
    List<InjectionInspectionPassRate> findAllPassRates();
} 
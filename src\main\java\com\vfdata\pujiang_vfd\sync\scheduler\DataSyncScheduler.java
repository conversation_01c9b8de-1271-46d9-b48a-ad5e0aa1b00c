package com.vfdata.pujiang_vfd.sync.scheduler;

import com.vfdata.pujiang_vfd.config.DataSyncConfig;
import com.vfdata.pujiang_vfd.sync.service.DataSyncService;
import com.vfdata.pujiang_vfd.sync.task.DashboardSyncTask;
import com.vfdata.pujiang_vfd.sync.task.SafetySyncTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "data-sync.enabled", havingValue = "true")
public class DataSyncScheduler {

    private final DataSyncConfig config;
    private final DashboardSyncTask dashboardSyncTask;
    private final SafetySyncTask safetySyncTask;

    /**
     * 定时执行数据同步任务
     * 每10分钟执行一次（可通过配置调整）
     */
    @Scheduled(fixedDelayString = "#{${data-sync.sync-interval-minutes:10} * 60 * 1000}")
    @Async
    public void scheduledSync() {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始定时数据同步任务, 时间: {}", startTime);
        
        try {
            // 异步执行各模块同步任务
            CompletableFuture<List<DataSyncService.SyncResult>> dashboardFuture = 
                CompletableFuture.supplyAsync(() -> {
                    try {
                        return dashboardSyncTask.syncAll();
                    } catch (Exception e) {
                        log.error("Dashboard模块同步失败: {}", e.getMessage(), e);
                        return List.of();
                    }
                });

            CompletableFuture<List<DataSyncService.SyncResult>> safetyFuture = 
                CompletableFuture.supplyAsync(() -> {
                    try {
                        return safetySyncTask.syncAll();
                    } catch (Exception e) {
                        log.error("Safety模块同步失败: {}", e.getMessage(), e);
                        return List.of();
                    }
                });

            // 等待所有任务完成
            CompletableFuture.allOf(dashboardFuture, safetyFuture).join();

            // 收集结果
            List<DataSyncService.SyncResult> dashboardResults = dashboardFuture.get();
            List<DataSyncService.SyncResult> safetyResults = safetyFuture.get();

            // 统计结果
            int totalSuccess = 0;
            int totalFailed = 0;
            int totalRecords = 0;

            for (DataSyncService.SyncResult result : dashboardResults) {
                if (result.isSuccess()) {
                    totalSuccess++;
                    totalRecords += result.getTotalRecords();
                } else {
                    totalFailed++;
                }
            }

            for (DataSyncService.SyncResult result : safetyResults) {
                if (result.isSuccess()) {
                    totalSuccess++;
                    totalRecords += result.getTotalRecords();
                } else {
                    totalFailed++;
                }
            }

            LocalDateTime endTime = LocalDateTime.now();
            long duration = java.time.Duration.between(startTime, endTime).toMillis();

            log.info("定时数据同步任务完成, 成功: {}, 失败: {}, 总记录数: {}, 耗时: {}ms", 
                totalSuccess, totalFailed, totalRecords, duration);

        } catch (Exception e) {
            log.error("定时数据同步任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 手动触发数据同步
     */
    @Async
    public CompletableFuture<Void> manualSync() {
        log.info("手动触发数据同步任务");
        scheduledSync();
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 应用启动时执行一次同步（可选）
     */
    // @EventListener(ApplicationReadyEvent.class)
    // public void onApplicationReady() {
    //     log.info("应用启动完成，执行初始数据同步");
    //     manualSync();
    // }
}

package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyAchievementRateService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/achievement-rate")
@RequiredArgsConstructor
public class AssemblyAchievementRateController {

    private final AssemblyAchievementRateService achievementRateService;

    @GetMapping("/today")
    @Operation(summary = "获取当天计划达成率", description = "获取组装车间当天的计划达成率记录")
    public ResponseUtils.Result<List<AchievementRateDTO>> getTodayAchievementRate(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<AchievementRateDTO> rates = achievementRateService.getTodayAchievementRate(workshop_name);
            return ResponseUtils.success(rates);
        } catch (Exception e) {
            return ResponseUtils.error("获取当天计划达成率失败：" + e.getMessage());
        }
    }

    @GetMapping("/week")
    @Operation(summary = "获取近7天计划完成率", description = "获取组装车间近7天的计划完成率信息")
    public ResponseUtils.Result<List<AssemblyPlanCompletionRate7DaysDTO>> getWeekAchievementRate(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<AssemblyPlanCompletionRate7DaysDTO> rates = achievementRateService.getWeekAchievementRate(workshop_name);
            return ResponseUtils.success(rates);
        } catch (Exception e) {
            return ResponseUtils.error("获取近7天计划完成率失败：" + e.getMessage());
        }
    }
} 
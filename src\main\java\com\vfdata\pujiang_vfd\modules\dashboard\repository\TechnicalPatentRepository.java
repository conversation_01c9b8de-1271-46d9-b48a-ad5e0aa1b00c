package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.TechnicalPatent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TechnicalPatentRepository extends JpaRepository<TechnicalPatent, Long> {
    
    List<TechnicalPatent> findAllByOrderByPatentYearAsc();
    
    @Query("SELECT SUM(t.patentNum) FROM TechnicalPatent t")
    Long countTotalPatents();
}

package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.FactoryNotificationDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.FactoryNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "全厂页面接口")
@RestController
@RequestMapping("/factory/notifications")
@RequiredArgsConstructor
public class FactoryNotificationController {

    private final FactoryNotificationService factoryNotificationService;

    @Operation(summary = "获取最新通知")
    @GetMapping("/latest")
    public ResponseUtils.Result<List<FactoryNotificationDTO>> getLatestNotifications() {
        List<FactoryNotificationDTO> notifications = factoryNotificationService.getLatestNotifications();
        return ResponseUtils.success(notifications);
    }
} 
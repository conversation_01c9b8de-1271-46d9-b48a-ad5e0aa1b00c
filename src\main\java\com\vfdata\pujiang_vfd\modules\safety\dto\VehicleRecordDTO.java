package com.vfdata.pujiang_vfd.modules.safety.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "车辆进出记录DTO")
public class VehicleRecordDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车牌号")
    private String license_plate;

    @Schema(description = "通道名称")
    private String channel_name;

    @Schema(description = "进入时间")
    private String entry_time;

    @Schema(description = "状态（进/出）")
    private String status;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新日期")
    private String update_date;
}

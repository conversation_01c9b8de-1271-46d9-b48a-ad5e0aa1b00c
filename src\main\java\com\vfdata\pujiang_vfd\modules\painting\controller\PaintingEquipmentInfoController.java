package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingEquipmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/equipment-info")
@RequiredArgsConstructor
public class PaintingEquipmentInfoController {

    private final PaintingEquipmentInfoService equipmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取喷涂车间设备信息", description = "获取喷涂车间最新的设备运行状态信息")
    public ResponseUtils.Result<PaintingEquipmentInfoDTO> getLatestEquipmentInfo() {
        try {
            PaintingEquipmentInfoDTO data = equipmentInfoService.getLatestEquipmentInfo();
            if (data == null) {
                return ResponseUtils.success(new PaintingEquipmentInfoDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间设备信息失败：" + e.getMessage());
        }
    }
}

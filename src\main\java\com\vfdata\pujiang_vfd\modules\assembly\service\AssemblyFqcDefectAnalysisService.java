package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.FqcDefectAnalysisDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyFqcDefectAnalysis;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyFqcDefectAnalysisRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyFqcDefectAnalysisService {

    private final AssemblyFqcDefectAnalysisRepository fqcDefectAnalysisRepository;

    public List<FqcDefectAnalysisDTO> getLatestFqcDefectAnalysis(String workshopName) {
        List<AssemblyFqcDefectAnalysis> analysisList;
        if (workshopName != null && !workshopName.isEmpty()) {
            analysisList = fqcDefectAnalysisRepository.findLatestByWorkshopName(workshopName);
        } else {
            analysisList = fqcDefectAnalysisRepository.findLatest();
        }
        
        return analysisList.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    private FqcDefectAnalysisDTO convertToDTO(AssemblyFqcDefectAnalysis analysis) {
        FqcDefectAnalysisDTO dto = new FqcDefectAnalysisDTO();
        dto.setWorkshop_name(analysis.getWorkshopName());
        dto.setDefect_type_name(analysis.getDefectTypeName());
        dto.setDefect_type_count(analysis.getDefectTypeCount());
        dto.setRecord_date(analysis.getRecordDate().toString());
        return dto;
    }
} 
package com.vfdata.pujiang_vfd.modules.dashboard.controller;

import com.vfdata.pujiang_vfd.modules.dashboard.dto.*;
import com.vfdata.pujiang_vfd.modules.dashboard.entity.*;
import com.vfdata.pujiang_vfd.modules.dashboard.repository.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "Dashboard综合接口")
@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    // 时间格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final DeviceRepository deviceRepository;
    private final EmployeeInOutRepository employeeInOutRepository;
    private final HostelRepository hostelRepository;
    private final HostelInfoRepository hostelInfoRepository;
    private final ParkConstructRepository parkConstructRepository;
    private final ParkInfoRepository parkInfoRepository;
    private final PersonnelDistributionRepository personnelDistributionRepository;
    private final PrivateRoomRepository privateRoomRepository;
    private final StaffVisitorRepository staffVisitorRepository;
    private final VisitorRepository visitorRepository;
    private final VenuesRepository venuesRepository;
    private final CompanyInfoRepository companyInfoRepository;
    private final TechnicalRepository technicalRepository;
    private final TechnicalEduRepository technicalEduRepository;
    private final TechnicalPatentRepository technicalPatentRepository;

    @GetMapping("/device/")
    @Operation(summary = "获取设备信息列表", description = "获取所有设备信息记录")
    public ResponseUtils.Result<List<DeviceDTO>> getAllDevices() {
        try {
            List<Device> devices = deviceRepository.findAllByOrderByRecordDateDesc();
            List<DeviceDTO> result = devices.stream()
                    .map(this::convertDeviceToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取设备信息列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/employee-inout/")
    @Operation(summary = "获取员工进出记录列表", description = "获取最近20条员工进出记录")
    public ResponseUtils.Result<List<EmployeeInOutDTO>> getEmployeeInOut() {
        try {
            List<EmployeeInOut> records = employeeInOutRepository.findTop20ByOrderByRecordDateDesc();
            List<EmployeeInOutDTO> result = records.stream()
                    .map(this::convertEmployeeInOutToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取员工进出记录失败：" + e.getMessage());
        }
    }

    @GetMapping("/hostel/latest/")
    @Operation(summary = "获取最新宿舍信息", description = "获取最新的宿舍使用情况记录")
    public ResponseUtils.Result<HostelDTO> getLatestHostel() {
        try {
            Hostel hostel = hostelRepository.findLatest()
                    .orElseThrow(() -> new RuntimeException("没有找到宿舍信息"));
            HostelDTO result = convertHostelToDTO(hostel);
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取最新宿舍信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/hostel/info/latest")
    @Operation(summary = "获取宿舍文字介绍", description = "获取所有宿舍文字介绍记录")
    public ResponseUtils.Result<List<HostelInfoDTO>> getLatestHostelInfo() {
        try {
            List<HostelInfo> hostelInfos = hostelInfoRepository.findAllByOrderByRecordDateDesc();
            List<HostelInfoDTO> result = hostelInfos.stream()
                    .map(this::convertHostelInfoToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取宿舍文字介绍失败：" + e.getMessage());
        }
    }

    @GetMapping("/park-construct/")
    @Operation(summary = "获取园区建设信息列表", description = "获取所有园区建设信息记录")
    public ResponseUtils.Result<List<ParkConstruct>> getAllParkConstruct() {
        try {
            List<ParkConstruct> parkConstructs = parkConstructRepository.findAllByOrderByRecordDateDesc();
            return ResponseUtils.success(parkConstructs);
        } catch (Exception e) {
            return ResponseUtils.error("获取园区建设信息列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/park/brief/")
    @Operation(summary = "获取园区简介", description = "获取园区简介信息，包括基本信息、园区人数和安全运营天数")
    public ResponseUtils.Result<List<ParkBriefInfoDTO>> getParkBrief() {
        try {
            List<Object[]> results = parkInfoRepository.findParkBriefInfo();
            List<ParkBriefInfoDTO> briefInfos = results.stream()
                    .map(this::convertToParkBriefInfoDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(briefInfos);
        } catch (Exception e) {
            return ResponseUtils.error("获取园区简介失败：" + e.getMessage());
        }
    }

    @GetMapping("/personnel-distribution/latest/")
    @Operation(summary = "获取最新厂区人员分布", description = "获取最新的厂区人员分布记录")
    public ResponseUtils.Result<List<PersonnelDistributionDTO>> getLatestPersonnelDistribution() {
        try {
            List<PersonnelDistribution> distributions = personnelDistributionRepository.findLatest();
            List<PersonnelDistributionDTO> result = distributions.stream()
                    .map(this::convertPersonnelDistributionToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取最新厂区人员分布失败：" + e.getMessage());
        }
    }

    @GetMapping("/private-room/latest/")
    @Operation(summary = "获取最新包间信息", description = "获取最新的包间使用情况记录")
    public ResponseUtils.Result<PrivateRoom> getLatestPrivateRoom() {
        try {
            PrivateRoom privateRoom = privateRoomRepository.findLatest()
                    .orElseThrow(() -> new RuntimeException("没有找到包间信息"));
            return ResponseUtils.success(privateRoom);
        } catch (Exception e) {
            return ResponseUtils.error("获取最新包间信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/parking/")
    @Operation(summary = "获取停车场使用统计",
               description = "获取停车场使用情况的统计数据，包括总车位数、已使用数量、剩余数量和使用率")
    public ResponseUtils.Result<List<ParkingStatisticsDTO>> getParkingStatistics() {
        try {
            List<Object[]> results = parkInfoRepository.findParkingStatistics();
            List<ParkingStatisticsDTO> statistics = results.stream()
                    .map(this::convertToParkingStatisticsDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(statistics);
        } catch (Exception e) {
            return ResponseUtils.error("获取停车场使用统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/venues/")
    @Operation(summary = "获取娱乐会议场所统计",
               description = "获取娱乐会议场所的统计数据，包括各类场所的数量统计")
    public ResponseUtils.Result<VenuesStatisticsDTO> getVenuesStatistics() {
        try {
            List<Object[]> results = venuesRepository.findVenuesStatistics();
            List<VenuesStatisticsDTO.StatisticItem> statisticItems = results.stream()
                    .map(this::convertToVenuesStatisticItem)
                    .collect(Collectors.toList());

            VenuesStatisticsDTO response = new VenuesStatisticsDTO();
            response.setStatistics(statisticItems);
            return ResponseUtils.success(response);
        } catch (Exception e) {
            return ResponseUtils.error("获取娱乐会议场所统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics/staff-visitor/")
    @Operation(summary = "获取员工访客统计", description = "获取员工访客情况的统计数据，包括各类访客的数量统计")
    public ResponseUtils.Result<StaffVisitorStatisticsDTO> getStaffVisitorStatistics() {
        try {
            List<StaffVisitor> staffVisitors = staffVisitorRepository.findLatest();

            // 转换为Python格式的响应
            List<StaffVisitorStatisticsDTO.StatisticItem> statistics = staffVisitors.stream()
                .map(visitor -> {
                    StaffVisitorStatisticsDTO.StatisticItem item = new StaffVisitorStatisticsDTO.StatisticItem();
                    item.setType(visitor.getType());
                    item.setTotal(visitor.getNum());
                    return item;
                })
                .collect(Collectors.toList());

            StaffVisitorStatisticsDTO response = new StaffVisitorStatisticsDTO();
            response.setStatistics(statistics);

            return ResponseUtils.success(response);
        } catch (Exception e) {
            return ResponseUtils.error("获取员工访客统计失败：" + e.getMessage());
        }
    }

    @GetMapping("/visitor/")
    @Operation(summary = "获取访客信息列表", description = "获取所有访客信息记录，支持日期范围过滤")
    public ResponseUtils.Result<List<Visitor>> getVisitorList(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate) {
        try {
            List<Visitor> visitors;
            if (startDate != null || endDate != null) {
                visitors = visitorRepository.findByDateRange(startDate, endDate);
            } else {
                visitors = visitorRepository.findAllByOrderByRecordDateDesc();
            }
            return ResponseUtils.success(visitors);
        } catch (Exception e) {
            return ResponseUtils.error("获取访客信息列表失败：" + e.getMessage());
        }
    }

    // 企业相关接口
    @GetMapping("/company/latest/")
    @Operation(summary = "获取企业信息列表", description = "获取所有企业信息记录")
    public ResponseUtils.Result<List<CompanyInfoDTO>> getLatestCompanyInfo() {
        try {
            List<CompanyInfo> companyInfoList = companyInfoRepository.findAllByOrderByRecordDateDesc();
            List<CompanyInfoDTO> result = companyInfoList.stream()
                    .map(this::convertCompanyInfoToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取企业信息列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/technical/")
    @Operation(summary = "获取技术人员信息列表", description = "获取所有技术人员信息记录")
    public ResponseUtils.Result<List<TechnicalDTO>> getAllTechnical() {
        try {
            List<Technical> technicalList = technicalRepository.findAllByOrderByRecordDateDesc();
            List<TechnicalDTO> result = technicalList.stream()
                    .map(this::convertTechnicalToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取技术人员信息列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/technical-edu/")
    @Operation(summary = "获取技术人员学历信息列表", description = "获取所有技术人员学历信息记录")
    public ResponseUtils.Result<List<TechnicalEduDTO>> getAllTechnicalEdu() {
        try {
            List<TechnicalEdu> technicalEduList = technicalEduRepository.findAllByOrderByRecordDateDesc();
            List<TechnicalEduDTO> result = technicalEduList.stream()
                    .map(this::convertTechnicalEduToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取技术人员学历信息列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/technical-patent/")
    @Operation(summary = "获取技术专利信息列表", description = "获取所有技术专利信息记录")
    public ResponseUtils.Result<List<TechnicalPatentDTO>> getAllTechnicalPatent() {
        try {
            List<TechnicalPatent> technicalPatentList = technicalPatentRepository.findAllByOrderByPatentYearAsc();
            List<TechnicalPatentDTO> result = technicalPatentList.stream()
                    .map(this::convertTechnicalPatentToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取技术专利信息列表失败：" + e.getMessage());
        }
    }

    // DTO转换方法
    private DeviceDTO convertDeviceToDTO(Device device) {
        DeviceDTO dto = new DeviceDTO();
        dto.setId(device.getId());
        dto.setDevice_type(device.getDeviceType());
        dto.setDevice_num(device.getDeviceNum());
        dto.setRecord_date(formatDate(device.getRecordDate()));
        dto.setUpdateman(device.getUpdateman());
        dto.setUpdatetime(formatDateTime(device.getUpdatetime()));
        return dto;
    }

    private EmployeeInOutDTO convertEmployeeInOutToDTO(EmployeeInOut employeeInOut) {
        EmployeeInOutDTO dto = new EmployeeInOutDTO();
        dto.setId(employeeInOut.getId());
        dto.setEmployee_name(employeeInOut.getEmployeeName());
        dto.setEmployee_addr(employeeInOut.getEmployeeAddr());
        dto.setEmployee_state(employeeInOut.getEmployeeState());
        dto.setRecord_date(formatDateTime(employeeInOut.getRecordDate()));
        dto.setUpdateman(employeeInOut.getUpdateman());
        dto.setUpdatetime(formatDateTime(employeeInOut.getUpdatetime()));
        return dto;
    }

    private HostelDTO convertHostelToDTO(Hostel hostel) {
        HostelDTO dto = new HostelDTO();
        dto.setId(hostel.getId());
        dto.setHostel_num(hostel.getHostelNum());
        dto.setHostel_use(hostel.getHostelUse());
        dto.setHostel_remaining(hostel.getHostelRemaining());
        dto.setHostel_info(hostel.getHostelInfo());
        dto.setRecord_date(formatDate(hostel.getRecordDate()));
        dto.setUpdateman(hostel.getUpdateman());
        dto.setUpdatetime(formatDateTime(hostel.getUpdatetime()));
        return dto;
    }

    private HostelInfoDTO convertHostelInfoToDTO(HostelInfo hostelInfo) {
        HostelInfoDTO dto = new HostelInfoDTO();
        dto.setId(hostelInfo.getId());
        dto.setHostel_info(hostelInfo.getHostelInfo());
        dto.setRecord_date(formatDate(hostelInfo.getRecordDate()));
        dto.setUpdateman(hostelInfo.getUpdateman());
        dto.setUpdatetime(formatDateTime(hostelInfo.getUpdatetime()));
        return dto;
    }

    private ParkBriefInfoDTO convertToParkBriefInfoDTO(Object[] row) {
        ParkBriefInfoDTO dto = new ParkBriefInfoDTO();
        dto.setId(row[0] != null ? ((Number) row[0]).intValue() : null);
        dto.setType(row[1] != null ? row[1].toString() : null);
        dto.setNum(row[2] != null ? ((Number) row[2]).doubleValue() : null);
        dto.setReserved_field1(row[3] != null ? row[3].toString() : null);
        return dto;
    }

    private PersonnelDistributionDTO convertPersonnelDistributionToDTO(PersonnelDistribution distribution) {
        PersonnelDistributionDTO dto = new PersonnelDistributionDTO();
        dto.setId(distribution.getId());
        dto.setPark_name(distribution.getParkName());
        dto.setTotal_num(distribution.getTotalNum());
        dto.setManager_num(distribution.getManagerNum());
        dto.setEmployee_num(distribution.getEmployeeNum());
        dto.setGeneral_worker_num(distribution.getGeneralWorkerNum());
        dto.setRecord_date(formatDate(distribution.getRecordDate()));
        dto.setUpdateman(distribution.getUpdateman());
        dto.setUpdatetime(formatDateTime(distribution.getUpdatetime()));
        return dto;
    }

    private CompanyInfoDTO convertCompanyInfoToDTO(CompanyInfo companyInfo) {
        CompanyInfoDTO dto = new CompanyInfoDTO();
        dto.setId(companyInfo.getId());
        dto.setSerial_num(companyInfo.getSerialNum());
        dto.setCompany_info(companyInfo.getCompanyInfo());
        dto.setRecord_date(formatDate(companyInfo.getRecordDate()));
        dto.setUpdateman(companyInfo.getUpdateman());
        dto.setUpdatetime(formatDateTime(companyInfo.getUpdatetime()));
        return dto;
    }

    private TechnicalDTO convertTechnicalToDTO(Technical technical) {
        TechnicalDTO dto = new TechnicalDTO();
        dto.setId(technical.getId());
        dto.setPersonnel_type(technical.getPersonnelType());
        dto.setPersonnel_num(technical.getPersonnelNum());
        dto.setRecord_date(formatDate(technical.getRecordDate()));
        dto.setUpdateman(technical.getUpdateman());
        dto.setUpdatetime(formatDateTime(technical.getUpdatetime()));
        return dto;
    }

    private TechnicalEduDTO convertTechnicalEduToDTO(TechnicalEdu technicalEdu) {
        TechnicalEduDTO dto = new TechnicalEduDTO();
        dto.setId(technicalEdu.getId());
        dto.setEdu_background(technicalEdu.getEduBackground());
        dto.setEdu_num(technicalEdu.getEduNum());
        dto.setRecord_date(formatDate(technicalEdu.getRecordDate()));
        dto.setUpdateman(technicalEdu.getUpdateman());
        dto.setUpdatetime(formatDateTime(technicalEdu.getUpdatetime()));
        return dto;
    }

    private TechnicalPatentDTO convertTechnicalPatentToDTO(TechnicalPatent technicalPatent) {
        TechnicalPatentDTO dto = new TechnicalPatentDTO();
        dto.setId(technicalPatent.getId());
        dto.setPatent_year(technicalPatent.getPatentYear());
        dto.setPatent_num(technicalPatent.getPatentNum());
        dto.setRecord_date(formatDate(technicalPatent.getRecordDate()));
        dto.setUpdateman(technicalPatent.getUpdateman());
        dto.setUpdatetime(formatDateTime(technicalPatent.getUpdatetime()));
        return dto;
    }

    private ParkingStatisticsDTO convertToParkingStatisticsDTO(Object[] row) {
        ParkingStatisticsDTO dto = new ParkingStatisticsDTO();
        dto.setParking_addr(row[0] != null ? row[0].toString() : null);
        dto.setTotal_parking(row[1] != null ? ((Number) row[1]).intValue() : null);
        dto.setTotal_used(row[2] != null ? ((Number) row[2]).intValue() : null);
        dto.setTotal_remaining(row[3] != null ? ((Number) row[3]).intValue() : null);
        dto.setUsage_rate(row[4] != null ? ((Number) row[4]).doubleValue() : null);
        return dto;
    }

    private VenuesStatisticsDTO.StatisticItem convertToVenuesStatisticItem(Object[] row) {
        VenuesStatisticsDTO.StatisticItem item = new VenuesStatisticsDTO.StatisticItem();
        item.setType(row[0] != null ? row[0].toString() : null);
        item.setTotal(row[1] != null ? ((Number) row[1]).intValue() : null);
        item.setRemaining(row[2] != null ? ((Number) row[2]).intValue() : null);
        return item;
    }

    /**
     * 格式化LocalDate为字符串，不包含时区信息
     */
    private String formatDate(LocalDate date) {
        return date != null ? date.format(DATE_FORMATTER) : null;
    }

    /**
     * 格式化LocalDateTime为字符串，不包含时区信息
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMATTER) : null;
    }
}

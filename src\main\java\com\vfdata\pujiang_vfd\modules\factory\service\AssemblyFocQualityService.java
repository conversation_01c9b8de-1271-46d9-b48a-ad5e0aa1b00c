package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.AssemblyFocQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.AssemblyFocQuality;
import com.vfdata.pujiang_vfd.modules.factory.repository.AssemblyFocQualityRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyFocQualityService {

    private final AssemblyFocQualityRepository assemblyFocQualityRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取组装FOC检验合格率
     * 返回每个车间最新记录日期的数据
     */
    public List<AssemblyFocQualityDTO> getAssemblyFocQualityInfo() {
        List<AssemblyFocQuality> entities = assemblyFocQualityRepository.findLatestByWorkshopName();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private AssemblyFocQualityDTO convertToDTO(AssemblyFocQuality entity) {
        AssemblyFocQualityDTO dto = new AssemblyFocQualityDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setPass_rate(entity.getPassRate());
        if (entity.getRecordDate() != null) {
            dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        }
        dto.setUpdated_by(entity.getUpdatedBy());
        if (entity.getUpdatedAt() != null) {
            dto.setUpdated_at(entity.getUpdatedAt().toString());
        }
        return dto;
    }
} 
package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.Technical;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TechnicalRepository extends JpaRepository<Technical, Long> {
    
    List<Technical> findAllByOrderByRecordDateDesc();
    
    @Query("SELECT COUNT(t) FROM Technical t")
    long countTotal();
}

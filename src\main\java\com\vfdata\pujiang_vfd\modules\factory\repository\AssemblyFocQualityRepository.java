package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.AssemblyFocQuality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface AssemblyFocQualityRepository extends JpaRepository<AssemblyFocQuality, Long> {

    /**
     * 根据车间名称查询组装FOC质量信息
     */
    List<AssemblyFocQuality> findByWorkshopName(String workshopName);

    /**
     * 根据车间名称和日期范围查询组装FOC质量信息
     */
    List<AssemblyFocQuality> findByWorkshopNameAndRecordDateBetween(
            String workshopName, LocalDate startDate, LocalDate endDate);

    /**
     * 根据日期范围查询组装FOC质量信息
     */
    List<AssemblyFocQuality> findByRecordDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 获取最新的组装FOC质量信息
     */
    @Query("SELECT a FROM AssemblyFocQuality a WHERE a.recordDate = (SELECT MAX(a2.recordDate) FROM AssemblyFocQuality a2)")
    List<AssemblyFocQuality> findLatestQualityInfo();

    @Query("SELECT a FROM AssemblyFocQuality a WHERE a.workshopName = :workshopName AND a.recordDate BETWEEN :startDate AND :endDate ORDER BY a.recordDate DESC")
    List<AssemblyFocQuality> findByWorkshopNameAndDateRange(
            @Param("workshopName") String workshopName,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    @Query("SELECT a FROM AssemblyFocQuality a WHERE a.recordDate BETWEEN :startDate AND :endDate ORDER BY a.recordDate DESC")
    List<AssemblyFocQuality> findByDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    @Query("SELECT a FROM AssemblyFocQuality a WHERE a.id IN " +
           "(SELECT MAX(a2.id) FROM AssemblyFocQuality a2 GROUP BY a2.workshopName)")
    List<AssemblyFocQuality> findLatestByWorkshopName();
} 
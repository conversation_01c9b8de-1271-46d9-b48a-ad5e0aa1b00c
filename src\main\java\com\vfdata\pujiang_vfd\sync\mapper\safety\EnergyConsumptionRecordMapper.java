package com.vfdata.pujiang_vfd.sync.mapper.safety;

import com.vfdata.pujiang_vfd.modules.safety.entity.EnergyConsumptionRecord;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class EnergyConsumptionRecordMapper implements DataMapper<EnergyConsumptionRecord> {

    @Override
    public EnergyConsumptionRecord mapToEntity(Map<String, Object> sourceData) {
        try {
            EnergyConsumptionRecord entity = new EnergyConsumptionRecord();

            // 映射基础字段
            entity.setId(DateTimeParseUtil.getLongValue(sourceData, "id"));
            entity.setWaterUsage(DateTimeParseUtil.getBigDecimalValue(sourceData, "waterUsage"));
            entity.setElectricityUsage(DateTimeParseUtil.getBigDecimalValue(sourceData, "electricityUsage"));
            entity.setGasUsage(DateTimeParseUtil.getBigDecimalValue(sourceData, "gasUsage"));
            entity.setUpdatedBy(DateTimeParseUtil.getStringValue(sourceData, "updatedBy"));

            // 映射时间字段
            String recordTimeStr = DateTimeParseUtil.getStringValue(sourceData, "recordTime");
            entity.setRecordTime(DateTimeParseUtil.parseDateTime(recordTimeStr));

            // 映射更新日期
            String updateDateStr = DateTimeParseUtil.getStringValue(sourceData, "updateDate");
            entity.setUpdateDate(DateTimeParseUtil.parseDate(updateDateStr));

            // 映射预留字段
            entity.setReservedField1(DateTimeParseUtil.getStringValue(sourceData, "reservedField1"));
            entity.setReservedField2(DateTimeParseUtil.getStringValue(sourceData, "reservedField2"));
            entity.setReservedField3(DateTimeParseUtil.getStringValue(sourceData, "reservedField3"));

            return entity;

        } catch (Exception e) {
            throw new RuntimeException("映射EnergyConsumptionRecord数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getUniqueField() {
        return "id";
    }
}

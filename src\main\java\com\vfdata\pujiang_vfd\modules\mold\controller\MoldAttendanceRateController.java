package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldAttendanceRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/attendance-rate")
@RequiredArgsConstructor
public class MoldAttendanceRateController {

    private final MoldAttendanceRateService attendanceRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取模具车间出勤率", description = "获取模具车间最新的职员和普工出勤率信息")
    public ResponseUtils.Result<MoldAttendanceRateDTO> getLatestAttendanceRate() {
        try {
            MoldAttendanceRateDTO data = attendanceRateService.getLatestAttendanceRate();
            if (data == null) {
                return ResponseUtils.success(new MoldAttendanceRateDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间出勤率失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.common.exception;

import com.vfdata.pujiang_vfd.common.util.ErrorCodeUtils;
import com.vfdata.pujiang_vfd.common.util.LogUtils;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseUtils.Result<Void> handleBusinessException(BusinessException e) {
        LogUtils.error("业务异常", e);
        return ResponseUtils.error(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseUtils.Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String message = fieldErrors.stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        LogUtils.error("参数校验异常: {}", message);
        return ResponseUtils.error(ErrorCodeUtils.ErrorCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseUtils.Result<Void> handleBindException(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String message = fieldErrors.stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        LogUtils.error("参数绑定异常: {}", message);
        return ResponseUtils.error(ErrorCodeUtils.ErrorCode.PARAM_ERROR.getCode(), message);
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseUtils.Result<Void> handleException(Exception e) {
        LogUtils.error("系统异常", e);
        return ResponseUtils.error(ErrorCodeUtils.ErrorCode.SYSTEM_ERROR.getCode(), "系统异常，请联系管理员");
    }
} 
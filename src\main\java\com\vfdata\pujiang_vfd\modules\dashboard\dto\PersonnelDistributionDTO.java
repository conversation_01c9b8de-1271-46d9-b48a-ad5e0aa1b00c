package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "厂区人员分布DTO")
public class PersonnelDistributionDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "园区名称")
    private String park_name;

    @Schema(description = "园区总人数")
    private Integer total_num;

    @Schema(description = "主管人数")
    private Integer manager_num;

    @Schema(description = "职员人数")
    private Integer employee_num;

    @Schema(description = "普工人数")
    private Integer general_worker_num;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

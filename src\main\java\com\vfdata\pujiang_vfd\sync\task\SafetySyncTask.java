package com.vfdata.pujiang_vfd.sync.task;

import com.vfdata.pujiang_vfd.modules.safety.repository.*;
import com.vfdata.pujiang_vfd.sync.mapper.safety.*;
import com.vfdata.pujiang_vfd.sync.service.DataSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "data-sync.enabled", havingValue = "true")
public class SafetySyncTask {

    private final DataSyncService dataSyncService;

    // Safety repositories
    private final VisitorRecordRepository visitorRecordRepository;
    private final VehicleRecordRepository vehicleRecordRepository;
    private final FireEquipmentInfoRepository fireEquipmentInfoRepository;
    private final EnergyConsumptionRecordRepository energyConsumptionRecordRepository;
    private final ActualEnergyConsumptionRepository actualEnergyConsumptionRepository;

    // Safety mappers
    private final VisitorRecordMapper visitorRecordMapper;
    private final VehicleRecordMapper vehicleRecordMapper;
    private final FireEquipmentInfoMapper fireEquipmentInfoMapper;
    private final EnergyConsumptionRecordMapper energyConsumptionRecordMapper;
    private final ActualEnergyConsumptionMapper actualEnergyConsumptionMapper;

    /**
     * 同步访客记录
     */
    public DataSyncService.SyncResult syncVisitorRecords() {
        log.info("开始同步访客记录");
        return dataSyncService.syncData(
            "/ioc/bigScreen/visitorRecords",
            visitorRecordRepository,
            visitorRecordMapper
        );
    }

    /**
     * 同步车辆进出记录
     */
    public DataSyncService.SyncResult syncVehicleRecords() {
        log.info("开始同步车辆进出记录");
        return dataSyncService.syncData(
            "/ioc/bigScreen/vehicleRecords",
            vehicleRecordRepository,
            vehicleRecordMapper
        );
    }

    /**
     * 同步消防设备信息
     */
    public DataSyncService.SyncResult syncFireEquipmentInfo() {
        log.info("开始同步消防设备信息");
        return dataSyncService.syncData(
            "/ioc/bigScreen/fireEquipmentInfo",
            fireEquipmentInfoRepository,
            fireEquipmentInfoMapper
        );
    }

    /**
     * 同步能耗统计记录
     */
    public DataSyncService.SyncResult syncEnergyConsumptionRecords() {
        log.info("开始同步能耗统计记录");
        return dataSyncService.syncData(
            "/ioc/bigScreen/energyConsumptionRecords",
            energyConsumptionRecordRepository,
            energyConsumptionRecordMapper
        );
    }

    /**
     * 同步实际能耗统计
     */
    public DataSyncService.SyncResult syncActualEnergyConsumption() {
        log.info("开始同步实际能耗统计");
        return dataSyncService.syncData(
            "/ioc/bigScreen/actualEnergyConsumption",
            actualEnergyConsumptionRepository,
            actualEnergyConsumptionMapper
        );
    }

    /**
     * 同步所有Safety模块数据
     */
    public List<DataSyncService.SyncResult> syncAll() {
        log.info("开始同步所有Safety模块数据");
        List<DataSyncService.SyncResult> results = new ArrayList<>();
        
        try {
            // 同步访客记录
            results.add(syncVisitorRecords());

            // 同步车辆进出记录
            results.add(syncVehicleRecords());

            // 同步消防设备信息
            results.add(syncFireEquipmentInfo());

            // 同步能耗统计记录
            results.add(syncEnergyConsumptionRecords());

            // 同步实际能耗统计
            results.add(syncActualEnergyConsumption());

        } catch (Exception e) {
            log.error("Safety模块数据同步失败: {}", e.getMessage(), e);
        }
        
        return results;
    }
}

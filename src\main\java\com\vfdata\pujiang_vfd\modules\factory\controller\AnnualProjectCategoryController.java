package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.AnnualProjectCategoryDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.AnnualProjectCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/factory/projects/annual")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class AnnualProjectCategoryController {

    private final AnnualProjectCategoryService service;

    @GetMapping
    @Operation(summary = "获取年度项目分类信息", description = "根据项目名称和日期范围获取年度项目分类信息")
    public ResponseUtils.Result<List<AnnualProjectCategoryDTO>> getAnnualProjectCategories(
            @Parameter(description = "项目名称") @RequestParam(required = false) String project_name,
            @Parameter(description = "开始日期") @RequestParam(required = false) LocalDate start_date,
            @Parameter(description = "结束日期") @RequestParam(required = false) LocalDate end_date) {
        try {
            List<AnnualProjectCategoryDTO> data = service.getAnnualProjectCategories(project_name, start_date, end_date);
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取年度项目分类信息失败：" + e.getMessage());
        }
    }
} 
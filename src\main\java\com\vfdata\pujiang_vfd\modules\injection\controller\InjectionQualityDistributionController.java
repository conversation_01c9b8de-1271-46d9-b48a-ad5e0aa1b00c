package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionQualityDistributionDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionQualityDistributionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/quality-distribution")
@RequiredArgsConstructor
public class InjectionQualityDistributionController {

    private final InjectionQualityDistributionService qualityDistributionService;

    @Operation(summary = "获取最新质量分布", description = "获取指定车间或所有车间的最新质量分布数据")
    @GetMapping("/latest")
    public ResponseUtils.Result<List<InjectionQualityDistributionDTO>> getLatestQualityDistribution(
            @Parameter(description = "车间名称") @RequestParam(required = false) String workshop_name) {
        try {
            List<InjectionQualityDistributionDTO> distributions = qualityDistributionService.getLatestQualityDistribution(workshop_name);
            return ResponseUtils.success(distributions);
        } catch (Exception e) {
            return ResponseUtils.error("获取质量分布数据失败：" + e.getMessage());
        }
    }
} 
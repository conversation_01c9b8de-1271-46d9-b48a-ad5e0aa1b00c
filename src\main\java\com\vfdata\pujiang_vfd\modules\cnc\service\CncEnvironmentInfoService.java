package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncEnvironmentInfo;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncEnvironmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CncEnvironmentInfoService {

    private final CncEnvironmentInfoRepository environmentInfoRepository;

    /**
     * 获取最新的环境信息
     */
    public CncEnvironmentInfoDTO getLatestEnvironmentInfo() {
        CncEnvironmentInfo environmentInfo = environmentInfoRepository.findLatestRecord();
        if (environmentInfo == null) {
            return null;
        }
        return convertToDTO(environmentInfo);
    }

    /**
     * 实体转DTO
     */
    private CncEnvironmentInfoDTO convertToDTO(CncEnvironmentInfo entity) {
        CncEnvironmentInfoDTO dto = new CncEnvironmentInfoDTO();
        dto.setId(entity.getId());
        dto.setDust_free_workshop_level(entity.getDustFreeWorkshopLevel());
        dto.setAverage_humidity(entity.getAverageHumidity());
        dto.setAverage_temperature(entity.getAverageTemperature());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

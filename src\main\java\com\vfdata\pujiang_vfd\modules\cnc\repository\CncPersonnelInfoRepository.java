package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncPersonnelInfoRepository extends JpaRepository<CncPersonnelInfo, Long> {

    /**
     * 获取最新日期的所有人员信息记录
     */
    @Query("SELECT c FROM CncPersonnelInfo c WHERE c.recordDate = (SELECT MAX(c2.recordDate) FROM CncPersonnelInfo c2)")
    List<CncPersonnelInfo> findLatest();
}

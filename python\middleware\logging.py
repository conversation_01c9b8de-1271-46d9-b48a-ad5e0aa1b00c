from fastapi import Request
from utils.logger import logger
import time
from typing import Callable
import json
from starlette.middleware.base import BaseHTTPMiddleware

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable):
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        logger.info(f"开始处理请求: {request.method} {request.url}")
        
        # 获取请求体
        body = None
        if request.method in ["POST", "PUT"]:
            try:
                body = await request.json()
                logger.info(f"请求体: {json.dumps(body, ensure_ascii=False)}")
            except:
                pass
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        logger.info(f"请求处理完成: {request.method} {request.url}")
        logger.info(f"处理时间: {process_time:.3f}秒")
        logger.info(f"状态码: {response.status_code}")
        
        return response 
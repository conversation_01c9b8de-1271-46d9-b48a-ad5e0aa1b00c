package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldNewMoldInfoDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldNewMoldInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/new-mold-info")
@RequiredArgsConstructor
public class MoldNewMoldInfoController {

    private final MoldNewMoldInfoService newMoldInfoService;

    @GetMapping("/6months")
    @Operation(summary = "获取模具车间新模信息", description = "获取模具车间新模信息数据，period=month返回6条，period=day返回7条")
    public ResponseUtils.Result<List<MoldNewMoldInfoDTO>> getNewMoldInfoByPeriod(@RequestParam String period) {
        try {
            List<MoldNewMoldInfoDTO> data = newMoldInfoService.getNewMoldInfoByPeriod(period);
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间新模信息失败：" + e.getMessage());
        }
    }
}

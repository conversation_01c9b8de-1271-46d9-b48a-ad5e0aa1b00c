package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncDailyAchievementRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/daily-achievement-rate")
@RequiredArgsConstructor
public class CncDailyAchievementRateController {

    private final CncDailyAchievementRateService dailyAchievementRateService;

    @GetMapping
    @Operation(summary = "获取CNC车间计划达成率", description = "获取CNC车间最新的计划达成率信息")
    public ResponseUtils.Result<List<CncDailyAchievementRateDTO>> getDailyAchievementRate() {
        try {
            List<CncDailyAchievementRateDTO> data = dailyAchievementRateService.getLatestDailyAchievementRate();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间计划达成率失败：" + e.getMessage());
        }
    }
}

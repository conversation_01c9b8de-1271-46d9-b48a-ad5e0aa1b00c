package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.ShipmentTrend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ShipmentTrendRepository extends JpaRepository<ShipmentTrend, Long> {
    
    @Query("SELECT s FROM ShipmentTrend s ORDER BY s.recordDate DESC, s.id DESC LIMIT 12")
    List<ShipmentTrend> findLatest();
} 
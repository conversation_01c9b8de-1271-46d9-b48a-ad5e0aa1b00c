package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface AssemblyProjectStatusRepository extends JpaRepository<AssemblyProjectStatus, Long> {
    
    @Query("SELECT p FROM AssemblyProjectStatus p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM AssemblyProjectStatus p2)")
    List<AssemblyProjectStatus> findLatest();
    
    @Query("SELECT p FROM AssemblyProjectStatus p WHERE p.workshopName = :workshopName AND p.recordDate = (SELECT MAX(p2.recordDate) FROM AssemblyProjectStatus p2 WHERE p2.workshopName = :workshopName)")
    List<AssemblyProjectStatus> findLatestByWorkshopName(@Param("workshopName") String workshopName);
} 
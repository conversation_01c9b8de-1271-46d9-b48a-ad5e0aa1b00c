package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionPlanCompletionRate7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionPlanCompletionRate7DaysRepository extends JpaRepository<InjectionPlanCompletionRate7Days, Long> {

    @Query("SELECT i FROM InjectionPlanCompletionRate7Days i WHERE i.workshopName = :workshopName ORDER BY i.recordDate DESC")
    List<InjectionPlanCompletionRate7Days> findByWorkshopName(@Param("workshopName") String workshopName);

    @Query("SELECT i FROM InjectionPlanCompletionRate7Days i ORDER BY i.recordDate DESC")
    List<InjectionPlanCompletionRate7Days> findAllOrderByRecordDateDesc();
} 
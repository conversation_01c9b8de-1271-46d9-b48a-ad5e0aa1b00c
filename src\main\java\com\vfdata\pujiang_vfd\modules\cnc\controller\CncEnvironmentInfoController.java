package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncEnvironmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/environment-info")
@RequiredArgsConstructor
public class CncEnvironmentInfoController {

    private final CncEnvironmentInfoService environmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间环境信息", description = "获取CNC车间最新的环境信息")
    public ResponseUtils.Result<CncEnvironmentInfoDTO> getLatestEnvironmentInfo() {
        try {
            CncEnvironmentInfoDTO data = environmentInfoService.getLatestEnvironmentInfo();
            if (data == null) {
                return ResponseUtils.success(new CncEnvironmentInfoDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间环境信息失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AssemblyEquipmentInfoService {
    
    private final AssemblyEquipmentInfoRepository equipmentInfoRepository;
    
    public AssemblyEquipmentInfoDTO getLatestEquipmentInfo() {
        Optional<AssemblyEquipmentInfo> optionalInfo = equipmentInfoRepository.findLatest();
        if (optionalInfo.isPresent()) {
            AssemblyEquipmentInfo info = optionalInfo.get();
            AssemblyEquipmentInfoDTO dto = new AssemblyEquipmentInfoDTO();
            dto.setId(info.getId());
            dto.setTotal_equipment_count(info.getTotalEquipmentCount());
            dto.setOperating_equipment_count(info.getOperatingEquipmentCount());
            dto.setEquipment_overall_efficiency(info.getEquipmentOverallEfficiency());
            dto.setOperating_rate(info.getOperatingRate());
            dto.setRecord_date(info.getRecordDate());
            dto.setUpdated_by(info.getUpdatedBy());
            dto.setUpdated_at(info.getUpdatedAt());
            dto.setReserved_field1(info.getReservedField1());
            dto.setReserved_field2(info.getReservedField2());
            dto.setReserved_field3(info.getReservedField3());
            return dto;
        }
        return null;
    }
} 
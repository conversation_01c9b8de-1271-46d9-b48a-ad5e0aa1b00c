package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.common.exception.BusinessException;
import com.vfdata.pujiang_vfd.modules.factory.dto.FactoryNotificationDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.FactoryNotification;
import com.vfdata.pujiang_vfd.modules.factory.repository.FactoryNotificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FactoryNotificationService {

    private final FactoryNotificationRepository factoryNotificationRepository;

    /**
     * 获取最新的通知信息
     */
    public List<FactoryNotificationDTO> getLatestNotifications() {
        List<FactoryNotification> latestRecords = factoryNotificationRepository.findLatestNotifications();
        
        if (latestRecords.isEmpty()) {
            throw new BusinessException("未找到通知信息记录");
        }
        
        return latestRecords.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private FactoryNotificationDTO convertToDTO(FactoryNotification entity) {
        FactoryNotificationDTO dto = new FactoryNotificationDTO();
        dto.setId(entity.getId());
        dto.setContent(entity.getNotificationContent());
        dto.setRecord_date(entity.getRecordDate().atStartOfDay());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
package com.vfdata.pujiang_vfd.sync.mapper.dashboard;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.EmployeeInOut;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class EmployeeInOutMapper implements DataMapper<EmployeeInOut> {

    @Override
    public EmployeeInOut mapToEntity(Map<String, Object> sourceData) {
        try {
            EmployeeInOut entity = new EmployeeInOut();

            // 映射基础字段
            entity.setId(DateTimeParseUtil.getLongValue(sourceData, "id"));
            entity.setEmployeeName(DateTimeParseUtil.getStringValue(sourceData, "employeeName"));
            entity.setEmployeeAddr(DateTimeParseUtil.getStringValue(sourceData, "employeeAddr"));
            entity.setEmployeeState(DateTimeParseUtil.getStringValue(sourceData, "employeeState"));

            // 映射时间字段 - 直接保存完整的原始时间信息
            String recordDateStr = DateTimeParseUtil.getStringValue(sourceData, "recordDate");
            entity.setRecordDate(DateTimeParseUtil.parseDateTime(recordDateStr));

            // 映射更新时间
            String updatetimeStr = DateTimeParseUtil.getStringValue(sourceData, "updatetime");
            entity.setUpdatetime(DateTimeParseUtil.parseDateTime(updatetimeStr));

            entity.setUpdateman(DateTimeParseUtil.getStringValue(sourceData, "updateman"));

            // 映射预留字段
            entity.setReservedField1(DateTimeParseUtil.getStringValue(sourceData, "reservedField1"));
            entity.setReservedField2(DateTimeParseUtil.getStringValue(sourceData, "reservedField2"));
            entity.setReservedField3(DateTimeParseUtil.getStringValue(sourceData, "reservedField3"));

            return entity;

        } catch (Exception e) {
            throw new RuntimeException("映射EmployeeInOut数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getUniqueField() {
        return "id";
    }
}

package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.ProcessYieldDTO;
import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyProcessYieldInfoDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyProcessYieldService;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyProcessYieldInfoService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/process-yield")
@RequiredArgsConstructor
public class AssemblyProcessYieldController {

    private final AssemblyProcessYieldService processYieldService;
    private final AssemblyProcessYieldInfoService processYieldInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新工序良率", description = "获取组装车间最新的工序良率记录，可选择指定车间")
    public ResponseUtils.Result<List<ProcessYieldDTO>> getLatestProcessYield(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<ProcessYieldDTO> yields = processYieldService.getLatestProcessYield(workshop_name);
            return ResponseUtils.success(yields);
        } catch (Exception e) {
            return ResponseUtils.error("获取工序良率失败：" + e.getMessage());
        }
    }

    @GetMapping("/week")
    @Operation(summary = "获取最近7天工序良率数据",
               description = "获取最近7天的工序良率数据，使用ioc_zzcj_process_yield_info表")
    public ResponseUtils.Result<List<AssemblyProcessYieldInfoDTO>> getWeekProcessYield(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<AssemblyProcessYieldInfoDTO> yields = processYieldInfoService.getWeeklyProcessYield(workshop_name);
            return ResponseUtils.success(yields);
        } catch (Exception e) {
            return ResponseUtils.error("获取最近7天工序良率数据失败：" + e.getMessage());
        }
    }
} 
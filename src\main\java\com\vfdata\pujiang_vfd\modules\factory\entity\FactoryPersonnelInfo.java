package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全厂人员信息实体
 */
@Data
@Entity
@Table(name = "ioc_qc_personnel_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "全厂人员信息")
public class FactoryPersonnelInfo extends BaseEntity {

    @Schema(description = "普工人数")
    @Column(name = "total_worker_count")
    private Integer totalWorkerCount;

    @Schema(description = "职员人数")
    @Column(name = "total_clerical_count")
    private Integer totalClericalCount;

    @Schema(description = "普工出勤人数")
    @Column(name = "on_duty_worker_count")
    private Integer onDutyWorkerCount;

    @Schema(description = "职员出勤人数")
    @Column(name = "on_duty_clerical_count")
    private Integer onDutyClericalCount;

    /**
     * 获取总人数
     */
    public Integer getTotalCount() {
        if (totalWorkerCount != null && totalClericalCount != null) {
            return totalWorkerCount + totalClericalCount;
        }
        return null;
    }
} 
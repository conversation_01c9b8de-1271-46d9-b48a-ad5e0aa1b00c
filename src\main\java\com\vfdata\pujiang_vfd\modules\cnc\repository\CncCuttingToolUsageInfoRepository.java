package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncCuttingToolUsageInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncCuttingToolUsageInfoRepository extends JpaRepository<CncCuttingToolUsageInfo, Long> {

    /**
     * 获取每个车间最新的刀具消耗信息记录
     */
    @Query("SELECT c FROM CncCuttingToolUsageInfo c WHERE c.updatedAt = (SELECT MAX(c2.updatedAt) FROM CncCuttingToolUsageInfo c2 WHERE c2.workshopName = c.workshopName)")
    List<CncCuttingToolUsageInfo> findLatestRecords();
}

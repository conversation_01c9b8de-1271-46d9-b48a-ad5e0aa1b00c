package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "企业信息DTO")
public class CompanyInfoDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "序号")
    private Integer serial_num;

    @Schema(description = "企业信息")
    private String company_info;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

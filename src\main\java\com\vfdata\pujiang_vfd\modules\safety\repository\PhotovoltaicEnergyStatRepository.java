package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.PhotovoltaicEnergyStat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface PhotovoltaicEnergyStatRepository extends JpaRepository<PhotovoltaicEnergyStat, Long> {
    
    List<PhotovoltaicEnergyStat> findAllByOrderByRecordTimeDesc();
}

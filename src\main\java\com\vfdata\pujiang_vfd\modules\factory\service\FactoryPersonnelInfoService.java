package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.common.exception.BusinessException;
import com.vfdata.pujiang_vfd.modules.factory.dto.FactoryPersonnelInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.FactoryPersonnelInfo;
import com.vfdata.pujiang_vfd.modules.factory.repository.FactoryPersonnelInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 全厂人员信息Service
 */
@Service
@RequiredArgsConstructor
public class FactoryPersonnelInfoService {

    private final FactoryPersonnelInfoRepository factoryPersonnelInfoRepository;

    /**
     * 获取最新的人员信息
     */
    public List<FactoryPersonnelInfoDTO> getLatestPersonnelInfo() {
        List<FactoryPersonnelInfo> latestRecords = factoryPersonnelInfoRepository.findLatestRecords();
        if (latestRecords.isEmpty()) {
            throw new BusinessException("未找到人员信息记录");
        }
        return latestRecords.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 创建人员信息
     */
    @Transactional
    public FactoryPersonnelInfoDTO createPersonnelInfo(FactoryPersonnelInfoDTO dto) {
        FactoryPersonnelInfo entity = new FactoryPersonnelInfo();
        entity.setTotalWorkerCount(dto.getTotal_worker_count());
        entity.setTotalClericalCount(dto.getTotal_clerical_count());
        entity.setOnDutyWorkerCount(dto.getOn_duty_worker_count());
        entity.setOnDutyClericalCount(dto.getOn_duty_clerical_count());
        entity.setRecordDate(dto.getRecord_date());
        entity.setUpdatedBy(dto.getUpdated_by());
        entity.setReservedField1(dto.getReserved_field1());
        entity.setReservedField2(dto.getReserved_field2());
        entity.setReservedField3(dto.getReserved_field3());

        FactoryPersonnelInfo savedEntity = factoryPersonnelInfoRepository.save(entity);
        return convertToDTO(savedEntity);
    }

    /**
     * 将实体转换为DTO
     */
    private FactoryPersonnelInfoDTO convertToDTO(FactoryPersonnelInfo entity) {
        FactoryPersonnelInfoDTO dto = new FactoryPersonnelInfoDTO();
        dto.setId(entity.getId());
        dto.setTotal_worker_count(entity.getTotalWorkerCount());
        dto.setTotal_clerical_count(entity.getTotalClericalCount());
        dto.setOn_duty_worker_count(entity.getOnDutyWorkerCount());
        dto.setOn_duty_clerical_count(entity.getOnDutyClericalCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
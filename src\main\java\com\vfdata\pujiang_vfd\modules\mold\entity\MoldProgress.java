package com.vfdata.pujiang_vfd.modules.mold.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_mjcj_mold_progress")
@Schema(description = "模具车间模具进度")
public class MoldProgress {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "mold_number")
    @Schema(description = "模具号")
    private String moldNumber;

    @Column(name = "current_process")
    @Schema(description = "当前工序")
    private String currentProcess;

    @Column(name = "processing_leadtime")
    @Schema(description = "加工交期")
    private LocalDate processingLeadtime;

    @Column(name = "complete_date")
    @Schema(description = "完成时间")
    private LocalDate completeDate;

    @Column(name = "assembly_delivery_time")
    @Schema(description = "装配交期")
    private LocalDate assemblyDeliveryTime;

    @Column(name = "current_progress")
    @Schema(description = "当前进度")
    private String currentProgress;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

package com.vfdata.pujiang_vfd.modules.cnc.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_cnccj_xjjy_quality_rate")
@Schema(description = "CNC车间巡检检验合格率")
public class CncQualityRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "所属车间")
    private String workshopName;

    @Column(name = "quality_rate")
    @Schema(description = "当前合格率")
    private BigDecimal qualityRate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}

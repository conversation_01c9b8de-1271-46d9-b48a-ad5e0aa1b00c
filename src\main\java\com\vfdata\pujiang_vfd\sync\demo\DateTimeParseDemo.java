package com.vfdata.pujiang_vfd.sync.demo;

import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 日期时间解析演示类
 * 用于演示修复后的时间格式解析功能
 */
@Slf4j
public class DateTimeParseDemo {

    public static void main(String[] args) {
        log.info("=== 日期时间解析演示 ===");
        
        // 测试各种格式
        String[] testDates = {
            "2025-06-23 00:00:13",      // 原始错误格式
            "2025-06-23 +08",           // 带时区的日期
            "2025-06-23",               // 标准日期
            "2025-06-23T00:00:13",      // ISO格式
            "2025-06-23 00:00:13.123",  // 带毫秒
            "2025-06-23T00:00:13+08:00" // 完整ISO格式
        };
        
        for (String dateStr : testDates) {
            log.info("原始字符串: {}", dateStr);
            
            LocalDate date = DateTimeParseUtil.parseDate(dateStr);
            LocalDateTime dateTime = DateTimeParseUtil.parseDateTime(dateStr);
            
            log.info("  解析为日期: {}", date);
            log.info("  解析为日期时间: {}", dateTime);
            log.info("---");
        }
        
        log.info("=== 演示完成 ===");
    }
}

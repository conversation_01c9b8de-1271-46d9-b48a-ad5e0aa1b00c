package com.vfdata.pujiang_vfd.sync.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class DateTimeParseUtil {

    // 常用的日期时间格式
    private static final List<DateTimeFormatter> DATETIME_FORMATTERS = Arrays.asList(
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss XXX"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss XXX"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS XXX"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS XXX"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd XXX"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd Z"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss Z")
    );

    // 常用的日期格式
    private static final List<DateTimeFormatter> DATE_FORMATTERS = Arrays.asList(
        DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        DateTimeFormatter.ofPattern("yyyy/MM/dd"),
        DateTimeFormatter.ofPattern("dd/MM/yyyy")
    );

    /**
     * 智能解析日期时间字符串为LocalDateTime
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        String trimmed = dateTimeStr.trim();

        // 首先尝试不带时区的格式
        for (DateTimeFormatter formatter : DATETIME_FORMATTERS) {
            try {
                return LocalDateTime.parse(trimmed, formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }

        // 如果包含时区信息，尝试解析为ZonedDateTime然后转换为LocalDateTime
        try {
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(trimmed);
            return zonedDateTime.toLocalDateTime();
        } catch (DateTimeParseException e) {
            // 继续尝试其他格式
        }

        // 尝试一些特殊格式，如 "2025-06-23 +08"
        try {
            if (trimmed.matches("\\d{4}-\\d{2}-\\d{2}\\s+[+-]\\d{2}")) {
                // 提取日期部分
                String datePart = trimmed.split("\\s+")[0];
                return LocalDate.parse(datePart).atStartOfDay();
            }
        } catch (Exception e) {
            // 继续尝试
        }

        log.warn("无法解析日期时间字符串: {}", dateTimeStr);
        return null;
    }

    /**
     * 智能解析日期字符串为LocalDate
     * 如果输入包含时间信息，会自动提取日期部分
     *
     * @param dateStr 日期字符串
     * @return LocalDate对象，解析失败返回null
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        String trimmed = dateStr.trim();

        // 特殊处理带时区的日期格式，如 "2025-06-23 +08"
        try {
            if (trimmed.matches("\\d{4}-\\d{2}-\\d{2}\\s+[+-]\\d{2}")) {
                // 提取日期部分
                String datePart = trimmed.split("\\s+")[0];
                return LocalDate.parse(datePart);
            }
        } catch (Exception e) {
            // 继续尝试其他格式
        }

        // 首先尝试解析为日期时间，然后提取日期部分
        LocalDateTime dateTime = parseDateTime(trimmed);
        if (dateTime != null) {
            return dateTime.toLocalDate();
        }

        // 如果不是日期时间格式，尝试纯日期格式
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDate.parse(trimmed, formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }

        log.warn("无法解析日期字符串: {}", dateStr);
        return null;
    }

    /**
     * 安全地从Map中获取字符串值
     * 
     * @param data 数据Map
     * @param key 键名
     * @return 字符串值，如果不存在或为null则返回null
     */
    public static String getStringValue(java.util.Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 安全地从Map中获取Long值
     * 
     * @param data 数据Map
     * @param key 键名
     * @return Long值，如果不存在、为null或无法转换则返回null
     */
    public static Long getLongValue(java.util.Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }
        
        try {
            if (value instanceof Number) {
                return ((Number) value).longValue();
            } else {
                return Long.valueOf(value.toString());
            }
        } catch (NumberFormatException e) {
            log.warn("无法转换为Long: key={}, value={}", key, value);
            return null;
        }
    }

    /**
     * 安全地从Map中获取Integer值
     *
     * @param data 数据Map
     * @param key 键名
     * @return Integer值，如果不存在、为null或无法转换则返回null
     */
    public static Integer getIntegerValue(java.util.Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else {
                return Integer.valueOf(value.toString());
            }
        } catch (NumberFormatException e) {
            log.warn("无法转换为Integer: key={}, value={}", key, value);
            return null;
        }
    }

    /**
     * 安全地从Map中获取BigDecimal值
     *
     * @param data 数据Map
     * @param key 键名
     * @return BigDecimal值，如果不存在、为null或无法转换则返回null
     */
    public static BigDecimal getBigDecimalValue(java.util.Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            } else if (value instanceof Number) {
                return BigDecimal.valueOf(((Number) value).doubleValue());
            } else {
                return new BigDecimal(value.toString());
            }
        } catch (NumberFormatException e) {
            log.warn("无法转换为BigDecimal: key={}, value={}", key, value);
            return null;
        }
    }
}

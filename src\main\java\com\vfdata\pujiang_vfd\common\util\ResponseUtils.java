package com.vfdata.pujiang_vfd.common.util;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 响应工具类
 */
public class ResponseUtils {

    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        return new Result<>();
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            result.setData(data);
            result.setTotal(list.size());
            result.setMessage("成功获取" + list.size() + "条记录");
        } else {
            result.setData(data);
        }
        return result;
    }

    /**
     * 成功响应（带消息和数据）
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        if (data instanceof List) {
            List<?> list = (List<?>) data;
            result.setData(data);
            result.setTotal(list.size());
        } else {
            result.setData(data);
        }
        result.setMessage(message);
        return result;
    }

    /**
     * 失败响应
     */
    public static <T> Result<T> error() {
        return error(500, "操作失败");
    }

    /**
     * 失败响应（带消息）
     */
    public static <T> Result<T> error(String message) {
        return error(500, message);
    }

    /**
     * 失败响应（带状态码和消息）
     */
    public static <T> Result<T> error(int code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setSuccess(false);
        return result;
    }

    @Data
    public static class Result<T> {
        private int code = 200;
        private String message = "操作成功";
        private boolean success = true;
        private T data;
        private Integer total;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS")
        private LocalDateTime timestamp = LocalDateTime.now();
    }
} 
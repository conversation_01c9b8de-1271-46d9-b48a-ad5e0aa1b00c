package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class NewEnergyEquipmentInfoService {

    private final NewEnergyEquipmentInfoRepository equipmentInfoRepository;

    /**
     * 获取新能源车间最新设备信息
     */
    public NewEnergyEquipmentInfoDTO getLatestEquipmentInfo() {
        Optional<NewEnergyEquipmentInfo> optionalInfo = equipmentInfoRepository.findLatest();
        if (optionalInfo.isPresent()) {
            NewEnergyEquipmentInfo info = optionalInfo.get();
            NewEnergyEquipmentInfoDTO dto = new NewEnergyEquipmentInfoDTO();
            dto.setId(info.getId());
            dto.setOperating_rate(info.getOperatingRate());
            dto.setTotal_equipment_count(info.getTotalEquipmentCount());
            dto.setOperating_equipment_count(info.getOperatingEquipmentCount());
            dto.setOee_rate(info.getOeeRate());
            dto.setRecord_date(info.getRecordDate());
            dto.setUpdated_by(info.getUpdatedBy());
            dto.setUpdated_at(info.getUpdatedAt());
            dto.setReserved_field1(info.getReservedField1());
            dto.setReserved_field2(info.getReservedField2());
            dto.setReserved_field3(info.getReservedField3());
            return dto;
        }
        return null;
    }
}

package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.common.exception.BusinessException;
import com.vfdata.pujiang_vfd.modules.factory.dto.WorkshopEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.WorkshopEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.factory.repository.WorkshopEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WorkshopEquipmentInfoService {

    private final WorkshopEquipmentInfoRepository workshopEquipmentInfoRepository;

    /**
     * 获取最新的设备信息
     */
    public List<WorkshopEquipmentInfoDTO> getLatestEquipmentInfo(String workshopName) {
        List<WorkshopEquipmentInfo> latestRecords;
        if (workshopName != null && !workshopName.isEmpty()) {
            latestRecords = workshopEquipmentInfoRepository.findLatestRecordsByWorkshopName(workshopName);
        } else {
            latestRecords = workshopEquipmentInfoRepository.findLatestRecords();
        }
        
        if (latestRecords.isEmpty()) {
            throw new BusinessException("未找到设备信息记录");
        }
        
        return latestRecords.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private WorkshopEquipmentInfoDTO convertToDTO(WorkshopEquipmentInfo entity) {
        WorkshopEquipmentInfoDTO dto = new WorkshopEquipmentInfoDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setRecord_date(entity.getRecordDate().atStartOfDay());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
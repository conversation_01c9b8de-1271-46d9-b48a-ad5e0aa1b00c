package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyAchievementRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface AssemblyAchievementRateRepository extends JpaRepository<AssemblyAchievementRate, Long> {

    List<AssemblyAchievementRate> findByRecordDate(LocalDate recordDate);

    List<AssemblyAchievementRate> findByRecordDateBetween(LocalDate startDate, LocalDate endDate);

    List<AssemblyAchievementRate> findByWorkshopNameAndRecordDate(String workshopName, LocalDate recordDate);

    List<AssemblyAchievementRate> findByWorkshopNameAndRecordDateBetween(String workshopName, LocalDate startDate, LocalDate endDate);

    @Query("SELECT a FROM AssemblyAchievementRate a WHERE a.recordDate = (SELECT MAX(a2.recordDate) FROM AssemblyAchievementRate a2)")
    List<AssemblyAchievementRate> findLatest();

    @Query("SELECT a FROM AssemblyAchievementRate a WHERE a.workshopName = :workshopName AND a.recordDate = (SELECT MAX(a2.recordDate) FROM AssemblyAchievementRate a2 WHERE a2.workshopName = :workshopName)")
    List<AssemblyAchievementRate> findLatestByWorkshopName(@Param("workshopName") String workshopName);
}
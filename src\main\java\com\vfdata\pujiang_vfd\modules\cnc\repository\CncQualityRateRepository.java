package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncQualityRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncQualityRateRepository extends JpaRepository<CncQualityRate, Long> {

    /**
     * 获取每个车间最新的质量率记录
     */
    @Query("SELECT c FROM CncQualityRate c WHERE c.updatedAt = (SELECT MAX(c2.updatedAt) FROM CncQualityRate c2 WHERE c2.workshopName = c.workshopName)")
    List<CncQualityRate> findLatestRecords();
}

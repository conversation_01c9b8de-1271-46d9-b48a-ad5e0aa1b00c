package com.vfdata.pujiang_vfd.modules.assembly.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "FQC缺陷分析信息")
public class FqcDefectAnalysisDTO {

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "缺陷类型名称")
    private String defect_type_name;

    @Schema(description = "缺陷类型数量")
    private Integer defect_type_count;

    @Schema(description = "记录日期")
    private String record_date;
} 
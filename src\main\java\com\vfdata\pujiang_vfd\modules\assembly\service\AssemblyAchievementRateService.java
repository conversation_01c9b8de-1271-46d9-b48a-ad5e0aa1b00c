package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyAchievementRate;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyPlanCompletionRate7Days;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyAchievementRateRepository;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyPlanCompletionRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyAchievementRateService {

    private final AssemblyAchievementRateRepository achievementRateRepository;
    private final AssemblyPlanCompletionRate7DaysRepository planCompletionRate7DaysRepository;

    public List<AchievementRateDTO> getTodayAchievementRate(String workshopName) {
        List<AssemblyAchievementRate> rates;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            rates = achievementRateRepository.findLatestByWorkshopName(workshopName);
        } else {
            rates = achievementRateRepository.findLatest();
        }
        return rates.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    public List<AssemblyPlanCompletionRate7DaysDTO> getWeekAchievementRate(String workshopName) {
        LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);
        List<AssemblyPlanCompletionRate7Days> rates;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            rates = planCompletionRate7DaysRepository.findByWorkshopNameAndRecordDateAfter(workshopName, sevenDaysAgo);
        } else {
            rates = planCompletionRate7DaysRepository.findByRecordDateAfter(sevenDaysAgo);
        }
        return rates.stream()
            .map(this::convertToPlanCompletionDTO)
            .collect(Collectors.toList());
    }

    private AchievementRateDTO convertToDTO(AssemblyAchievementRate rate) {
        AchievementRateDTO dto = new AchievementRateDTO();
        dto.setId(rate.getId());
        dto.setWorkshop_name(rate.getWorkshopName());
        dto.setPlanned_quantity(rate.getPlannedQuantity());
        dto.setActual_quantity(rate.getActualQuantity());
        dto.setAchievement_rate(rate.getAchievementRate());
        dto.setRecord_date(rate.getRecordDate());
        dto.setUpdated_by(rate.getUpdatedBy());
        dto.setUpdated_at(rate.getUpdatedAt());
        dto.setReserved_field1(rate.getReservedField1());
        dto.setReserved_field2(rate.getReservedField2());
        dto.setReserved_field3(rate.getReservedField3());
        return dto;
    }

    private AssemblyPlanCompletionRate7DaysDTO convertToPlanCompletionDTO(AssemblyPlanCompletionRate7Days rate) {
        AssemblyPlanCompletionRate7DaysDTO dto = new AssemblyPlanCompletionRate7DaysDTO();
        dto.setId(rate.getId());
        dto.setWorkshop_name(rate.getWorkshopName());
        dto.setCompletion_rate(rate.getCompletionRate());
        dto.setRecord_date(rate.getRecordDate());
        dto.setUpdated_by(rate.getUpdatedBy());
        dto.setUpdate_date(rate.getUpdateDate());
        dto.setReserved_field1(rate.getReservedField1());
        dto.setReserved_field2(rate.getReservedField2());
        dto.setReserved_field3(rate.getReservedField3());
        return dto;
    }
}
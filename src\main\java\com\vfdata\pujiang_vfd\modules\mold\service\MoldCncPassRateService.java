package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldCncPassRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldCncPassRate;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldCncPassRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldCncPassRateService {

    private final MoldCncPassRateRepository cncPassRateRepository;

    /**
     * 获取CNC合格率数据
     * @param period 周期类型：month返回6条，day返回7条
     */
    public List<MoldCncPassRateDTO> getCncPassRateByPeriod(String period) {
        int limit = "day".equals(period) ? 7 : 6;
        List<MoldCncPassRate> entities = cncPassRateRepository.findLatestRecordsByPeriod(period, limit);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldCncPassRateDTO convertToDTO(MoldCncPassRate entity) {
        MoldCncPassRateDTO dto = new MoldCncPassRateDTO();
        dto.setId(entity.getId());
        dto.setPeriod(entity.getPeriod());
        dto.setPujiang_pass_rate(entity.getPujiangPassRate());
        dto.setBoluo_pass_rate(entity.getBoluoPassRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

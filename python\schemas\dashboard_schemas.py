from pydantic import BaseModel, validator, Field
from datetime import date, datetime
from typing import Optional, Dict, List
from schemas.common_schemas import Response, ListResponse

class CompanyInfoBase(BaseModel):
    """企业信息基础模型"""
    serial_num: Optional[int] = Field(None, description="序号")
    company_info: Optional[str] = Field(None, description="企业信息")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class CompanyInfoCreate(CompanyInfoBase):
    """创建企业信息的请求模型"""
    pass

class CompanyInfo(CompanyInfoBase):
    """企业信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class DeviceBase(BaseModel):
    """设备信息基础模型"""
    device_type: Optional[str] = Field(None, max_length=255, description="设备类型")
    device_num: Optional[str] = Field(None, max_length=255, description="设备编号")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, max_length=20, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, max_length=255, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, max_length=255, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, max_length=255, description="预留字段3")

    @validator('device_type', 'device_num')
    def validate_string_fields(cls, v):
        if not v.strip():
            raise ValueError('字段不能为空字符串')
        return v.strip()

class DeviceCreate(DeviceBase):
    """创建设备信息的请求模型"""
    pass

class Device(DeviceBase):
    """设备信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class EmployeeInOutBase(BaseModel):
    """员工进出记录基础模型"""
    employee_name: Optional[str] = Field(None, max_length=255, description="员工姓名")
    employee_addr: Optional[str] = Field(None, max_length=255, description="员工地址")
    employee_state: Optional[str] = Field(None, max_length=255, description="员工状态")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, max_length=20, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, max_length=255, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, max_length=255, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, max_length=255, description="预留字段3")

    @validator('employee_state')
    def validate_employee_state(cls, v):
        if v is None:
            return v
        valid_states = ['进入', '离开', '不区分']
        if v not in valid_states:
            raise ValueError(f'员工状态必须是以下之一: {", ".join(valid_states)}')
        return v

class EmployeeInOutCreate(EmployeeInOutBase):
    """创建员工进出记录的请求模型"""
    pass

class EmployeeInOut(EmployeeInOutBase):
    """员工进出记录的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class HostelBase(BaseModel):
    """宿舍信息基础模型"""
    hostel_num: Optional[int] = Field(None, description="宿舍总数")
    hostel_use: Optional[int] = Field(None, description="已使用数量")
    hostel_remaining: Optional[int] = Field(None, description="剩余数量")
    hostel_info: Optional[str] = Field(None, description="宿舍信息")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

    @validator('hostel_remaining')
    def validate_hostel_numbers(cls, v, values):
        if 'hostel_num' in values and 'hostel_use' in values:
            if values['hostel_use'] > values['hostel_num']:
                raise ValueError('使用数量不能大于总数量')
            if v != values['hostel_num'] - values['hostel_use']:
                raise ValueError('剩余数量必须等于总数量减去使用数量')
        return v

class HostelCreate(HostelBase):
    """创建宿舍信息的请求模型"""
    pass

class Hostel(HostelBase):
    """宿舍信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class HostelInfoBase(BaseModel):
    """宿舍文字介绍基础模型"""
    hostel_info: Optional[str] = None
    record_date: Optional[date] = None
    updateman: Optional[str] = None
    updatetime: Optional[datetime] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

class HostelInfoCreate(HostelInfoBase):
    """创建宿舍文字介绍请求模型"""
    pass

class HostelInfo(HostelInfoBase):
    """宿舍文字介绍响应模型"""
    id: int
    
    class Config:
        from_attributes = True

class ParkConstructBase(BaseModel):
    """园区建设基础模型"""
    serial_number: Optional[int] = Field(None, description="序号")
    project_name: Optional[str] = Field(None, description="项目名称")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class ParkConstructCreate(ParkConstructBase):
    """创建园区建设信息的请求模型"""
    pass

class ParkConstruct(ParkConstructBase):
    """园区建设信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class ParkInfoBase(BaseModel):
    """园区信息基础模型"""
    type: Optional[str] = Field(None, description="类型")
    num: Optional[float] = Field(None, description="数值")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    record_date: Optional[date] = Field(None, description="记录日期")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class ParkInfoCreate(ParkInfoBase):
    """创建园区信息的请求模型"""
    pass

class ParkInfo(ParkInfoBase):
    """园区信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class PrivateRoomBase(BaseModel):
    """单间信息基础模型"""
    privateroom_num: Optional[int] = Field(None, description="单间总数")
    privateroom_use: Optional[int] = Field(None, description="已使用数量")
    privateroom_remaining: Optional[int] = Field(None, description="剩余数量")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class PrivateRoomCreate(PrivateRoomBase):
    """创建单间信息的请求模型"""
    pass

class PrivateRoom(PrivateRoomBase):
    """单间信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class RealParkingBase(BaseModel):
    """实时停车信息基础模型"""
    parking_addr: Optional[str] = Field(None, description="停车场地址")
    parking_num: Optional[int] = Field(None, description="停车位总数")
    parking_use: Optional[int] = Field(None, description="已使用数量")
    parking_remaining: Optional[int] = Field(None, description="剩余数量")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class RealParkingCreate(RealParkingBase):
    """创建实时停车信息的请求模型"""
    pass

class RealParking(RealParkingBase):
    """实时停车信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class TechnicalBase(BaseModel):
    """技术人员基础模型"""
    personnel_type: Optional[str] = Field(None, description="人员类型")
    personnel_num: Optional[int] = Field(None, description="人员数量")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    record_date: Optional[date] = Field(None, description="记录日期")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class TechnicalCreate(TechnicalBase):
    """创建技术人员信息的请求模型"""
    pass

class Technical(TechnicalBase):
    """技术人员信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class TechnicalEduBase(BaseModel):
    """技术人员学历基础模型"""
    edu_background: Optional[str] = Field(None, description="学历背景")
    edu_num: Optional[int] = Field(None, description="人数")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class TechnicalEduCreate(TechnicalEduBase):
    """创建技术人员学历信息的请求模型"""
    pass

class TechnicalEdu(TechnicalEduBase):
    """技术人员学历信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class TechnicalPatentBase(BaseModel):
    """技术专利基础模型"""
    patent_year: Optional[str] = Field(None, description="专利年份")
    patent_num: Optional[int] = Field(None, description="专利数量")
    record_date: Optional[date] = Field(None, description="记录日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="预留字段1")
    reserved_field2: Optional[str] = Field(None, description="预留字段2")
    reserved_field3: Optional[str] = Field(None, description="预留字段3")

class TechnicalPatentCreate(TechnicalPatentBase):
    """创建技术专利信息的请求模型"""
    pass

class TechnicalPatent(TechnicalPatentBase):
    """技术专利信息的响应模型"""
    id: int = Field(..., description="记录ID")

    class Config:
        from_attributes = True

class StatisticsResponse(BaseModel):
    """统计数据响应模型"""
    total: int = Field(..., description="总数")
    percentage: Optional[float] = Field(None, description="百分比")
    details: Optional[Dict] = Field(None, description="详细信息")
    trend: Optional[List[Dict]] = Field(None, description="趋势数据")

class DashboardOverview(BaseModel):
    """大屏总览数据模型"""
    park_info: Optional[Dict] = Field(None, description="园区信息")
    company_info: Optional[Dict] = Field(None, description="企业信息")
    total_technical: int = Field(ge=0, description="技术人员总数")
    total_patents: int = Field(ge=0, description="专利总数")
    today_inout: int = Field(ge=0, description="今日进出人数")
    parking_statistics: Optional[Dict] = Field(None, description="停车场统计")
    hostel_statistics: Optional[Dict] = Field(None, description="宿舍统计")
    technical_education: Optional[Dict] = Field(None, description="技术人员学历分布")
    technical_distribution: Optional[Dict] = Field(None, description="技术人员分布")
    device_statistics: Optional[Dict] = Field(None, description="设备统计")

    @validator('park_info', 'company_info', 'parking_statistics', 'hostel_statistics',
              'technical_education', 'technical_distribution', 'device_statistics')
    def validate_dict_fields(cls, v):
        if v is not None and not isinstance(v, dict):
            raise ValueError('必须是字典类型')
        return v

class ParkBriefInfo(BaseModel):
    """园区简介信息模型"""
    id: Optional[int] = Field(None, description="ID")
    type: Optional[str] = Field(None, description="类型")
    num: Optional[float] = Field(None, description="数值")
    reserved_field1: Optional[str] = Field(None, description="单位")

    class Config:
        from_attributes = True

class PersonnelDistributionBase(BaseModel):
    """厂区人员分布基础模型"""
    park_name: Optional[str] = None
    total_num: Optional[int] = None
    manager_num: Optional[int] = None
    employee_num: Optional[int] = None
    general_worker_num: Optional[int] = None
    record_date: Optional[date] = None
    reserved_field1: Optional[str] = None
    reserved_field2: Optional[str] = None
    reserved_field3: Optional[str] = None

class PersonnelDistributionCreate(PersonnelDistributionBase):
    """创建厂区人员分布模型"""
    updateman: Optional[str] = None
    updatetime: Optional[datetime] = None

class PersonnelDistribution(PersonnelDistributionBase):
    """厂区人员分布模型"""
    id: int
    updateman: Optional[str] = None
    updatetime: Optional[datetime] = None

    class Config:
        from_attributes = True

class VisitorBase(BaseModel):
    """访客信息基础模型"""
    visitor_type: Optional[str] = Field(None, description="类型（在园员工、离园员工、访客数）")
    visitor_date: Optional[datetime] = Field(None, description="访客日期")
    visitor_num: Optional[int] = Field(None, description="人数")
    record_date: Optional[date] = Field(None, description="日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="供应商数量")
    reserved_field2: Optional[str] = Field(None, description="客户数量")
    reserved_field3: Optional[str] = Field(None, description="其他访客数量")

class VisitorCreate(VisitorBase):
    """创建访客信息的请求模型"""
    pass

class Visitor(BaseModel):
    """访客信息的响应模型"""
    id: int = Field(..., description="记录ID")
    visitor_type: Optional[str] = Field(None, description="类型（在园员工、离园员工、访客数）")
    visitor_date: Optional[datetime] = Field(None, description="访客日期")
    visitor_num: Optional[int] = Field(None, description="人数")
    record_date: Optional[date] = Field(None, description="日期")
    updateman: Optional[str] = Field(None, description="更新人")
    updatetime: Optional[datetime] = Field(None, description="更新时间")
    reserved_field1: Optional[str] = Field(None, description="供应商数量")
    reserved_field2: Optional[str] = Field(None, description="客户数量")
    reserved_field3: Optional[str] = Field(None, description="其他访客数量")

    class Config:
        from_attributes = True
        allow_population_by_field_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        } 
import request from '../request'

/**
 * 获取模具车间人员信息
 * @returns {Promise} 返回人员统计信息数据
 */
export const getPersonnelInfo = async () => {
  return await request.get('/api/v1/mold/personnel-info/latest')
}

/**
 * 获取模具车间设备信息
 */
export const getEquipmentInfo = async () => {
  return await request.get('/api/v1/mold/equipment-info/latest')
}

/**
 * 获取模具车间出勤率
 */
export const getAttendanceRate = async () => {
  return await request.get('/api/v1/mold/attendance-rate/latest')
}

/**
 * 获取模具车间设备状况
 */
export const getWorkshopStatus = async () => {
  return await request.get('/api/v1/mold/workshop-status/latest')
}

/**
 * 获取模具车间EDM合格率
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getEdmPassRateByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/edm-pass-rate/6months?period=${period}`)
}

/**
 * 获取模具车间EDM稼动率
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getEdmUtilizationRateByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/edm-utilization-rate/6months?period=${period}`)
}

/**
 * 获取模具车间CNC合格率
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getCncPassRateByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/cnc-pass-rate/6months?period=${period}`)
}

/**
 * 获取模具车间CNC稼动率
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getCncUtilizationRateByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/cnc-utilization-rate/6months?period=${period}`)
}

/**
 * 获取模具车间WE合格率
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getWePassRateByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/we-pass-rate/6months?period=${period}`)
}

/**
 * 获取模具车间WE稼动率
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getWeUtilizationRateByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/we-utilization-rate/6months?period=${period}`)
}

/**
 * 获取模具车间新模信息
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getNewMoldInfoByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/new-mold-info/6months?period=${period}`)
}

/**
 * 获取模具车间修模信息
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getRepairMoldInfoByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/repair-mold-info/6months?period=${period}`)
}

/**
 * 获取模具车间改模信息
 * @param {string} period - 周期类型：'month'返回6条，'day'返回7条
 */
export const getChangeMoldInfoByPeriod = async (period) => {
  return await request.get(`/api/v1/mold/change-mold-info/6months?period=${period}`)
}

/**
 * 获取模具车间模具进度
 */
export const getMoldProgress = async () => {
  return await request.get('/api/v1/mold/progress/latest')
}

/**
 * 获取模具车间物料消耗
 */
export const getMaterialConsumption = async () => {
  return await request.get('/api/v1/mold/material-consumption/latest')
}

/**
 * 模具车间相关API集合
 */
export default {
  // 基础信息接口
  getPersonnelInfo,
  getEquipmentInfo,
  getAttendanceRate,
  getWorkshopStatus,

  // 6个月数据接口（支持period参数）
  getEdmPassRateByPeriod,
  getEdmUtilizationRateByPeriod,
  getCncPassRateByPeriod,
  getCncUtilizationRateByPeriod,
  getWePassRateByPeriod,
  getWeUtilizationRateByPeriod,

  // 模具相关接口（支持period参数）
  getNewMoldInfoByPeriod,
  getRepairMoldInfoByPeriod,
  getChangeMoldInfoByPeriod,
  getMoldProgress,

  // 物料消耗接口
  getMaterialConsumption
}

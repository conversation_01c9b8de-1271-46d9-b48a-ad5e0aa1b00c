package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "ioc_qc_annual_project_category")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "年度项目分类信息")
public class AnnualProjectCategory extends BaseEntity {

    @Column(name = "project_name", length = 100)
    @Schema(description = "项目名称")
    private String projectName;

    @Column(name = "project_count")
    @Schema(description = "项目数量")
    private Integer projectCount;
} 
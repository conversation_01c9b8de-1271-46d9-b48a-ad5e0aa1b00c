package com.vfdata.pujiang_vfd.modules.safety.controller;

import com.vfdata.pujiang_vfd.modules.safety.dto.*;
import com.vfdata.pujiang_vfd.modules.safety.entity.*;
import com.vfdata.pujiang_vfd.modules.safety.repository.*;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "Safety安全管理接口")
@RestController
@RequestMapping("/safety")
@RequiredArgsConstructor
public class SafetyController {

    // 时间格式化器
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final VisitorRecordRepository visitorRecordRepository;
    private final VehicleRecordRepository vehicleRecordRepository;
    private final WastewaterMonitoringRepository wastewaterMonitoringRepository;
    private final FireEquipmentInfoRepository fireEquipmentInfoRepository;
    private final EnergyConsumptionRecordRepository energyConsumptionRecordRepository;
    private final ActualEnergyConsumptionRepository actualEnergyConsumptionRepository;
    private final PhotovoltaicEnergyMetricRepository photovoltaicEnergyMetricRepository;
    private final PhotovoltaicEnergyStatRepository photovoltaicEnergyStatRepository;

    @GetMapping("/visitor-records/")
    @Operation(summary = "获取访客记录列表", description = "获取最近15条访客记录")
    public ResponseUtils.Result<List<VisitorRecordDTO>> getVisitorRecords() {
        try {
            List<VisitorRecord> records = visitorRecordRepository.findTop15ByOrderByVisitTimeDesc();
            List<VisitorRecordDTO> result = records.stream()
                    .map(this::convertVisitorRecordToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取访客记录列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/vehicle-records/")
    @Operation(summary = "获取车辆记录列表", description = "获取最近15条车辆进出记录")
    public ResponseUtils.Result<List<VehicleRecordDTO>> getVehicleRecords() {
        try {
            List<VehicleRecord> records = vehicleRecordRepository.findTop15ByOrderByEntryTimeDesc();
            List<VehicleRecordDTO> result = records.stream()
                    .map(this::convertVehicleRecordToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取车辆记录列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/wastewater-monitoring/")
    @Operation(summary = "获取废水监控记录列表", description = "获取所有废水监控记录")
    public ResponseUtils.Result<List<WastewaterMonitoringDTO>> getWastewaterMonitoring() {
        try {
            List<WastewaterMonitoring> records = wastewaterMonitoringRepository.findAllByOrderByRecordTimeDesc();
            List<WastewaterMonitoringDTO> result = records.stream()
                    .map(this::convertWastewaterMonitoringToDTO)
                    .collect(Collectors.toList());
            return ResponseUtils.success(result);
        } catch (Exception e) {
            return ResponseUtils.error("获取废水监控记录列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/fire-equipment/")
    @Operation(summary = "获取消防设备信息列表", description = "获取所有消防设备信息记录")
    public ResponseUtils.Result<List<FireEquipmentInfo>> getFireEquipment() {
        try {
            List<FireEquipmentInfo> records = fireEquipmentInfoRepository.findAllByOrderByRecordDateDesc();
            return ResponseUtils.success(records);
        } catch (Exception e) {
            return ResponseUtils.error("获取消防设备信息列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/energy-consumption/")
    @Operation(summary = "获取能耗统计记录列表", description = "获取所有能耗统计记录")
    public ResponseUtils.Result<List<EnergyConsumptionRecord>> getEnergyConsumption() {
        try {
            List<EnergyConsumptionRecord> records = energyConsumptionRecordRepository.findAllByOrderByRecordTimeDesc();
            return ResponseUtils.success(records);
        } catch (Exception e) {
            return ResponseUtils.error("获取能耗统计记录列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/actual-energy-consumption/")
    @Operation(summary = "获取实际能耗统计记录列表", description = "获取所有实际能耗统计记录")
    public ResponseUtils.Result<List<ActualEnergyConsumption>> getActualEnergyConsumption() {
        try {
            List<ActualEnergyConsumption> records = actualEnergyConsumptionRepository.findAllByOrderByRecordTimeDesc();
            return ResponseUtils.success(records);
        } catch (Exception e) {
            return ResponseUtils.error("获取实际能耗统计记录列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/photovoltaic-energy-metrics/")
    @Operation(summary = "获取光伏能源指标记录列表", description = "获取所有光伏能源指标记录")
    public ResponseUtils.Result<List<PhotovoltaicEnergyMetric>> getPhotovoltaicEnergyMetrics() {
        try {
            List<PhotovoltaicEnergyMetric> records = photovoltaicEnergyMetricRepository.findAllByOrderByRecordTimeDesc();
            return ResponseUtils.success(records);
        } catch (Exception e) {
            return ResponseUtils.error("获取光伏能源指标记录列表失败：" + e.getMessage());
        }
    }

    @GetMapping("/photovoltaic-energy-stats/")
    @Operation(summary = "获取光伏能源统计记录列表", description = "获取所有光伏能源统计记录")
    public ResponseUtils.Result<List<PhotovoltaicEnergyStat>> getPhotovoltaicEnergyStats() {
        try {
            List<PhotovoltaicEnergyStat> records = photovoltaicEnergyStatRepository.findAllByOrderByRecordTimeDesc();
            return ResponseUtils.success(records);
        } catch (Exception e) {
            return ResponseUtils.error("获取光伏能源统计记录列表失败：" + e.getMessage());
        }
    }

    // DTO转换方法
    private VisitorRecordDTO convertVisitorRecordToDTO(VisitorRecord record) {
        VisitorRecordDTO dto = new VisitorRecordDTO();
        dto.setId(record.getId());
        dto.setVisitor_name(record.getVisitorName());
        dto.setChannel_name(record.getChannelName());
        dto.setVisit_time(formatDateTime(record.getVisitTime()));
        dto.setStatus(record.getStatus());
        dto.setUpdated_by(record.getUpdatedBy());
        dto.setUpdate_date(formatDate(record.getUpdateDate()));
        return dto;
    }

    private VehicleRecordDTO convertVehicleRecordToDTO(VehicleRecord record) {
        VehicleRecordDTO dto = new VehicleRecordDTO();
        dto.setId(record.getId());
        dto.setLicense_plate(record.getLicensePlate());
        dto.setChannel_name(record.getChannelName());
        dto.setEntry_time(formatDateTime(record.getEntryTime()));
        dto.setStatus(record.getStatus());
        dto.setUpdated_by(record.getUpdatedBy());
        dto.setUpdate_date(formatDate(record.getUpdateDate()));
        return dto;
    }

    private WastewaterMonitoringDTO convertWastewaterMonitoringToDTO(WastewaterMonitoring monitoring) {
        WastewaterMonitoringDTO dto = new WastewaterMonitoringDTO();
        dto.setId(monitoring.getId());
        dto.setCod(monitoring.getCod());
        dto.setAmmonia_nitrogen(monitoring.getAmmoniaNitrogen());
        dto.setRecord_time(formatDateTime(monitoring.getRecordTime()));
        dto.setUpdated_by(monitoring.getUpdatedBy());
        dto.setUpdate_date(formatDate(monitoring.getUpdateDate()));
        return dto;
    }

    /**
     * 格式化LocalDate为字符串，不包含时区信息
     */
    private String formatDate(LocalDate date) {
        return date != null ? date.format(DATE_FORMATTER) : null;
    }

    /**
     * 格式化LocalDateTime为字符串，不包含时区信息
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMATTER) : null;
    }
}

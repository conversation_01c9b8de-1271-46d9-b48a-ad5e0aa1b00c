package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.ParkInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ParkInfoRepository extends JpaRepository<ParkInfo, Long> {
    
    List<ParkInfo> findAllByOrderByRecordDateDesc();
    
    @Query(value = "select id,type,num,reserved_field1 from ioc_dp_parkinfo where type not in ('园区人数','安全运营天数') " +
           "union all " +
           "select 4 as id,'园区人数' as type,sum(num) as num,'人' as reserved_field1 " +
           "from ioc_dp_staff_visitor where type != '离园员工' and id = (select max(id) from ioc_dp_staff_visitor) " +
           "union all " +
           "SELECT 5 as id, '安全运营天数' as type, " +
           "CURRENT_DATE-to_date(reserved_field1,'YYYY-MM-DD') as num, '天' as reserved_field1 " +
           "FROM ioc_dp_parkinfo where type='安全运营天数'", nativeQuery = true)
    List<Object[]> findParkBriefInfo();

    @Query(value = "SELECT parking_addr, parking_num AS total_parking, parking_use AS total_used, parking_remaining AS total_remaining, " +
           "CASE WHEN parking_num > 0 THEN ROUND(CAST(parking_use AS decimal) / parking_num * 100, 2) ELSE 0 END AS usage_rate " +
           "FROM ioc_dp_realparking " +
           "WHERE id IN ( " +
           "    SELECT max(id) " +
           "    FROM ioc_dp_realparking " +
           "    GROUP BY parking_addr " +
           ") " +
           "UNION ALL " +
           "SELECT '总车位' AS parking_addr, " +
           "    SUM(parking_num) AS total_parking, " +
           "    SUM(parking_use) AS total_used, " +
           "    SUM(parking_remaining) AS total_remaining, " +
           "    CASE WHEN SUM(parking_num) > 0 THEN ROUND(CAST(SUM(parking_use) AS decimal) / SUM(parking_num) * 100, 2) ELSE 0 END AS usage_rate " +
           "FROM ioc_dp_realparking " +
           "WHERE id IN ( " +
           "    SELECT max(id) " +
           "    FROM ioc_dp_realparking " +
           "    GROUP BY parking_addr " +
           ")", nativeQuery = true)
    List<Object[]> findParkingStatistics();
}

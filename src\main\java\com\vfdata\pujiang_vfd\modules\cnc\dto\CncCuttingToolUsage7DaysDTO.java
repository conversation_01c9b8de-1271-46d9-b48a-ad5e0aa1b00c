package com.vfdata.pujiang_vfd.modules.cnc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "CNC车间刀具用量近7天DTO")
public class CncCuttingToolUsage7DaysDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "刀具用量")
    private BigDecimal cuttingtool_usage;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

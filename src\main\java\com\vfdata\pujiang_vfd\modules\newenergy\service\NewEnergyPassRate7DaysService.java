package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyPassRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPassRate7Days;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyPassRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyPassRate7DaysService {

    private final NewEnergyPassRate7DaysRepository passRate7DaysRepository;

    /**
     * 获取最新7条性能测试良率数据
     */
    public List<NewEnergyPassRate7DaysDTO> getPassRate7Days() {
        List<NewEnergyPassRate7Days> passRateList = passRate7DaysRepository.findTop7OrderByRecordDateDesc();
        
        return passRateList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyPassRate7DaysDTO convertToDTO(NewEnergyPassRate7Days entity) {
        NewEnergyPassRate7DaysDTO dto = new NewEnergyPassRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

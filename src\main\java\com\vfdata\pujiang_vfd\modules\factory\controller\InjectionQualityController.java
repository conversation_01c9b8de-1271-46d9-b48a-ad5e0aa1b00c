package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.InjectionQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.InjectionQualityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/quality")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class InjectionQualityController {

    private final InjectionQualityService injectionQualityService;

    @GetMapping("/injection")
    @Operation(summary = "获取注塑质量信息", description = "获取每个车间最新记录日期的注塑质量信息")
    public ResponseUtils.Result<List<InjectionQualityDTO>> getInjectionQualityInfo() {
        List<InjectionQualityDTO> qualityInfo = injectionQualityService.getInjectionQualityInfo();
        return ResponseUtils.success(qualityInfo);
    }
} 
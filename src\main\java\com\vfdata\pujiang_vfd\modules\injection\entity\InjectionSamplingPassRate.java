package com.vfdata.pujiang_vfd.modules.injection.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zscj_sampling_inspection_pass_rate")
public class InjectionSamplingPassRate {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "workshop_name", length = 20)
    private String workshopName;

    @Column(name = "pass_rate", precision = 5, scale = 2)
    private BigDecimal passRate;

    @Column(name = "record_date")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    private String updatedBy;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    private String reservedField3;
} 
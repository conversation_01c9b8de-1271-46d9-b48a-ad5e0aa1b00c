package com.vfdata.pujiang_vfd.modules.newenergy.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_xnycj_tonglv_usage")
@Schema(description = "新能源车间铜铝用量")
public class NewEnergyTongLvUsage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "tong_usage", precision = 7, scale = 2)
    @Schema(description = "铜用量")
    private BigDecimal tongUsage;

    @Column(name = "lv_usage", precision = 7, scale = 2)
    @Schema(description = "铝用量")
    private BigDecimal lvUsage;

    @Column(name = "record_date")
    @Schema(description = "日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "备用字段3")
    private String reservedField3;
}

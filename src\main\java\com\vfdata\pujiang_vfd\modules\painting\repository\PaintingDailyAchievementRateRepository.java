package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingDailyAchievementRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingDailyAchievementRateRepository extends JpaRepository<PaintingDailyAchievementRate, Long> {

    /**
     * 获取最新记录日期的所有车间数据
     */
    @Query("SELECT p FROM PaintingDailyAchievementRate p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingDailyAchievementRate p2)")
    List<PaintingDailyAchievementRate> findLatestRecords();

    /**
     * 获取指定车间最新记录日期的数据
     */
    @Query("SELECT p FROM PaintingDailyAchievementRate p WHERE p.workshopName = :workshopName AND p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingDailyAchievementRate p2 WHERE p2.workshopName = :workshopName)")
    PaintingDailyAchievementRate findLatestRecordByWorkshop(@Param("workshopName") String workshopName);
}

package com.vfdata.pujiang_vfd.modules.assembly.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "组装车间人员信息DTO")
public class AssemblyPersonnelInfoDTO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "人员类型")
    private String personnel_type;
    
    @Schema(description = "职员人数")
    private Integer staff_count;
    
    @Schema(description = "普工人数")
    private Integer general_worker_count;
    
    @Schema(description = "记录日期")
    private LocalDate record_date;
    
    @Schema(description = "更新人")
    private String updated_by;
    
    @Schema(description = "更新时间")
    private LocalDateTime updated_at;
    
    @Schema(description = "预留字段1")
    private String reserved_field1;
    
    @Schema(description = "预留字段2")
    private String reserved_field2;
    
    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
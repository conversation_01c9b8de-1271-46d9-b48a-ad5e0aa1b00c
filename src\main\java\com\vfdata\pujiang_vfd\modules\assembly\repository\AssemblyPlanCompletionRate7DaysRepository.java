package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyPlanCompletionRate7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface AssemblyPlanCompletionRate7DaysRepository extends JpaRepository<AssemblyPlanCompletionRate7Days, Long> {

    @Query("SELECT a FROM AssemblyPlanCompletionRate7Days a WHERE a.recordDate >= :startDate ORDER BY a.recordDate DESC")
    List<AssemblyPlanCompletionRate7Days> findByRecordDateAfter(@Param("startDate") LocalDate startDate);

    @Query("SELECT a FROM AssemblyPlanCompletionRate7Days a WHERE a.workshopName = :workshopName AND a.recordDate >= :startDate ORDER BY a.recordDate DESC")
    List<AssemblyPlanCompletionRate7Days> findByWorkshopNameAndRecordDateAfter(
        @Param("workshopName") String workshopName, 
        @Param("startDate") LocalDate startDate
    );

    @Query("SELECT a FROM AssemblyPlanCompletionRate7Days a ORDER BY a.recordDate DESC")
    List<AssemblyPlanCompletionRate7Days> findAllOrderByRecordDateDesc();
}

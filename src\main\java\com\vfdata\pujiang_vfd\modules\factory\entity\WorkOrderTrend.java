package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Table(name = "ioc_qc_work_order_close_rate_6months")
@Schema(description = "工单趋势信息")
@Getter
@Setter
public class WorkOrderTrend extends BaseEntity {
    
    @Schema(description = "车间名称")
    @Column(name = "workshop_name")
    private String workshopName;

    @Schema(description = "已完成数量")
    @Column(name = "completed_count")
    private Integer completedCount;

    @Schema(description = "未关闭数量")
    @Column(name = "not_closed_count")
    private Integer notClosedCount;

    @Schema(description = "关闭率")
    @Column(name = "close_rate")
    private BigDecimal closeRate;
    
    @Schema(description = "预留字段1")
    private String reservedField1;
    
    @Schema(description = "预留字段2")
    private String reservedField2;
    
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
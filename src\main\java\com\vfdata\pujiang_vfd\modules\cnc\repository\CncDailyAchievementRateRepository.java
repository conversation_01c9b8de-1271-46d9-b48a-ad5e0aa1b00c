package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncDailyAchievementRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface CncDailyAchievementRateRepository extends JpaRepository<CncDailyAchievementRate, Long> {

    /**
     * 获取最新日期的所有计划达成率记录
     */
    @Query("SELECT c FROM CncDailyAchievementRate c WHERE c.recordDate = (SELECT MAX(c2.recordDate) FROM CncDailyAchievementRate c2)")
    List<CncDailyAchievementRate> findLatestRecords();
}

package com.vfdata.pujiang_vfd.modules.mold.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_mjcj_repair_mold_info")
@Schema(description = "模具车间修模信息")
public class MoldRepairMoldInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "period")
    @Schema(description = "周期")
    private String period;

    @Column(name = "pujian_count")
    @Schema(description = "浦江修模数量")
    private Integer pujiangCount;

    @Column(name = "boluo_count")
    @Schema(description = "博罗修模数量")
    private Integer boluoCount;

    @Column(name = "pujian_ratio")
    @Schema(description = "浦江修模占比")
    private BigDecimal pujiangRatio;

    @Column(name = "boluo_ratio")
    @Schema(description = "博罗修模占比")
    private BigDecimal boluoRatio;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

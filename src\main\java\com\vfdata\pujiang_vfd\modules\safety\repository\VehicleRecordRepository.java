package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.VehicleRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface VehicleRecordRepository extends JpaRepository<VehicleRecord, Long> {
    
    List<VehicleRecord> findTop15ByOrderByEntryTimeDesc();
}

package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingEnvironmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/environment-info")
@RequiredArgsConstructor
public class PaintingEnvironmentInfoController {

    private final PaintingEnvironmentInfoService environmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取喷涂车间环境信息", description = "获取喷涂车间最新的环境信息")
    public ResponseUtils.Result<List<PaintingEnvironmentInfoDTO>> getLatestEnvironmentInfo() {
        try {
            List<PaintingEnvironmentInfoDTO> data = environmentInfoService.getLatestEnvironmentInfo();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间环境信息失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.MaterialPassRateTrendDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.MaterialPassRateTrendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/material-pass-rate")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class MaterialPassRateTrendController {

    private final MaterialPassRateTrendService materialPassRateTrendService;

    @GetMapping("/trend")
    @Operation(summary = "获取物料合格率趋势信息", description = "获取每个车间类型的最新6条物料合格率趋势数据")
    public ResponseUtils.Result<List<MaterialPassRateTrendDTO>> getMaterialPassRateTrendInfo() {
        List<MaterialPassRateTrendDTO> trendInfo = materialPassRateTrendService.getMaterialPassRateTrendInfo();
        return ResponseUtils.success(trendInfo);
    }
} 
package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionWorkshopStatus;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionWorkshopStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionWorkshopStatusService {
    
    private final InjectionWorkshopStatusRepository workshopStatusRepository;
    
    public List<InjectionWorkshopStatusDTO> getWorkshopStatus(String workshopName) {
        List<InjectionWorkshopStatus> statusList;
        if (workshopName == null || workshopName.trim().isEmpty()) {
            statusList = workshopStatusRepository.findLatestStatus();
        } else {
            statusList = workshopStatusRepository.findLatestStatusByWorkshopName(workshopName);
        }

        if (statusList.isEmpty()) {
            throw new RuntimeException("没有找到车间设备状态记录");
        }

        return statusList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    private InjectionWorkshopStatusDTO convertToDTO(InjectionWorkshopStatus status) {
        InjectionWorkshopStatusDTO dto = new InjectionWorkshopStatusDTO();
        dto.setId(status.getId());
        dto.setWorkshop_name(status.getWorkshopName());
        dto.setTotal_equipment(status.getTotalEquipment());
        dto.setOperating_equipment(status.getOperatingEquipment());
        dto.setOperating_rate(status.getOperatingRate());
        dto.setRecord_date(status.getRecordDate());
        dto.setUpdated_by(status.getUpdatedBy());
        dto.setUpdated_at(status.getUpdatedAt());
        dto.setReserved_field1(status.getReservedField1());
        dto.setReserved_field2(status.getReservedField2());
        dto.setReserved_field3(status.getReservedField3());
        return dto;
    }
} 
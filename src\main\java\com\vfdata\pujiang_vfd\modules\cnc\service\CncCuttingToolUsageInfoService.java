package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncCuttingToolUsageInfoDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncCuttingToolUsageInfo;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncCuttingToolUsageInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncCuttingToolUsageInfoService {

    private final CncCuttingToolUsageInfoRepository cuttingToolUsageInfoRepository;

    /**
     * 获取每个车间最新的刀具消耗信息
     */
    public List<CncCuttingToolUsageInfoDTO> getLatestCuttingToolUsageInfo() {
        List<CncCuttingToolUsageInfo> cuttingToolUsageInfos = cuttingToolUsageInfoRepository.findLatestRecords();
        return cuttingToolUsageInfos.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncCuttingToolUsageInfoDTO convertToDTO(CncCuttingToolUsageInfo entity) {
        CncCuttingToolUsageInfoDTO dto = new CncCuttingToolUsageInfoDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setCuttingtool_usage(entity.getCuttingtoolUsage());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyTestDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyTestDefectClassificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/test-defect-classification")
@RequiredArgsConstructor
public class NewEnergyTestDefectClassificationController {

    private final NewEnergyTestDefectClassificationService testDefectClassificationService;

    @GetMapping
    @Operation(summary = "获取测试不良分类数据", description = "获取最新记录日期的所有测试不良分类数据")
    public ResponseUtils.Result<List<NewEnergyTestDefectClassificationDTO>> getLatestTestDefectClassification() {
        try {
            List<NewEnergyTestDefectClassificationDTO> data = testDefectClassificationService.getLatestTestDefectClassification();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取测试不良分类数据失败：" + e.getMessage());
        }
    }
}

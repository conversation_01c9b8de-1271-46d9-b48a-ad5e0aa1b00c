package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "宿舍文字介绍DTO")
public class HostelInfoDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "文字介绍")
    private String hostel_info;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

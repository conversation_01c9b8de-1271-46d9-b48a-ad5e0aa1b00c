package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionInspectionPassRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionInspectionPassRateService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/inspection-pass-rate")
@RequiredArgsConstructor
public class InjectionInspectionPassRateController {

    private final InjectionInspectionPassRateService injectionInspectionPassRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取车间巡检合格率数据")
    public ResponseUtils.Result<List<InjectionInspectionPassRateDTO>> getPassRates(
            @RequestParam(required = false) String workshop_name) {
        List<InjectionInspectionPassRateDTO> rateList = injectionInspectionPassRateService.getPassRates(workshop_name);
        if (rateList == null) {
            return ResponseUtils.error("未找到巡检合格率数据");
        }
        return ResponseUtils.success(rateList);
    }
} 
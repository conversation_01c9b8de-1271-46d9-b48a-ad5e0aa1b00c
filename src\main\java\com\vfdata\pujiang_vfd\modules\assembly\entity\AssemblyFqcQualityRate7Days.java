package com.vfdata.pujiang_vfd.modules.assembly.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zzcj_fqc_quality_rate_7days")
@Schema(description = "组装车间7天FQC质量率")
public class AssemblyFqcQualityRate7Days {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    @Column(name = "workshop_name")
    private String workshopName;

    @Schema(description = "质量率")
    @Column(name = "quality_rate")
    private Float qualityRate;

    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;

    @Schema(description = "更新人")
    @Column(name = "updated_by")
    private String updatedBy;

    @Schema(description = "更新时间")
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Schema(description = "预留字段1")
    @Column(name = "reserved_field1")
    private String reservedField1;

    @Schema(description = "预留字段2")
    @Column(name = "reserved_field2")
    private String reservedField2;

    @Schema(description = "预留字段3")
    @Column(name = "reserved_field3")
    private String reservedField3;
}

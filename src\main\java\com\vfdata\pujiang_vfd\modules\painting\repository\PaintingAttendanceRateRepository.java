package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingAttendanceRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PaintingAttendanceRateRepository extends JpaRepository<PaintingAttendanceRate, Long> {

    /**
     * 获取最新的出勤率记录
     */
    @Query("SELECT p FROM PaintingAttendanceRate p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingAttendanceRate p2)")
    PaintingAttendanceRate findLatestRecord();
}

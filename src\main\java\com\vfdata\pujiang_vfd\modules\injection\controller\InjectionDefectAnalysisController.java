package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionDefectAnalysisDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionDefectAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/injection/defect-analysis")
@Tag(name = "注塑车间页面接口")
@RequiredArgsConstructor
public class InjectionDefectAnalysisController {
    
    private final InjectionDefectAnalysisService defectAnalysisService;
    
    @GetMapping("/latest")
    @Operation(summary = "抽巡检不良类型分布", description = "获取指定车间或所有车间的抽巡检不良类型分布数据")
    public ResponseUtils.Result<List<InjectionDefectAnalysisDTO>> getLatestDefectAnalyses(
            @Parameter(description = "车间名称，不传则获取所有车间数据") 
            @RequestParam(required = false) String workshop_name) {
        try {
            List<InjectionDefectAnalysisDTO> defectAnalyses = defectAnalysisService.getLatestDefectAnalyses(workshop_name);
            return ResponseUtils.success(defectAnalyses);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("没有找到")) {
                return ResponseUtils.error(404, e.getMessage());
            }
            return ResponseUtils.error("获取缺陷分析数据失败：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取缺陷分析数据失败：" + e.getMessage());
        }
    }
} 
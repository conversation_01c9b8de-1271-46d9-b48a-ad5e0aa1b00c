package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.CoatingQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.CoatingQuality;
import com.vfdata.pujiang_vfd.modules.factory.repository.CoatingQualityRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CoatingQualityService {

    private final CoatingQualityRepository coatingQualityRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取涂装巡检检验合格率
     * 返回最新的7条数据
     */
    public List<CoatingQualityDTO> getCoatingQualityInfo() {
        List<CoatingQuality> qualityList = coatingQualityRepository.findLatestByWorkshopName();
        return qualityList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private CoatingQualityDTO convertToDTO(CoatingQuality entity) {
        CoatingQualityDTO dto = new CoatingQualityDTO();
        dto.setId(entity.getId());
        dto.setPass_rate(entity.getPassRate());
        dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt().format(DATETIME_FORMATTER));
        return dto;
    }
} 
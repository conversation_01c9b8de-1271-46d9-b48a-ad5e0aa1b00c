package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingProjectStatusRepository extends JpaRepository<PaintingProjectStatus, Long> {

    /**
     * 获取指定车间的所有项目状态数据
     */
    @Query("SELECT p FROM PaintingProjectStatus p WHERE p.workshopName = :workshopName ORDER BY p.recordDate DESC")
    List<PaintingProjectStatus> findByWorkshopName(@Param("workshopName") String workshopName);
}

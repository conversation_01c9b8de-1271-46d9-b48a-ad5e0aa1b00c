package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.PersonnelDistribution;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface PersonnelDistributionRepository extends JpaRepository<PersonnelDistribution, Long> {
    
    @Query("SELECT p FROM PersonnelDistribution p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PersonnelDistribution p2)")
    List<PersonnelDistribution> findLatest();
    
    List<PersonnelDistribution> findAllByOrderByRecordDateDesc();
}

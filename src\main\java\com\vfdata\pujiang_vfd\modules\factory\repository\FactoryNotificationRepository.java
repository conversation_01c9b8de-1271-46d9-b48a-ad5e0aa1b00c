package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.FactoryNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FactoryNotificationRepository extends JpaRepository<FactoryNotification, Long> {

    @Query("SELECT f FROM FactoryNotification f WHERE f.recordDate = (SELECT MAX(f2.recordDate) FROM FactoryNotification f2) order by f.id desc")
    List<FactoryNotification> findLatestNotifications();
} 
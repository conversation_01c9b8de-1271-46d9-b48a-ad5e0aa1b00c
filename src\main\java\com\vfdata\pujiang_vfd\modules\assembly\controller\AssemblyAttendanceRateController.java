package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyAttendanceRateService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/attendance")
@RequiredArgsConstructor
public class AssemblyAttendanceRateController {

    private final AssemblyAttendanceRateService attendanceRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新出勤率数据")
    public ResponseUtils.Result<AssemblyAttendanceRateDTO> getLatestAttendanceRate() {
        try {
            AssemblyAttendanceRateDTO rateList = attendanceRateService.getLatestAttendanceRate();
            return ResponseUtils.success(rateList);
        } catch (Exception e) {
            return ResponseUtils.error("获取出勤率数据失败：" + e.getMessage());
        }
    }
} 
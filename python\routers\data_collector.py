from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from datetime import datetime, date

from database import get_db
from schemas.common_schemas import Response, ListResponse
from schemas import dashboard_schemas
from models import dashboard_models

router = APIRouter(
    prefix="/api/v1/collector",
    tags=["数据采集"]
)

@router.post("/technical-patent/batch/", 
    response_model=Response[List[dashboard_schemas.TechnicalPatent]],
    summary="批量采集技术专利数据",
    description="批量添加技术专利相关数据")
async def collect_technical_patent(
    patents: List[dashboard_schemas.TechnicalPatentCreate],
    db: Session = Depends(get_db)
):
    """批量采集技术专利数据"""
    db_patents = []
    try:
        for patent in patents:
            db_patent = dashboard_models.TechnicalPatent(**patent.dict())
            db.add(db_patent)
            db_patents.append(db_patent)
        db.commit()
        for patent in db_patents:
            db.refresh(patent)
        return Response(code=200, message="数据采集成功", data=db_patents)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据采集失败: {str(e)}")

@router.post("/technical/batch/", 
    response_model=Response[List[dashboard_schemas.Technical]],
    summary="批量采集技术人员数据",
    description="批量添加技术人员相关数据")
async def collect_technical(
    technicals: List[dashboard_schemas.TechnicalCreate],
    db: Session = Depends(get_db)
):
    """批量采集技术人员数据"""
    db_technicals = []
    try:
        for technical in technicals:
            db_technical = dashboard_models.Technical(**technical.dict())
            db.add(db_technical)
            db_technicals.append(db_technical)
        db.commit()
        for technical in db_technicals:
            db.refresh(technical)
        return Response(code=200, message="数据采集成功", data=db_technicals)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据采集失败: {str(e)}")

@router.post("/technical-edu/batch/", 
    response_model=Response[List[dashboard_schemas.TechnicalEdu]],
    summary="批量采集技术人员学历数据",
    description="批量添加技术人员学历相关数据")
async def collect_technical_edu(
    edus: List[dashboard_schemas.TechnicalEduCreate],
    db: Session = Depends(get_db)
):
    """批量采集技术人员学历数据"""
    db_edus = []
    try:
        for edu in edus:
            db_edu = dashboard_models.TechnicalEdu(**edu.dict())
            db.add(db_edu)
            db_edus.append(db_edu)
        db.commit()
        for edu in db_edus:
            db.refresh(edu)
        return Response(code=200, message="数据采集成功", data=db_edus)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据采集失败: {str(e)}")

@router.post("/company/batch/", 
    response_model=Response[List[dashboard_schemas.CompanyInfo]],
    summary="批量采集企业信息数据",
    description="批量添加企业信息相关数据")
async def collect_company_info(
    companies: List[dashboard_schemas.CompanyInfoCreate],
    db: Session = Depends(get_db)
):
    """批量采集企业信息数据"""
    db_companies = []
    try:
        for company in companies:
            db_company = dashboard_models.CompanyInfo(**company.dict())
            db.add(db_company)
            db_companies.append(db_company)
        db.commit()
        for company in db_companies:
            db.refresh(company)
        return Response(code=200, message="数据采集成功", data=db_companies)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据采集失败: {str(e)}")

@router.post("/park-info/batch/", 
    response_model=Response[List[dashboard_schemas.ParkInfo]],
    summary="批量采集园区信息数据",
    description="批量添加园区信息相关数据")
async def collect_park_info(
    parks: List[dashboard_schemas.ParkInfoCreate],
    db: Session = Depends(get_db)
):
    """批量采集园区信息数据"""
    db_parks = []
    try:
        for park in parks:
            db_park = dashboard_models.ParkInfo(**park.dict())
            db.add(db_park)
            db_parks.append(db_park)
        db.commit()
        for park in db_parks:
            db.refresh(park)
        return Response(code=200, message="数据采集成功", data=db_parks)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据采集失败: {str(e)}") 
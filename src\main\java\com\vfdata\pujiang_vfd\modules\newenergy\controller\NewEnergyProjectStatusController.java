package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyProjectStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/project-status")
@RequiredArgsConstructor
public class NewEnergyProjectStatusController {

    private final NewEnergyProjectStatusService projectStatusService;

    @GetMapping
    @Operation(summary = "获取项目开机分布数据", description = "根据车间名称获取该车间的所有项目开机分布数据")
    public ResponseUtils.Result<List<NewEnergyProjectStatusDTO>> getProjectStatus(
            @Parameter(description = "车间名称", required = true)
            @RequestParam("workshop_name") String workshopName) {
        try {
            if (workshopName == null || workshopName.trim().isEmpty()) {
                return ResponseUtils.error("车间名称不能为空");
            }
            
            List<NewEnergyProjectStatusDTO> data = projectStatusService.getProjectStatusByWorkshop(workshopName.trim());
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取项目开机分布数据失败：" + e.getMessage());
        }
    }
}

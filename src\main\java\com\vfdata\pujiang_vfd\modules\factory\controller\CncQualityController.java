package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.CncQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.CncQualityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/quality")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class CncQualityController {

    private final CncQualityService cncQualityService;

    @GetMapping("/cnc")
    @Operation(summary = "获取CNC巡检检验合格率", description = "获取最新的7条CNC巡检检验合格率数据")
    public ResponseUtils.Result<List<CncQualityDTO>> getCncQualityInfo() {
        List<CncQualityDTO> qualityInfo = cncQualityService.getCncQualityInfo();
        return ResponseUtils.success(qualityInfo);
    }
} 
package com.vfdata.pujiang_vfd.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "data-sync")
public class DataSyncConfig {

    /**
     * 外部数据源基础URL
     */
    private String baseUrl = "http://*************:8081";

    /**
     * HTTP连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * HTTP读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 批量处理大小
     */
    private int batchSize = 1000;

    /**
     * 是否跳过已存在的记录
     */
    private boolean skipExisting = true;

    /**
     * 同步间隔（分钟）
     */
    private int syncIntervalMinutes = 10;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 重试间隔（毫秒）
     */
    private int retryInterval = 5000;
}

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime
from typing import List, Optional
from database import get_db
from schemas.base import Response, ErrorResponse
from models import injection_models
from schemas import injection_schemas

router = APIRouter(
    prefix="/injection",
    tags=["注塑车间"]
)

def create_inspection_defect_analysis(defect: injection_schemas.InspectionDefectTypeAnalysisCreate, db: Session = Depends(get_db)):
    """
    创建抽检不良类型分析记录
    
    参数说明：
    - **workshop_name**: 所属车间
    - **defect_type_name**: 不良类型名称
    - **type_count**: 类型数量
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    """
    try:
        db_defect = injection_models.InspectionDefectTypeAnalysis(**defect.dict())
        db_defect.updated_at = datetime.now()
        db.add(db_defect)
        db.commit()
        db.refresh(db_defect)
        return Response(data=db_defect)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/inspection-defect/latest", 
    response_model=Response[List[injection_schemas.InspectionDefectTypeAnalysis]],
    summary="获取最新巡检不良类型分析")
def read_latest_inspection_defect(workshop_name: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, workshop_name, defect_type_name, type_count,
                record_date, updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_zscj_inspection_defect_type_analysis 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_inspection_defect_type_analysis
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        defect_list = [{**row._asdict()} for row in result]
        
        if not defect_list:
            return ErrorResponse(code=404, message="没有找到巡检不良类型分析记录", success=False)
            
        return Response(code=200, message="获取巡检不良类型分析记录成功", data=defect_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取巡检不良类型分析记录失败: {str(e)}", success=False)

@router.get("/personnel/latest", 
    response_model=Response[List[injection_schemas.PersonnelCount]],
    summary="获取最新人员信息",
    description="获取注塑车间最新日期的人员信息记录")
def read_latest_personnel_info(db: Session = Depends(get_db)):
    """
    获取注塑车间最新人员信息
    
    返回字段说明：
    - count: 人数
    - personnel_type: 人员类型（包括：普工、职员、总人数）
    """
    try:
        sql = """
            select general_worker_count as count, personnel_type 
            from ioc_zscj_personnel_info 
            where record_date = (
                select max(record_date) 
                from ioc_zscj_personnel_info
            )
            union ALL
            select staff_count as count, '总人数' as personnel_type 
            from ioc_zscj_personnel_info 
            where record_date = (
                select max(record_date) 
                from ioc_zscj_personnel_info
            ) 
            and personnel_type = '职员'
        """
        
        result = db.execute(text(sql))
        
        personnel_list = [
            {
                "count": row.count,
                "personnel_type": row.personnel_type
            }
            for row in result
        ]
        
        if not personnel_list:
            return ErrorResponse(
                code=404,
                message="没有找到人员信息记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取人员信息记录成功",
            data=personnel_list
        )
        
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取人员信息记录失败: {str(e)}",
            success=False
        )

@router.get("/attendance/latest/v1", 
    response_model=Response[injection_schemas.AttendanceRate],
    summary="获取最新出勤率",
    description="获取注塑车间最新的出勤率记录")
def read_latest_attendance_rate(db: Session = Depends(get_db)):
    """
    获取最新出勤率记录
    
    返回字段说明：
    - clerk_attendance_rate: 职员出勤率
    - worker_attendance_rate: 工人出勤率
    - record_date: 记录日期
    - updated_by: 更新人
    - updated_at: 更新时间
    """
    try:
        sql = """
            SELECT 
                id,
                clerk_attendance_rate,
                worker_attendance_rate,
                record_date,
                updated_by,
                updated_at
            FROM ioc_zscj_attendance_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_attendance_rate
            )
        """
        
        result = db.execute(text(sql)).first()
        if not result:
            return ErrorResponse(
                code=404,
                message="没有找到出勤率记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取出勤率记录成功",
            data={
                "id": result.id,
                "clerk_attendance_rate": float(result.clerk_attendance_rate),
                "worker_attendance_rate": float(result.worker_attendance_rate),
                "record_date": result.record_date,
                "updated_by": result.updated_by,
                "updated_at": result.updated_at
            }
        )
        
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取出勤率记录失败: {str(e)}",
            success=False
        )

@router.get("/environment/latest", 
    response_model=Response[injection_schemas.EnvironmentInfo],
    summary="获取最新环境信息",
    description="获取注塑车间最新的环境信息记录")
def read_latest_environment_info(db: Session = Depends(get_db)):
    """
    获取最新环境信息记录
    
    返回字段说明：
    - dust_free_workshop_level: 无尘车间等级
    - average_humidity: 平均湿度
    - average_temperature: 平均温度
    - workshop_name: 车间名称
    - record_date: 记录日期
    - updated_by: 更新人
    - updated_at: 更新时间
    """
    try:
        sql = """
            SELECT 
                id,
                dust_free_workshop_level,
                average_humidity,
                average_temperature,
                workshop_name,
                record_date,
                updated_by,
                updated_at
            FROM ioc_zscj_environment_info 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_environment_info
            )
        """
        
        result = db.execute(text(sql)).first()
        if not result:
            return ErrorResponse(
                code=404,
                message="没有找到环境信息记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取环境信息记录成功",
            data={
                "id": result.id,
                "dust_free_workshop_level": result.dust_free_workshop_level,
                "average_humidity": float(result.average_humidity),
                "average_temperature": float(result.average_temperature),
                "workshop_name": result.workshop_name,
                "record_date": result.record_date,
                "updated_by": result.updated_by,
                "updated_at": result.updated_at
            }
        )
        
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取环境信息记录失败: {str(e)}",
            success=False
        )

@router.get("/project-status/latest", 
    response_model=Response[List[injection_schemas.ProjectStatus]],
    summary="获取最新项目开机分布",
    description="获取注塑车间最新的项目开机分布记录，可选择指定车间")
def read_latest_project_status(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新项目开机分布记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - project_name: 项目名称
      - product_name: 产品名称
      - machine_id: 机器ID
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
      - reserved_field1: 保留字段1
      - reserved_field2: 保留字段2
      - reserved_field3: 保留字段3
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                project_name,
                product_name,
                machine_id,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_project_status 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_project_status
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        
        project_list = [{**row._asdict()} for row in result]
        
        if not project_list:
            return ErrorResponse(
                code=404,
                message="没有找到项目开机分布记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取项目开机分布记录成功",
            data=project_list
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取项目开机分布记录失败: {str(e)}",
            success=False
        )

@router.get("/inspection-pass-rate/latest", 
    response_model=Response[List[injection_schemas.InspectionPassRate]],
    summary="获取最新巡检检验合格率")
def read_latest_inspection_pass_rate(workshop_name: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, workshop_name, pass_rate, record_date,
                updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_zscj_inspection_pass_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_inspection_pass_rate
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        pass_rate_list = [
            {
                **row._asdict(),
                "pass_rate": float(row.pass_rate)
            }
            for row in result
        ]
        
        if not pass_rate_list:
            return ErrorResponse(code=404, message="没有找到巡检检验合格率记录", success=False)
            
        return Response(code=200, message="获取巡检检验合格率记录成功", data=pass_rate_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取巡检检验合格率记录失败: {str(e)}", success=False)

@router.get("/quality-distribution/latest", 
    response_model=Response[List[injection_schemas.QualityDistribution]],
    summary="获取最新整体品质分布")
def read_latest_quality_distribution(workshop_name: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, workshop_name, quality_name, quality_rate,
                record_date, updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_quality_distribution_chart 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_quality_distribution_chart
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        quality_list = [
            {
                **row._asdict(),
                "quality_rate": float(row.quality_rate)
            }
            for row in result
        ]
        
        if not quality_list:
            return ErrorResponse(code=404, message="没有找到品质分布记录", success=False)
            
        return Response(code=200, message="获取品质分布记录成功", data=quality_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取品质分布记录失败: {str(e)}", success=False)

@router.get("/achievement-rate/today", 
    response_model=Response[List[injection_schemas.DailyAchievementRate]],
    summary="获取当天计划达成率",
    description="获取注塑车间当天的计划达成率记录")
def read_today_achievement_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取当天计划达成率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - planned_quantity: 计划数量
      - actual_quantity: 实际数量
      - achievement_rate: 达成率
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
      - reserved_field1: 保留字段1
      - reserved_field2: 保留字段2
      - reserved_field3: 保留字段3
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                planned_quantity,
                actual_quantity,
                achievement_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_daily_achievement_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_daily_achievement_rate
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        
        achievement_list = [
            {
                **row._asdict(),
                "achievement_rate": float(row.achievement_rate)
            }
            for row in result
        ]
        
        if not achievement_list:
            return ErrorResponse(
                code=404,
                message="没有找到当天计划达成率记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取当天计划达成率记录成功",
            data=achievement_list
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取当天计划达成率记录失败: {str(e)}",
            success=False
        )

@router.get("/achievement-rate/week", 
    response_model=Response[List[injection_schemas.PlanCompletionRate7Days]],
    summary="获取近7天计划达成率",
    description="获取注塑车间近7天的计划达成率记录")
def read_week_achievement_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取近7天计划达成率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - completion_rate: 达成率
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
      - update_date: 更新日期
      - reserved_field1: 保留字段1
      - reserved_field2: 保留字段2
      - reserved_field3: 保留字段3
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                completion_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_plan_completion_rate_7days 
            WHERE record_date >= CURRENT_DATE - INTERVAL '7 days'
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        sql += " ORDER BY record_date DESC"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        
        achievement_list = [
            {
                **row._asdict(),
                "completion_rate": float(row.completion_rate)
            }
            for row in result
        ]
        
        if not achievement_list:
            return ErrorResponse(
                code=404,
                message="没有找到近7天计划达成率记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取近7天计划达成率记录成功",
            data=achievement_list
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取近7天计划达成率记录失败: {str(e)}",
            success=False
        )

@router.get("/equipment-status/workshop", 
    response_model=Response[List[injection_schemas.WorkshopStatus]],
    summary="获取最新车间设备状态")
def read_latest_workshop_equipment_status(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                total_equipment,
                operating_equipment,
                operating_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_workshop_status 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_workshop_status
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        status_list = [
            {
                **row._asdict(),
                "operating_rate": float(row.operating_rate)
            }
            for row in result
        ]
        
        if not status_list:
            return ErrorResponse(code=404, message="没有找到车间设备状态记录", success=False)
            
        return Response(code=200, message="获取车间设备状态记录成功", data=status_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取车间设备状态记录失败: {str(e)}", success=False)

@router.get("/equipment-status/distribution", 
    response_model=Response[List[injection_schemas.EquipmentStatusDistribution]],
    summary="获取最新设备状态分布",
    description="获取注塑车间最新的设备状态分布记录，可选择指定车间")
def read_latest_equipment_status_distribution(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新设备状态分布记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - status_name: 状态名称
      - status_count: 状态数量
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                status_name,
                status_count,
                record_date,
                updated_by,
                updated_at
            FROM ioc_zscj_equipment_status_distribution 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_equipment_status_distribution
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        
        distribution_list = [{**row._asdict()} for row in result]
        
        if not distribution_list:
            return ErrorResponse(
                code=404,
                message="没有找到设备状态分布记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取设备状态分布记录成功",
            data=distribution_list
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取设备状态分布记录失败: {str(e)}",
            success=False
        )

@router.get("/sampling-inspection/pass-rate", 
    response_model=Response[List[injection_schemas.SamplingInspectionPassRate]],
    summary="获取最新抽检检验合格率",
    description="获取注塑车间最新的抽检检验合格率记录，可选择指定车间")
def read_latest_sampling_inspection_pass_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新抽检检验合格率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - pass_rate: 合格率
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
      - reserved_field1: 保留字段1
      - reserved_field2: 保留字段2
      - reserved_field3: 保留字段3
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                pass_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_sampling_inspection_pass_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_sampling_inspection_pass_rate
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        
        pass_rate_list = [
            {
                **row._asdict(),
                "pass_rate": float(row.pass_rate)
            }
            for row in result
        ]
        
        if not pass_rate_list:
            return ErrorResponse(
                code=404,
                message="没有找到抽检检验合格率记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取抽检检验合格率记录成功",
            data=pass_rate_list
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取抽检检验合格率记录失败: {str(e)}",
            success=False
        )

@router.get("/stop-type/latest", 
    response_model=Response[List[injection_schemas.StopTypeDistribution]],
    summary="获取最新停机类型分布",
    description="获取注塑车间最新的停机类型分布记录，可选择指定车间")
def read_latest_stop_type_distribution(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新停机类型分布记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - stop_type_name: 停机类型名称
      - type_count: 停机次数
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
      - reserved_field1: 保留字段1
      - reserved_field2: 保留字段2
      - reserved_field3: 保留字段3
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                stop_type_name,
                type_count,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_stop_type_distribution 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_stop_type_distribution
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        
        stop_type_list = [
            {
                **row._asdict(),
                "type_count": row.type_count
            }
            for row in result
        ]
        
        if not stop_type_list:
            return ErrorResponse(
                code=404,
                message="没有找到停机类型分布记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取停机类型分布记录成功",
            data=stop_type_list
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取停机类型分布记录失败: {str(e)}",
            success=False
        )

@router.get("/environment-info/latest", 
    response_model=Response[injection_schemas.EnvironmentInfo],
    summary="获取最新环境信息")
def read_latest_environment_info(db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, dust_free_workshop_level, average_humidity, average_temperature,
                record_date, updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_zscj_environment_info 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_environment_info
            )
        """
        result = db.execute(text(sql)).first()
        if not result:
            return ErrorResponse(code=404, message="没有找到环境信息记录", success=False)
            
        return Response(
            code=200,
            message="获取环境信息记录成功",
            data={
                **result._asdict(),
                "average_humidity": float(result.average_humidity),
                "average_temperature": float(result.average_temperature)
            }
        )
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取环境信息记录失败: {str(e)}", success=False)

@router.get("/workshop-status/latest", 
    response_model=Response[List[injection_schemas.WorkshopStatus]],
    summary="获取最新车间设备状态")
def read_latest_workshop_status(workshop_name: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, total_equipment, operating_equipment, operating_rate,
                workshop_name, record_date, updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_zscj_workshop_status 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_workshop_status
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        status_list = [
            {
                **row._asdict(),
                "operating_rate": float(row.operating_rate)
            }
            for row in result
        ]
        
        if not status_list:
            return ErrorResponse(code=404, message="没有找到车间设备状态记录", success=False)
            
        return Response(code=200, message="获取车间设备状态记录成功", data=status_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取车间设备状态记录失败: {str(e)}", success=False)

@router.get("/sampling-pass-rate/latest", 
    response_model=Response[List[injection_schemas.SamplingInspectionPassRate]],
    summary="获取最新抽检检验合格率")
def read_latest_sampling_pass_rate(workshop_name: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, workshop_name, pass_rate, record_date,
                updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_zscj_sampling_inspection_pass_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_sampling_inspection_pass_rate
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        pass_rate_list = [
            {
                **row._asdict(),
                "pass_rate": float(row.pass_rate)
            }
            for row in result
        ]
        
        if not pass_rate_list:
            return ErrorResponse(code=404, message="没有找到抽检检验合格率记录", success=False)
            
        return Response(code=200, message="获取抽检检验合格率记录成功", data=pass_rate_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取抽检检验合格率记录失败: {str(e)}", success=False)

@router.get("/defect-analysis/latest", 
    response_model=Response[List[injection_schemas.InspectionDefectTypeAnalysis]],
    summary="获取最新不良类型分析")
def read_latest_defect_analysis(workshop_name: Optional[str] = None, db: Session = Depends(get_db)):
    try:
        sql = """
            SELECT 
                id, workshop_name, defect_type_name, type_count,
                record_date, updated_by, updated_at,
                reserved_field1, reserved_field2, reserved_field3
            FROM ioc_zscj_inspection_defect_type_analysis 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_inspection_defect_type_analysis
            )
        """
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        defect_list = [{**row._asdict()} for row in result]
        
        if not defect_list:
            return ErrorResponse(code=404, message="没有找到不良类型分析记录", success=False)
            
        return Response(code=200, message="获取不良类型分析记录成功", data=defect_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取不良类型分析记录失败: {str(e)}", success=False)

@router.get("/equipment-info/latest", 
    response_model=Response[injection_schemas.EquipmentInfo],
    summary="获取最新设备信息",
    description="获取注塑车间最新的设备信息记录")
def read_latest_equipment_info(db: Session = Depends(get_db)):
    """
    获取最新设备信息记录
    
    返回字段说明：
      - total_equipment_count: 设备总数
      - operating_equipment_count: 总开机数
      - equipment_overall_efficiency: 设备综合效率
      - operating_rate: 开机率
      - record_date: 记录日期
      - updated_by: 更新人
      - updated_at: 更新时间
      - reserved_field1: 备用字段1
      - reserved_field2: 备用字段2
      - reserved_field3: 备用字段3
    """
    try:
        sql = """
            SELECT 
                id,
                total_equipment_count,
                operating_equipment_count,
                equipment_overall_efficiency,
                operating_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zscj_equipment_info 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zscj_equipment_info
            )
        """
        
        result = db.execute(text(sql)).first()
        
        if not result:
            return ErrorResponse(
                code=404,
                message="没有找到设备信息记录",
                success=False
            )
            
        return Response(
            code=200,
            message="获取设备信息记录成功",
            data={
                **result._asdict(),
                "equipment_overall_efficiency": float(result.equipment_overall_efficiency),
                "operating_rate": float(result.operating_rate)
            }
        )
    except Exception as e:
        return ErrorResponse(
            code=500,
            message=f"获取设备信息记录失败: {str(e)}",
            success=False
        )

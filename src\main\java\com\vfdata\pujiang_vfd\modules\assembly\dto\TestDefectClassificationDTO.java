package com.vfdata.pujiang_vfd.modules.assembly.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "测试缺陷分类信息")
public class TestDefectClassificationDTO {

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "分类名称")
    private String classification_name;

    @Schema(description = "缺陷数量")
    private Integer defect_count;

    @Schema(description = "记录日期")
    private String record_date;
} 
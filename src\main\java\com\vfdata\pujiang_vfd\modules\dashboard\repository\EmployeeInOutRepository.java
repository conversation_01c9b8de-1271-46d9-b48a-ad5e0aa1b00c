package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.EmployeeInOut;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface EmployeeInOutRepository extends JpaRepository<EmployeeInOut, Long> {
    
    List<EmployeeInOut> findTop20ByOrderByRecordDateDesc();
}

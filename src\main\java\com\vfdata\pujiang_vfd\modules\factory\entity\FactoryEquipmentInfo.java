package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "ioc_qc_equipment_info")
@EqualsAndHashCode(callSuper = true)
public class FactoryEquipmentInfo extends BaseEntity {
    
    @Column(name = "total_equipment_count")
    private Integer totalEquipmentCount;
    
    @Column(name = "operating_equipment_count")
    private Integer operatingEquipmentCount;
    
    @Column(name = "operating_rate", precision = 5, scale = 2)
    private BigDecimal operatingRate;
} 
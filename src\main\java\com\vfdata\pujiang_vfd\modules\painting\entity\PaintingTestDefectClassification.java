package com.vfdata.pujiang_vfd.modules.painting.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_ptcj_test_defect_classification")
@Schema(description = "喷涂车间品质不良分类")
public class PaintingTestDefectClassification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "defect_type_name")
    @Schema(description = "不良类型")
    private String defectTypeName;

    @Column(name = "defect_type_count")
    @Schema(description = "该类型不良发生次数")
    private Integer defectTypeCount;

    @Column(name = "workshop_name")
    @Schema(description = "发现不良的车间")
    private String workshopName;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

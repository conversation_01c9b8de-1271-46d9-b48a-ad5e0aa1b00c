package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingRtoStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingRtoStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/rto-status")
@RequiredArgsConstructor
public class PaintingRtoStatusController {

    private final PaintingRtoStatusService rtoStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取喷涂车间RTO运行状态", description = "获取喷涂车间最新的RTO设备运行状态信息")
    public ResponseUtils.Result<PaintingRtoStatusDTO> getLatestRtoStatus() {
        try {
            PaintingRtoStatusDTO data = rtoStatusService.getLatestRtoStatus();
            if (data == null) {
                return ResponseUtils.success(new PaintingRtoStatusDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间RTO运行状态失败：" + e.getMessage());
        }
    }
}

from pydantic import BaseModel
from datetime import date, datetime
from typing import Optional
from decimal import Decimal

class SafetyBase(BaseModel):
    """安全管理基础模型"""
    record_date: Optional[datetime] = None
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    reserved_1: Optional[str] = None
    reserved_2: Optional[str] = None
    reserved_3: Optional[str] = None

    class Config:
        from_attributes = True

class VisitorRecordBase(SafetyBase):
    """
    访客记录基础模型
    """
    visitor_name: Optional[str] = None  # 访客姓名
    channel_name: Optional[str] = None  # 通道名称
    visit_time: Optional[datetime] = None  # 访问时间
    status: Optional[str] = None  # 状态（进/出）
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class VisitorRecordCreate(VisitorRecordBase):
    """创建访客记录的请求模型"""
    pass

class VisitorRecord(VisitorRecordBase):
    """访客记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class VehicleRecordBase(SafetyBase):
    """
    车辆记录基础模型
    """
    license_plate: Optional[str] = None  # 车牌号
    channel_name: Optional[str] = None  # 通道名称
    entry_time: Optional[datetime] = None  # 进入时间
    status: Optional[str] = None  # 状态（进/出）
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class VehicleRecordCreate(VehicleRecordBase):
    """创建车辆记录的请求模型"""
    pass

class VehicleRecord(VehicleRecordBase):
    """车辆记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class WastewaterMonitoringBase(SafetyBase):
    """废水监控基础模型"""
    cod: Optional[Decimal] = None  # COD值
    ammonia_nitrogen: Optional[Decimal] = None  # 氨氮值
    record_time: Optional[datetime] = None  # 记录时间
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class WastewaterMonitoringCreate(WastewaterMonitoringBase):
    """创建废水监控记录的请求模型"""
    pass

class WastewaterMonitoring(WastewaterMonitoringBase):
    """废水监控记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class FireEquipmentInfoBase(SafetyBase):
    """消防设备信息基础模型"""
    fire_extinguisher: Optional[str] = None  # 灭火器状态
    fire_sprinkler: Optional[str] = None  # 喷淋系统状态
    fire_hydrant: Optional[str] = None  # 消防栓状态
    fire_alarm_device: Optional[str] = None  # 火警报警器状态
    record_date: Optional[datetime] = None  # 记录时间
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class FireEquipmentInfoCreate(FireEquipmentInfoBase):
    """创建消防设备信息的请求模型"""
    pass

class FireEquipmentInfo(FireEquipmentInfoBase):
    """消防设备信息的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class EnergyConsumptionRecordBase(SafetyBase):
    """能耗统计记录基础模型"""
    water_usage: Optional[Decimal] = None  # 水用量
    electricity_usage: Optional[Decimal] = None  # 电用量
    gas_usage: Optional[Decimal] = None  # 气用量
    record_time: Optional[datetime] = None  # 记录时间
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class EnergyConsumptionRecordCreate(EnergyConsumptionRecordBase):
    """创建能耗统计记录的请求模型"""
    pass

class EnergyConsumptionRecord(EnergyConsumptionRecordBase):
    """能耗统计记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class ActualEnergyConsumptionBase(SafetyBase):
    """实际能耗统计基础模型"""
    water_usage: Optional[Decimal] = None  # 水使用量
    electricity_usage: Optional[Decimal] = None  # 电使用量
    gas_usage: Optional[Decimal] = None  # 气使用量
    record_time: Optional[datetime] = None  # 记录时间
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class ActualEnergyConsumptionCreate(ActualEnergyConsumptionBase):
    """创建实际能耗统计记录的请求模型"""
    pass

class ActualEnergyConsumption(ActualEnergyConsumptionBase):
    """实际能耗统计记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class PhotovoltaicEnergyMetricBase(SafetyBase):
    """光伏能源指标基础模型"""
    daily_generation: Optional[Decimal] = None  # 今日发电量
    total_generation: Optional[Decimal] = None  # 历史总发电量
    daily_co2_reduction: Optional[Decimal] = None  # 日减少CO2排放量
    record_time: Optional[datetime] = None  # 记录时间
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class PhotovoltaicEnergyMetricCreate(PhotovoltaicEnergyMetricBase):
    """创建光伏能源指标记录的请求模型"""
    pass

class PhotovoltaicEnergyMetric(PhotovoltaicEnergyMetricBase):
    """光伏能源指标记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True

class PhotovoltaicEnergyStatBase(SafetyBase):
    """光伏能源统计基础模型"""
    daily_generation: Optional[Decimal] = None  # 日发电量
    record_time: Optional[datetime] = None  # 记录时间
    update_date: Optional[date] = None  # 更新日期
    reserved_field1: Optional[str] = None  # 预留字段1
    reserved_field2: Optional[str] = None  # 预留字段2
    reserved_field3: Optional[str] = None  # 预留字段3

class PhotovoltaicEnergyStatCreate(PhotovoltaicEnergyStatBase):
    """创建光伏能源统计记录的请求模型"""
    pass

class PhotovoltaicEnergyStat(PhotovoltaicEnergyStatBase):
    """光伏能源统计记录的响应模型"""
    id: Optional[int] = None  # 记录ID

    class Config:
        from_attributes = True 
package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionSamplingPassRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionSamplingPassRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/sampling-pass-rate")
@RequiredArgsConstructor
public class InjectionSamplingPassRateController {

    private final InjectionSamplingPassRateService samplingPassRateService;

    @Operation(summary = "获取最新抽检合格率", description = "获取所有车间的最新抽检合格率数据")
    @GetMapping("/latest")
    public ResponseUtils.Result<List<InjectionSamplingPassRateDTO>> getLatestPassRates() {
        try {
            List<InjectionSamplingPassRateDTO> rates = samplingPassRateService.getLatestPassRates();
            return ResponseUtils.success(rates);
        } catch (Exception e) {
            return ResponseUtils.error("获取抽检合格率数据失败：" + e.getMessage());
        }
    }
} 
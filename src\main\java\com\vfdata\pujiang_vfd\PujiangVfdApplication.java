package com.vfdata.pujiang_vfd;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "com.vfdata.pujiang_vfd")
@EntityScan("com.vfdata.pujiang_vfd.modules.*.entity")
@EnableJpaRepositories("com.vfdata.pujiang_vfd.modules.*.repository")
@EnableScheduling
@EnableTransactionManagement
public class PujiangVfdApplication {

    public static void main(String[] args) {
        SpringApplication.run(PujiangVfdApplication.class, args);
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("浦江VFD数据采集和发布系统")
                        .version("1.0.0")
                        .description("用于采集和发布工业设备数据的系统")
                        .contact(new Contact()
                                .name("VFData")
                                .email("<EMAIL>")
                                .url("https://www.vfdata.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")));
    }
} 
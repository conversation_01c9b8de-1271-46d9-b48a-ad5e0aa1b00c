package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.WorkOrderTrendDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.WorkOrderTrend;
import com.vfdata.pujiang_vfd.modules.factory.repository.WorkOrderTrendRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WorkOrderTrendService {

    private final WorkOrderTrendRepository workOrderTrendRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    /**
     * 获取工单趋势信息
     * 获取最近6个日期的工单关闭率数据，按车间名称和日期排序
     */
    public List<WorkOrderTrendDTO> getWorkOrderTrendInfo() {
        List<WorkOrderTrend> trendList = workOrderTrendRepository.findLatest6DateRecords();
        return trendList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private WorkOrderTrendDTO convertToDTO(WorkOrderTrend entity) {
        WorkOrderTrendDTO dto = new WorkOrderTrendDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setCompleted_count(entity.getCompletedCount());
        dto.setNot_closed_count(entity.getNotClosedCount());
        dto.setClose_rate(entity.getCloseRate());
        dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt().format(DATETIME_FORMATTER));
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
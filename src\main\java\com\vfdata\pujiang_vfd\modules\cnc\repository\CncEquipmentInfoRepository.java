package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CncEquipmentInfoRepository extends JpaRepository<CncEquipmentInfo, Long> {

    /**
     * 获取最新的设备信息记录
     */
    @Query("SELECT c FROM CncEquipmentInfo c WHERE c.recordDate = (SELECT MAX(c2.recordDate) FROM CncEquipmentInfo c2)")
    CncEquipmentInfo findLatestRecord();
}

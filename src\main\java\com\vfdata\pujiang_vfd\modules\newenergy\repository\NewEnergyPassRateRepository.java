package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPassRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyPassRateRepository extends JpaRepository<NewEnergyPassRate, Long> {
    
    /**
     * 获取每个车间最新记录日期的性能测试良率数据
     * 按车间分组，获取每个车间record_date最新的数据
     */
    @Query("SELECT n FROM NewEnergyPassRate n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyPassRate n2 WHERE n2.workshopName = n.workshopName)")
    List<NewEnergyPassRate> findLatestPassRate();
}

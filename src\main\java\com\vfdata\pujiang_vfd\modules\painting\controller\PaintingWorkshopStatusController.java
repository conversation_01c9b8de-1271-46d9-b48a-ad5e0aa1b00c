package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingWorkshopStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/workshop-status")
@RequiredArgsConstructor
public class PaintingWorkshopStatusController {

    private final PaintingWorkshopStatusService workshopStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取喷涂车间设备状况", description = "获取每个喷涂车间最新的设备状况信息")
    public ResponseUtils.Result<List<PaintingWorkshopStatusDTO>> getLatestWorkshopStatus() {
        try {
            List<PaintingWorkshopStatusDTO> data = workshopStatusService.getLatestWorkshopStatus();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间设备状况信息失败：" + e.getMessage());
        }
    }
}

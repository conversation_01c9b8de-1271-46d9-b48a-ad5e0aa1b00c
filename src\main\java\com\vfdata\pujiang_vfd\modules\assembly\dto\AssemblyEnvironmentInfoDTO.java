package com.vfdata.pujiang_vfd.modules.assembly.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "组装车间环境信息DTO")
public class AssemblyEnvironmentInfoDTO {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "无尘车间等级")
    private String dust_free_workshop_level;
    
    @Schema(description = "平均湿度")
    private BigDecimal average_humidity;
    
    @Schema(description = "平均温度")
    private BigDecimal average_temperature;
    
    @Schema(description = "记录日期")
    private LocalDateTime record_date;
    
    @Schema(description = "更新人")
    private String updated_by;
    
    @Schema(description = "更新时间")
    private LocalDateTime updated_at;
    
    @Schema(description = "预留字段1")
    private String reserved_field1;
    
    @Schema(description = "预留字段2")
    private String reserved_field2;
    
    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
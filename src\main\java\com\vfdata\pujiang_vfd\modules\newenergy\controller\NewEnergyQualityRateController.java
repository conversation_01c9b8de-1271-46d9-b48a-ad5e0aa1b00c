package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyQualityRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/quality-rate")
@RequiredArgsConstructor
public class NewEnergyQualityRateController {

    private final NewEnergyQualityRateService qualityRateService;

    @GetMapping
    @Operation(summary = "获取抽检检验合格率", description = "获取每个车间最新记录日期的抽检检验合格率数据")
    public ResponseUtils.Result<List<NewEnergyQualityRateDTO>> getLatestQualityRate() {
        try {
            List<NewEnergyQualityRateDTO> data = qualityRateService.getLatestQualityRate();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取抽检检验合格率失败：" + e.getMessage());
        }
    }
}

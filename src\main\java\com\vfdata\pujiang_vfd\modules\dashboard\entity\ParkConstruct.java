package com.vfdata.pujiang_vfd.modules.dashboard.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_dp_parkconstruct")
@Schema(description = "园区建设信息")
public class ParkConstruct {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "serial_number")
    @Schema(description = "序号")
    @JsonProperty("serial_number")
    private Integer serialNumber;

    @Column(name = "project_name", length = 255)
    @Schema(description = "项目名称")
    @JsonProperty("project_name")
    private String projectName;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    @JsonProperty("record_date")
    private LocalDate recordDate;

    @Column(name = "updateman", length = 20)
    @Schema(description = "更新人")
    private String updateman;

    @Column(name = "updatetime")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    @JsonProperty("reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    @JsonProperty("reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    @JsonProperty("reserved_field3")
    private String reservedField3;
}

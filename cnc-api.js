import request from './request.js'

// CNC车间人员信息
export const personnel_info = async () => {
    return await request.get(`/cnc/personnel-info/latest`);
};

// CNC车间设备信息
export const equipment_info = async () => {
    return await request.get(`/cnc/equipment-info/latest`);
};

// CNC车间出勤率
export const attendance_rate = async () => {
    return await request.get(`/cnc/attendance-rate/latest`);
};

// CNC车间当前计划达成率
export const daily_achievement_rate = async () => {
    return await request.get(`/cnc/daily-achievement-rate`);
};

// CNC车间巡检检验合格率
export const quality_rate = async () => {
    return await request.get(`/cnc/quality-rate/latest`);
};

// CNC车间环境信息
export const environment_info = async () => {
    return await request.get(`/cnc/environment-info/latest`);
};

// CNC车间刀具消耗指数
export const cuttingtool_usage = async () => {
    return await request.get(`/cnc/cuttingtool-usage/latest`);
};

// CNC车间计划达成率近7天 (需要车间参数)
export const plan_completion_rate_7days = async (workshop_name) => {
    return await request.get(`/cnc/plan-completion-rate-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// CNC车间巡检检验合格率近7天 (需要车间参数)
export const quality_rate_7days = async (workshop_name) => {
    return await request.get(`/cnc/quality-rate-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// CNC车间设备状态分布 (需要车间参数)
export const equipment_status = async (workshop_name) => {
    return await request.get(`/cnc/equipment-status?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// CNC车间刀具近7天下机原因 (需要车间参数)
export const cuttingtool_offline_7days = async (workshop_name) => {
    return await request.get(`/cnc/cuttingtool-offline-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// CNC车间刀具用量近7天 (需要车间参数)
export const cuttingtool_usage_7days = async (workshop_name) => {
    return await request.get(`/cnc/cuttingtool-usage-7days?workshop_name=${encodeURIComponent(workshop_name)}`);
};

// CNC车间项目开机分布 (需要车间参数)
export const project_status = async (workshop_name) => {
    return await request.get(`/cnc/project-status?workshop_name=${encodeURIComponent(workshop_name)}`);
};

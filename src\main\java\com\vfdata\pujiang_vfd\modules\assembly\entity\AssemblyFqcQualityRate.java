package com.vfdata.pujiang_vfd.modules.assembly.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "assembly_fqc_quality_rate")
public class AssemblyFqcQualityRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "workshop_name")
    private String workshopName;

    @Column(name = "quality_rate")
    private Double qualityRate;

    @Column(name = "record_date")
    private LocalDate recordDate;
} 
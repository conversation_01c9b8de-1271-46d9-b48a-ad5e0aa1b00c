package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncEquipmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncEquipmentStatusRepository extends JpaRepository<CncEquipmentStatus, Long> {

    /**
     * 获取指定车间的最新设备状态分布数据
     */
    @Query("SELECT c FROM CncEquipmentStatus c WHERE c.workshopName = :workshopName AND c.updatedAt = (SELECT MAX(c2.updatedAt) FROM CncEquipmentStatus c2 WHERE c2.workshopName = :workshopName)")
    List<CncEquipmentStatus> findLatestByWorkshop(@Param("workshopName") String workshopName);
}

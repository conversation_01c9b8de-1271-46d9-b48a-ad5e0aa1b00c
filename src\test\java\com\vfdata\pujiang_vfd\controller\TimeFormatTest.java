package com.vfdata.pujiang_vfd.controller;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;

class TimeFormatTest {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Test
    void testDateTimeFormatting() {
        // 测试LocalDateTime格式化
        LocalDateTime dateTime = LocalDateTime.of(2025, 6, 23, 18, 31, 54);
        String formatted = dateTime.format(DATETIME_FORMATTER);
        
        assertEquals("2025-06-23 18:31:54", formatted);
        assertFalse(formatted.contains("+08"));  // 确保不包含时区信息
        assertFalse(formatted.contains("T"));    // 确保不包含ISO格式的T
    }

    @Test
    void testDateFormatting() {
        // 测试LocalDate格式化
        LocalDate date = LocalDate.of(2025, 6, 23);
        String formatted = date.format(DATE_FORMATTER);
        
        assertEquals("2025-06-23", formatted);
        assertFalse(formatted.contains("+08"));  // 确保不包含时区信息
    }

    @Test
    void testToStringComparison() {
        // 对比toString()方法和格式化器的区别
        LocalDateTime dateTime = LocalDateTime.of(2025, 6, 23, 18, 31, 54);
        
        String toStringResult = dateTime.toString();
        String formattedResult = dateTime.format(DATETIME_FORMATTER);
        
        System.out.println("toString(): " + toStringResult);
        System.out.println("formatted: " + formattedResult);
        
        // toString()可能包含T，格式化器不会
        assertTrue(toStringResult.contains("T"));
        assertFalse(formattedResult.contains("T"));
        
        // 格式化器的结果更符合预期
        assertEquals("2025-06-23 18:31:54", formattedResult);
    }

    @Test
    void testNullHandling() {
        // 测试null值处理
        LocalDateTime nullDateTime = null;
        LocalDate nullDate = null;
        
        String formattedDateTime = nullDateTime != null ? nullDateTime.format(DATETIME_FORMATTER) : null;
        String formattedDate = nullDate != null ? nullDate.format(DATE_FORMATTER) : null;
        
        assertNull(formattedDateTime);
        assertNull(formattedDate);
    }
}

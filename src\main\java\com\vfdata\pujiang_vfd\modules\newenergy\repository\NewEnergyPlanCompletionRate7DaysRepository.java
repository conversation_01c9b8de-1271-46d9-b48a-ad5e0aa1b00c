package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPlanCompletionRate7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyPlanCompletionRate7DaysRepository extends JpaRepository<NewEnergyPlanCompletionRate7Days, Long> {
    
    /**
     * 根据车间名称获取最新7条记录数据
     * 按record_date降序排列，取前7条
     */
    @Query("SELECT n FROM NewEnergyPlanCompletionRate7Days n WHERE n.workshopName = :workshopName ORDER BY n.recordDate DESC LIMIT 7")
    List<NewEnergyPlanCompletionRate7Days> findTop7ByWorkshopNameOrderByRecordDateDesc(@Param("workshopName") String workshopName);
}

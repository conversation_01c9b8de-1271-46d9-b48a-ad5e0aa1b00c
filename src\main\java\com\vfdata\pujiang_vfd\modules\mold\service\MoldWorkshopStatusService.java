package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldWorkshopStatus;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldWorkshopStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldWorkshopStatusService {

    private final MoldWorkshopStatusRepository workshopStatusRepository;

    /**
     * 获取每个车间最新的设备状况信息
     */
    public List<MoldWorkshopStatusDTO> getLatestWorkshopStatus() {
        List<MoldWorkshopStatus> entities = workshopStatusRepository.findLatestRecords();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldWorkshopStatusDTO convertToDTO(MoldWorkshopStatus entity) {
        MoldWorkshopStatusDTO dto = new MoldWorkshopStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setWorkshop_rate(entity.getWorkshopRate());
        dto.setPark_name(entity.getParkName());
        dto.setPark_status_rate(entity.getParkStatusRate());
        dto.setTotal_equipment_count(entity.getTotalEquipmentCount());
        dto.setOperating_equipment_count(entity.getOperatingEquipmentCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

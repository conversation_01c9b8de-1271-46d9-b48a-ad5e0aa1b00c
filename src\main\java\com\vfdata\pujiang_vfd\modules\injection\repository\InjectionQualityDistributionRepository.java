package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionQualityDistribution;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionQualityDistributionRepository extends JpaRepository<InjectionQualityDistribution, Long> {
    
    @Query(value = "SELECT DISTINCT ON (workshop_name, quality_name) * FROM ioc_quality_distribution_chart WHERE workshop_name = :workshopName ORDER BY workshop_name, quality_name, record_date DESC, id DESC", nativeQuery = true)
    List<InjectionQualityDistribution> findLatestByWorkshopName(@Param("workshopName") String workshopName);

    @Query(value = "SELECT DISTINCT ON (workshop_name, quality_name) * FROM ioc_quality_distribution_chart ORDER BY workshop_name, quality_name, record_date DESC, id DESC", nativeQuery = true)
    List<InjectionQualityDistribution> findLatestAll();
} 
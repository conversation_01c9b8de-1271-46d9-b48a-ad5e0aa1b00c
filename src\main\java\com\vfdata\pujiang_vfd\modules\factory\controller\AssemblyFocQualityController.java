package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.AssemblyFocQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.AssemblyFocQualityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/quality")  
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class AssemblyFocQualityController {

    private final AssemblyFocQualityService assemblyFocQualityService;

    @GetMapping("/asm-foc")
    @Operation(summary = "获取组装FOC检验合格率", description = "获取每个车间最新记录日期的组装FOC检验合格率数据")
    public ResponseUtils.Result<List<AssemblyFocQualityDTO>> getAssemblyFocQualityInfo() {
        List<AssemblyFocQualityDTO> qualityInfo = assemblyFocQualityService.getAssemblyFocQualityInfo();
        return ResponseUtils.success(qualityInfo);
    }
} 
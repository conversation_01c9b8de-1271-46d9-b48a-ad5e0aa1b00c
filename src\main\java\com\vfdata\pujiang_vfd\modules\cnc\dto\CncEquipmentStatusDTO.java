package com.vfdata.pujiang_vfd.modules.cnc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Schema(description = "CNC车间设备状态分布DTO")
public class CncEquipmentStatusDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "状态名称")
    private String status_name;

    @Schema(description = "状态数量")
    private Integer status_count;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

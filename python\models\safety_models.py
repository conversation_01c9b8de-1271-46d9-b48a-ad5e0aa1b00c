from sqlalchemy import Column, Integer, String, Date, DECIMAL, TIMESTAMP, Float, DateTime, ForeignKey
from database import Base
from datetime import datetime


class VisitorRecord(Base):
    """访客记录表"""
    __tablename__ = "ioc_szhaq_visitor_records"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    visitor_name = Column(String(100), nullable=False, comment="姓名")
    channel_name = Column(String(100), nullable=False, comment="通道名称")
    visit_time = Column(TIMESTAMP, nullable=False, comment="时间")
    status = Column(String(50), nullable=False, comment="状态")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class VehicleRecord(Base):
    """车辆进出记录表"""
    __tablename__ = "ioc_szhaq_vehicle_records"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    license_plate = Column(String(100), nullable=False, comment="车牌")
    channel_name = Column(String(100), nullable=False, comment="通道名称")
    entry_time = Column(TIMESTAMP, nullable=False, comment="时间")
    status = Column(String(50), nullable=False, comment="状态")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class WastewaterMonitoring(Base):
    """废水监控表"""
    __tablename__ = "ioc_szhaq_wastewater_monitoring"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    cod = Column(DECIMAL(10, 2), nullable=False, comment="COD值")
    ammonia_nitrogen = Column(DECIMAL(10, 2), nullable=False, comment="氨氮值")
    record_time = Column(TIMESTAMP, nullable=False, comment="时间")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class FireEquipmentInfo(Base):
    """消防设备信息表"""
    __tablename__ = "ioc_szhaq_fire_equipment_info"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    fire_extinguisher = Column(String(100), nullable=False, comment="灭火器")
    fire_sprinkler = Column(String(100), nullable=False, comment="消防喷淋")
    fire_hydrant = Column(String(100), nullable=False, comment="消防栓")
    fire_alarm_device = Column(String(100), nullable=False, comment="火警装置")
    record_date = Column(TIMESTAMP, nullable=False, comment="时间")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class EnergyConsumptionRecord(Base):
    """能耗统计表"""
    __tablename__ = "ioc_szhaq_energy_consumption_records"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    water_usage = Column(DECIMAL(10, 2), nullable=False, comment="水用量")
    electricity_usage = Column(DECIMAL(10, 2), nullable=False, comment="电用量")
    gas_usage = Column(DECIMAL(10, 2), nullable=False, comment="气用量")
    record_time = Column(TIMESTAMP, nullable=False, comment="时间")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class ActualEnergyConsumption(Base):
    """实际能耗统计表"""
    __tablename__ = "ioc_szhaq_actual_energy_consumption"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    water_usage = Column(DECIMAL(10, 2), nullable=False, comment="水使用量")
    electricity_usage = Column(DECIMAL(10, 2), nullable=False, comment="电使用量")
    gas_usage = Column(DECIMAL(10, 2), nullable=False, comment="气使用量")
    record_time = Column(TIMESTAMP, nullable=False, comment="时间")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class PhotovoltaicEnergyMetric(Base):
    """光伏能源指标表"""
    __tablename__ = "ioc_szhaq_photovoltaic_energy_metrics"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    daily_generation = Column(DECIMAL(15, 2), nullable=False, comment="今日发电量")
    total_generation = Column(DECIMAL(15, 2), nullable=False, comment="历史总发电量")
    daily_co2_reduction = Column(DECIMAL(15, 2), nullable=False, comment="日减少CO2排放量")
    record_time = Column(TIMESTAMP, nullable=False, comment="时间")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3")

class PhotovoltaicEnergyStat(Base):
    """光伏能源统计表"""
    __tablename__ = "ioc_szhaq_photovoltaic_energy_stats"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    daily_generation = Column(DECIMAL(10, 2), nullable=False, comment="日发电量")
    record_time = Column(TIMESTAMP, nullable=False, comment="时间")
    updated_by = Column(String(20), nullable=False, comment="更新人")
    update_date = Column(Date, nullable=False, comment="更新日期")
    reserved_field1 = Column(String(255), nullable=True, comment="备用字段1")
    reserved_field2 = Column(String(255), nullable=True, comment="备用字段2")
    reserved_field3 = Column(String(255), nullable=True, comment="备用字段3") 

package com.vfdata.pujiang_vfd.modules.factory.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Schema(description = "涂装巡检检验合格率DTO")
@Getter
@Setter
public class CoatingQualityDTO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "合格率")
    private BigDecimal pass_rate;
    
    @Schema(description = "记录日期")
    private String record_date;
    
    @Schema(description = "更新人")
    private String updated_by;
    
    @Schema(description = "更新时间")
    private String updated_at;
} 
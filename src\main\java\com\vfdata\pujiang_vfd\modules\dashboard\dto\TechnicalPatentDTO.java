package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "技术专利信息DTO")
public class TechnicalPatentDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "专利年份")
    private String patent_year;

    @Schema(description = "专利数量")
    private Integer patent_num;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

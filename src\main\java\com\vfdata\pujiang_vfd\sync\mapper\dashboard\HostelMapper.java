package com.vfdata.pujiang_vfd.sync.mapper.dashboard;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.Hostel;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HostelMapper implements DataMapper<Hostel> {

    @Override
    public Hostel mapToEntity(Map<String, Object> sourceData) {
        try {
            Hostel entity = new Hostel();

            // 映射基础字段
            entity.setId(DateTimeParseUtil.getLongValue(sourceData, "id"));
            entity.setHostelNum(DateTimeParseUtil.getIntegerValue(sourceData, "hostelNum"));
            entity.setHostelUse(DateTimeParseUtil.getIntegerValue(sourceData, "hosteluse")); // 注意Python中是hosteluse
            entity.setHostelRemaining(DateTimeParseUtil.getIntegerValue(sourceData, "hostelRemaining"));
            entity.setHostelInfo(DateTimeParseUtil.getStringValue(sourceData, "hostelInfo"));

            // 映射时间字段
            String recordDateStr = DateTimeParseUtil.getStringValue(sourceData, "recordDate");
            entity.setRecordDate(DateTimeParseUtil.parseDate(recordDateStr));

            String updatetimeStr = DateTimeParseUtil.getStringValue(sourceData, "updatetime");
            entity.setUpdatetime(DateTimeParseUtil.parseDateTime(updatetimeStr));

            entity.setUpdateman(DateTimeParseUtil.getStringValue(sourceData, "updateman"));

            // 映射预留字段 - 注意Python中字段名有拼写错误
            entity.setReservedField1(DateTimeParseUtil.getStringValue(sourceData, "reservedFeld1")); // Python中是reservedFeld1
            entity.setReservedField2(DateTimeParseUtil.getStringValue(sourceData, "reservedFeld2")); // Python中是reservedFeld2
            entity.setReservedField3(DateTimeParseUtil.getStringValue(sourceData, "reservedFeld3")); // Python中是reservedFeld3

            return entity;

        } catch (Exception e) {
            throw new RuntimeException("映射Hostel数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getUniqueField() {
        return "id";
    }
}

package com.vfdata.pujiang_vfd.modules.safety.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_szhaq_photovoltaic_energy_metrics")
@Schema(description = "光伏能源指标")
public class PhotovoltaicEnergyMetric {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "daily_generation", precision = 10, scale = 2)
    @Schema(description = "今日发电量")
    @JsonProperty("daily_generation")
    private BigDecimal dailyGeneration;

    @Column(name = "total_generation", precision = 10, scale = 2)
    @Schema(description = "历史总发电量")
    @JsonProperty("total_generation")
    private BigDecimal totalGeneration;

    @Column(name = "daily_co2_reduction", precision = 10, scale = 2)
    @Schema(description = "日减少CO2排放量")
    @JsonProperty("daily_co2_reduction")
    private BigDecimal dailyCo2Reduction;

    @Column(name = "record_time")
    @Schema(description = "记录时间")
    @JsonProperty("record_time")
    private LocalDateTime recordTime;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    @JsonProperty("updated_by")
    private String updatedBy;

    @Column(name = "update_date")
    @Schema(description = "更新日期")
    @JsonProperty("update_date")
    private LocalDate updateDate;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    @JsonProperty("reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    @JsonProperty("reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    @JsonProperty("reserved_field3")
    private String reservedField3;
}

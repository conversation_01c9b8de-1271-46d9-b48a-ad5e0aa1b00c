package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyProcessYieldInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface AssemblyProcessYieldInfoRepository extends JpaRepository<AssemblyProcessYieldInfo, Long> {

    /**
     * 获取最近7天的工序良率数据
     */
    @Query("SELECT a FROM AssemblyProcessYieldInfo a WHERE a.recordDate >= :startDate ORDER BY a.recordDate DESC")
    List<AssemblyProcessYieldInfo> findWeeklyData(LocalDate startDate);

    /**
     * 获取指定车间最近7天的工序良率数据
     */
    @Query("SELECT a FROM AssemblyProcessYieldInfo a WHERE a.workshopName = :workshopName AND a.recordDate >= :startDate ORDER BY a.recordDate DESC")
    List<AssemblyProcessYieldInfo> findWeeklyDataByWorkshopName(@Param("workshopName") String workshopName, @Param("startDate") LocalDate startDate);

    /**
     * 按记录日期降序查询所有数据
     */
    List<AssemblyProcessYieldInfo> findAllByOrderByRecordDateDesc();
}

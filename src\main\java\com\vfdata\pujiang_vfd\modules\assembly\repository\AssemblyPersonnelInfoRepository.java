package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface AssemblyPersonnelInfoRepository extends JpaRepository<AssemblyPersonnelInfo, Long> {
    
    @Query("SELECT p FROM AssemblyPersonnelInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM AssemblyPersonnelInfo p2 WHERE p2.personnelType = :type) AND p.personnelType = :type")
    Optional<AssemblyPersonnelInfo> findLatestByType(@Param("type") String type);

    @Query("SELECT p FROM AssemblyPersonnelInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM AssemblyPersonnelInfo p2)")
    List<AssemblyPersonnelInfo> findLatest();
} 
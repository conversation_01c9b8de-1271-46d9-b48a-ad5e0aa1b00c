package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionEnvironmentInfo;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionEnvironmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class InjectionEnvironmentInfoService {

    private final InjectionEnvironmentInfoRepository injectionEnvironmentInfoRepository;

    public InjectionEnvironmentInfoDTO getLatestEnvironmentInfo() {
        InjectionEnvironmentInfo latestInfo = injectionEnvironmentInfoRepository.findLatest()
                .orElseThrow(() -> new RuntimeException("未找到最新的环境信息记录"));

        InjectionEnvironmentInfoDTO dto = new InjectionEnvironmentInfoDTO();
        dto.setId(latestInfo.getId());
        dto.setDust_free_workshop_level(latestInfo.getDustFreeWorkshopLevel());
        dto.setAverage_humidity(latestInfo.getAverageHumidity());
        dto.setAverage_temperature(latestInfo.getAverageTemperature());
        dto.setRecord_date(latestInfo.getRecordDate());
        dto.setUpdated_by(latestInfo.getUpdatedBy());
        dto.setUpdated_at(latestInfo.getUpdatedAt());
        dto.setReserved_field1(latestInfo.getReservedField1());
        dto.setReserved_field2(latestInfo.getReservedField2());
        dto.setReserved_field3(latestInfo.getReservedField3());

        return dto;
    }
} 
package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyDefectClassificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/defect-analysis")
@RequiredArgsConstructor
public class NewEnergyDefectClassificationController {

    private final NewEnergyDefectClassificationService defectClassificationService;

    @GetMapping
    @Operation(summary = "获取抽检不良分析", 
               description = "workshop_name为非必填参数。不填时返回每个车间最新记录日期的数据；填写时返回指定车间最新记录日期的数据")
    public ResponseUtils.Result<List<NewEnergyDefectClassificationDTO>> getDefectClassification(
            @Parameter(description = "车间名称（可选）") 
            @RequestParam(required = false) String workshop_name) {
        try {
            List<NewEnergyDefectClassificationDTO> data = defectClassificationService.getDefectClassification(workshop_name);
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取抽检不良分析失败：" + e.getMessage());
        }
    }
}

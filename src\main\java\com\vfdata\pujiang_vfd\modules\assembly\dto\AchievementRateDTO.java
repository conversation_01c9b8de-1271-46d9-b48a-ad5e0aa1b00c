package com.vfdata.pujiang_vfd.modules.assembly.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "达成率信息")
public class AchievementRateDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "计划数量")
    private Integer planned_quantity;

    @Schema(description = "实际数量")
    private Integer actual_quantity;

    @Schema(description = "达成率")
    private Double achievement_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "预留字段1")
    private String reserved_field1;

    @Schema(description = "预留字段2")
    private String reserved_field2;

    @Schema(description = "预留字段3")
    private String reserved_field3;
}
package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldPersonnelInfoRepository extends JpaRepository<MoldPersonnelInfo, Long> {

    /**
     * 获取最新记录日期的人员信息
     */
    @Query("SELECT m FROM MoldPersonnelInfo m WHERE m.recordDate = (SELECT MAX(m2.recordDate) FROM MoldPersonnelInfo m2)")
    List<MoldPersonnelInfo> findLatestRecords();
}

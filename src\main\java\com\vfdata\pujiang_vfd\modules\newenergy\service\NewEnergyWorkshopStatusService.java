package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyWorkshopStatus;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyWorkshopStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyWorkshopStatusService {

    private final NewEnergyWorkshopStatusRepository workshopStatusRepository;

    /**
     * 获取每个车间最新记录日期的状态数据
     */
    public List<NewEnergyWorkshopStatusDTO> getLatestWorkshopStatus() {
        List<NewEnergyWorkshopStatus> statusList = workshopStatusRepository.findLatestByEachWorkshop();
        
        return statusList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyWorkshopStatusDTO convertToDTO(NewEnergyWorkshopStatus entity) {
        NewEnergyWorkshopStatusDTO dto = new NewEnergyWorkshopStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setTotal_equipment_count(entity.getTotalEquipmentCount());
        dto.setOperating_equipment_count(entity.getOperatingEquipmentCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

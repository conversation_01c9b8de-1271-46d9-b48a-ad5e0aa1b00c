package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionWorkshopStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionWorkshopStatusRepository extends JpaRepository<InjectionWorkshopStatus, Long> {
    
    @Query("SELECT w FROM InjectionWorkshopStatus w WHERE w.workshopName = :workshopName ORDER BY w.recordDate DESC, w.id DESC")
    List<InjectionWorkshopStatus> findByWorkshopName(@Param("workshopName") String workshopName);

    @Query("SELECT w FROM InjectionWorkshopStatus w ORDER BY w.workshopName, w.recordDate DESC, w.id DESC")
    List<InjectionWorkshopStatus> findAllStatus();

    @Query("SELECT w FROM InjectionWorkshopStatus w WHERE w.recordDate = (SELECT MAX(w2.recordDate) FROM InjectionWorkshopStatus w2)")
    List<InjectionWorkshopStatus> findLatestStatus();

    @Query("SELECT w FROM InjectionWorkshopStatus w WHERE w.recordDate = (SELECT MAX(w2.recordDate) FROM InjectionWorkshopStatus w2) AND w.workshopName = :workshopName")
    List<InjectionWorkshopStatus> findLatestStatusByWorkshopName(@Param("workshopName") String workshopName);
} 
package com.vfdata.pujiang_vfd.modules.factory.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "工厂通知DTO")
public class FactoryNotificationDTO {
    
    @Schema(description = "通知ID")
    private Long id;
    
    @Schema(description = "通知内容")
    private String content;
    
    @Schema(description = "记录日期")
    private LocalDateTime record_date;
    
    @Schema(description = "更新人")
    private String updated_by;
    
    @Schema(description = "更新时间")
    private LocalDateTime updated_at;
    
    @Schema(description = "预留字段1")
    private String reserved_field1;
    
    @Schema(description = "预留字段2")
    private String reserved_field2;
    
    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
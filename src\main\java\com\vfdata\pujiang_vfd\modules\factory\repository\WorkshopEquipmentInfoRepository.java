package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.WorkshopEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkshopEquipmentInfoRepository extends JpaRepository<WorkshopEquipmentInfo, Long> {

    @Query("SELECT w FROM WorkshopEquipmentInfo w WHERE w.recordDate = (SELECT MAX(w2.recordDate) FROM WorkshopEquipmentInfo w2)")
    List<WorkshopEquipmentInfo> findLatestRecords();

    @Query("SELECT w FROM WorkshopEquipmentInfo w WHERE w.workshopName = :workshopName AND w.recordDate = (SELECT MAX(w2.recordDate) FROM WorkshopEquipmentInfo w2 WHERE w2.workshopName = :workshopName)")
    List<WorkshopEquipmentInfo> findLatestRecordsByWorkshopName(@Param("workshopName") String workshopName);
} 
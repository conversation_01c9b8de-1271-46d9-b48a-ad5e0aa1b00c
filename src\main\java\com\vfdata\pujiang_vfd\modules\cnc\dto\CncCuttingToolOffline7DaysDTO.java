package com.vfdata.pujiang_vfd.modules.cnc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "CNC车间刀具近7天下机原因DTO")
public class CncCuttingToolOffline7DaysDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "原因名称")
    private String offline_name;

    @Schema(description = "下机数量")
    private Integer offline_count;

    @Schema(description = "下机占比")
    private BigDecimal offline_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldCncUtilizationRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldCncUtilizationRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/cnc-utilization-rate")
@RequiredArgsConstructor
public class MoldCncUtilizationRateController {

    private final MoldCncUtilizationRateService cncUtilizationRateService;

    @GetMapping("/6months")
    @Operation(summary = "获取模具车间CNC稼动率", description = "获取模具车间CNC稼动率数据，period=month返回6条，period=day返回7条")
    public ResponseUtils.Result<List<MoldCncUtilizationRateDTO>> getCncUtilizationRateByPeriod(@RequestParam String period) {
        try {
            List<MoldCncUtilizationRateDTO> data = cncUtilizationRateService.getCncUtilizationRateByPeriod(period);
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间CNC稼动率失败：" + e.getMessage());
        }
    }
}

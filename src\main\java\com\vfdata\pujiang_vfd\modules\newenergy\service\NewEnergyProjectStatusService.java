package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyProjectStatus;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyProjectStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyProjectStatusService {

    private final NewEnergyProjectStatusRepository projectStatusRepository;

    /**
     * 根据车间名称获取项目开机分布数据
     */
    public List<NewEnergyProjectStatusDTO> getProjectStatusByWorkshop(String workshopName) {
        List<NewEnergyProjectStatus> projectStatusList = projectStatusRepository.findByWorkshopName(workshopName);
        
        return projectStatusList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyProjectStatusDTO convertToDTO(NewEnergyProjectStatus entity) {
        NewEnergyProjectStatusDTO dto = new NewEnergyProjectStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setProject_name(entity.getProjectName());
        dto.setProduct_name(entity.getProductName());
        dto.setMachine_id(entity.getMachineId());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

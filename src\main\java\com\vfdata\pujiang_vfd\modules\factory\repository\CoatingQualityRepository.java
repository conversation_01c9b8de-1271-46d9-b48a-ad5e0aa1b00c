package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.CoatingQuality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CoatingQualityRepository extends JpaRepository<CoatingQuality, Long> {
    
    @Query("SELECT c FROM CoatingQuality c ORDER BY c.recordDate DESC, c.id DESC LIMIT 7")
    List<CoatingQuality> findLatestByWorkshopName();
} 
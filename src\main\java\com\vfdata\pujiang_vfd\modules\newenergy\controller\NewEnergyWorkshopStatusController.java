package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyWorkshopStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/workshop-status")
@RequiredArgsConstructor
public class NewEnergyWorkshopStatusController {

    private final NewEnergyWorkshopStatusService workshopStatusService;

    @GetMapping
    @Operation(summary = "获取车间状态", description = "获取每个车间最新记录日期的状态数据")
    public ResponseUtils.Result<List<NewEnergyWorkshopStatusDTO>> getLatestWorkshopStatus() {
        try {
            List<NewEnergyWorkshopStatusDTO> data = workshopStatusService.getLatestWorkshopStatus();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取车间状态失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.WorkOrderTrend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkOrderTrendRepository extends JpaRepository<WorkOrderTrend, Long> {
    
    @Query(value = """
        SELECT * FROM ioc_qc_work_order_close_rate_6months
        WHERE record_date IN (
            SELECT record_date FROM ioc_qc_work_order_close_rate_6months
            GROUP BY record_date
            ORDER BY record_date DESC
            LIMIT 6
        )
        ORDER BY workshop_name, record_date DESC
        """, nativeQuery = true)
    List<WorkOrderTrend> findLatest6DateRecords();
} 
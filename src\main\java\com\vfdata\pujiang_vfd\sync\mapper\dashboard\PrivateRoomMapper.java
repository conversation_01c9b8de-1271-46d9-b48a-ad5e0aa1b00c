package com.vfdata.pujiang_vfd.sync.mapper.dashboard;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.PrivateRoom;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import com.vfdata.pujiang_vfd.sync.util.DateTimeParseUtil;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PrivateRoomMapper implements DataMapper<PrivateRoom> {

    @Override
    public PrivateRoom mapToEntity(Map<String, Object> sourceData) {
        try {
            PrivateRoom entity = new PrivateRoom();

            // 映射基础字段
            entity.setId(DateTimeParseUtil.getLongValue(sourceData, "id"));
            entity.setPrivateroomNum(DateTimeParseUtil.getIntegerValue(sourceData, "privateroomNum"));
            entity.setPrivateroomUse(DateTimeParseUtil.getIntegerValue(sourceData, "privateroomUse"));
            entity.setPrivateroomRemaining(DateTimeParseUtil.getIntegerValue(sourceData, "privateroomRemaining"));

            // 映射时间字段
            String recordDateStr = DateTimeParseUtil.getStringValue(sourceData, "recordDate");
            entity.setRecordDate(DateTimeParseUtil.parseDate(recordDateStr));

            String updatetimeStr = DateTimeParseUtil.getStringValue(sourceData, "updatetime");
            entity.setUpdatetime(DateTimeParseUtil.parseDateTime(updatetimeStr));

            entity.setUpdateman(DateTimeParseUtil.getStringValue(sourceData, "updateman"));

            // 映射预留字段
            entity.setReservedField1(DateTimeParseUtil.getStringValue(sourceData, "reservedField1"));
            entity.setReservedField2(DateTimeParseUtil.getStringValue(sourceData, "reservedField2"));
            entity.setReservedField3(DateTimeParseUtil.getStringValue(sourceData, "reservedField3"));

            return entity;

        } catch (Exception e) {
            throw new RuntimeException("映射PrivateRoom数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getUniqueField() {
        return "id";
    }
}

package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.FactoryEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.FactoryEquipmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "全厂页面接口")
@RestController
@RequestMapping("/factory/equipment")
@RequiredArgsConstructor
public class FactoryEquipmentInfoController {

    private final FactoryEquipmentInfoService factoryEquipmentInfoService;

    @Operation(summary = "获取最新设备信息")
    @GetMapping("/latest")
    public ResponseUtils.Result<List<FactoryEquipmentInfoDTO>> getLatestEquipmentInfo() {
        try {
            List<FactoryEquipmentInfoDTO> data = factoryEquipmentInfoService.getLatestEquipmentInfo();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取设备信息失败：" + e.getMessage());
        }
    }
} 
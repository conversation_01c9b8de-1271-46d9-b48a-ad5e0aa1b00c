package com.vfdata.pujiang_vfd.sync.service;

import com.vfdata.pujiang_vfd.config.DataSyncConfig;
import com.vfdata.pujiang_vfd.sync.client.DataSyncClient;
import com.vfdata.pujiang_vfd.sync.mapper.DataMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataSyncService {

    private final DataSyncClient dataSyncClient;
    private final DataSyncConfig config;

    /**
     * 通用数据同步方法
     * 
     * @param endpoint API端点
     * @param repository JPA仓库
     * @param mapper 数据映射器
     * @param <T> 实体类型
     * @param <ID> 主键类型
     * @return 同步结果统计
     */
    @Transactional
    public <T, ID> SyncResult syncData(String endpoint, 
                                       JpaRepository<T, ID> repository, 
                                       DataMapper<T> mapper) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始同步数据: {}, 时间: {}", endpoint, startTime);
        
        SyncResult result = new SyncResult();
        result.setEndpoint(endpoint);
        result.setStartTime(startTime);
        
        try {
            // 获取外部数据
            List<Map<String, Object>> sourceData = dataSyncClient.fetchDataWithRetry(endpoint);
            
            if (sourceData.isEmpty()) {
                log.warn("从接口获取数据为空: {}", endpoint);
                result.setSuccess(false);
                result.setErrorMessage("获取数据为空");
                return result;
            }
            
            result.setTotalRecords(sourceData.size());
            log.info("准备同步数据: {}, 数据量: {}", endpoint, sourceData.size());
            
            int insertCount = 0;
            int updateCount = 0;
            int skipCount = 0;
            
            // 分批处理数据
            for (int i = 0; i < sourceData.size(); i += config.getBatchSize()) {
                int endIndex = Math.min(i + config.getBatchSize(), sourceData.size());
                List<Map<String, Object>> batch = sourceData.subList(i, endIndex);
                
                for (Map<String, Object> item : batch) {
                    try {
                        // 映射数据
                        T entity = mapper.mapToEntity(item);
                        
                        if (entity == null) {
                            skipCount++;
                            continue;
                        }
                        
                        // 检查是否已存在
                        if (config.isSkipExisting()) {
                            // 这里需要根据具体的唯一标识来查询
                            // 由于JPA Repository没有通用的按字段查询方法，
                            // 这部分逻辑需要在具体的同步任务中实现
                            repository.save(entity);
                            insertCount++;
                        } else {
                            repository.save(entity);
                            insertCount++;
                        }
                        
                    } catch (Exception e) {
                        log.error("处理单条记录失败: {}, 数据: {}", e.getMessage(), item);
                        result.getErrors().add("处理记录失败: " + e.getMessage());
                    }
                }
                
                // 批次间短暂休息
                if (endIndex < sourceData.size()) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            result.setInsertCount(insertCount);
            result.setUpdateCount(updateCount);
            result.setSkipCount(skipCount);
            result.setSuccess(true);
            
            LocalDateTime endTime = LocalDateTime.now();
            result.setEndTime(endTime);
            
            log.info("数据同步完成: {}, 插入: {}, 更新: {}, 跳过: {}, 耗时: {}ms", 
                endpoint, insertCount, updateCount, skipCount, 
                java.time.Duration.between(startTime, endTime).toMillis());
            
        } catch (Exception e) {
            log.error("数据同步失败: {}, 错误: {}", endpoint, e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }

    /**
     * 同步结果统计类
     */
    public static class SyncResult {
        private String endpoint;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private boolean success;
        private String errorMessage;
        private int totalRecords;
        private int insertCount;
        private int updateCount;
        private int skipCount;
        private List<String> errors = new java.util.ArrayList<>();

        // Getters and Setters
        public String getEndpoint() { return endpoint; }
        public void setEndpoint(String endpoint) { this.endpoint = endpoint; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public int getTotalRecords() { return totalRecords; }
        public void setTotalRecords(int totalRecords) { this.totalRecords = totalRecords; }
        
        public int getInsertCount() { return insertCount; }
        public void setInsertCount(int insertCount) { this.insertCount = insertCount; }
        
        public int getUpdateCount() { return updateCount; }
        public void setUpdateCount(int updateCount) { this.updateCount = updateCount; }
        
        public int getSkipCount() { return skipCount; }
        public void setSkipCount(int skipCount) { this.skipCount = skipCount; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
    }
}

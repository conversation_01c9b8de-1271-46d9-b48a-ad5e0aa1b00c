package com.vfdata.pujiang_vfd.modules.newenergy.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_xnycj_pass_rate")
@Schema(description = "新能源车间性能测试良率")
public class NewEnergyPassRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "所属车间")
    private String workshopName;

    @Column(name = "quality_rate", precision = 5, scale = 2)
    @Schema(description = "合格率")
    private BigDecimal qualityRate;

    @Column(name = "record_date")
    @Schema(description = "日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "备用字段3")
    private String reservedField3;
}

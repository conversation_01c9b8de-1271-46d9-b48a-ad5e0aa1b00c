package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.MaterialPassRateTrendDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.MaterialPassRateTrend;
import com.vfdata.pujiang_vfd.modules.factory.repository.MaterialPassRateTrendRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MaterialPassRateTrendService {

    private final MaterialPassRateTrendRepository materialPassRateTrendRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    /**
     * 获取物料合格率趋势信息
     * 返回最新的6条数据
     */
    public List<MaterialPassRateTrendDTO> getMaterialPassRateTrendInfo() {
        List<MaterialPassRateTrend> trendList = materialPassRateTrendRepository.findLatest();
        return trendList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private MaterialPassRateTrendDTO convertToDTO(MaterialPassRateTrend entity) {
        MaterialPassRateTrendDTO dto = new MaterialPassRateTrendDTO();
        dto.setId(entity.getId());
        dto.setPass_rate(entity.getPassRate());
        dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt().format(DATETIME_FORMATTER));
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
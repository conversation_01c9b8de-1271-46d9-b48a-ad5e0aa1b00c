package com.vfdata.pujiang_vfd.modules.factory.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "车间人员信息DTO")
public class WorkshopPersonnelInfoDTO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "车间名称")
    private String workshop_name;
    
    @Schema(description = "工人总数")
    private Integer total_worker_count;
    
    @Schema(description = "职员总数")
    private Integer total_clerical_count;
    
    @Schema(description = "记录日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate record_date;
    
    @Schema(description = "更新人")
    private String updated_by;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS")
    private LocalDateTime updated_at;
    
    @Schema(description = "预留字段1")
    private String reserved_field1;
    
    @Schema(description = "预留字段2")
    private String reserved_field2;
    
    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
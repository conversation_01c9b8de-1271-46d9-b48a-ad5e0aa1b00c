package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingPaintUsageInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingPaintUsageInfoRepository extends JpaRepository<PaintingPaintUsageInfo, Long> {

    /**
     * 获取最新记录日期的所有车间数据
     */
    @Query("SELECT p FROM PaintingPaintUsageInfo p order by p.recordDate limit 7")
    List<PaintingPaintUsageInfo> findLatestRecords();

    /**
     * 获取指定车间最新记录日期的数据
     */
    @Query("SELECT p FROM PaintingPaintUsageInfo p WHERE p.workshopName = :workshopName order by p.recordDate limit 7 ")
    PaintingPaintUsageInfo findLatestRecordByWorkshop(@Param("workshopName") String workshopName);
}

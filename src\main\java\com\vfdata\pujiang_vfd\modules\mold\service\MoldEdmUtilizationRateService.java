package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldEdmUtilizationRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldEdmUtilizationRate;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldEdmUtilizationRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldEdmUtilizationRateService {

    private final MoldEdmUtilizationRateRepository edmUtilizationRateRepository;

    /**
     * 获取EDM稼动率数据
     * @param period 周期类型：month返回6条，day返回7条
     */
    public List<MoldEdmUtilizationRateDTO> getEdmUtilizationRateByPeriod(String period) {
        int limit = "day".equals(period) ? 7 : 6;
        List<MoldEdmUtilizationRate> entities = edmUtilizationRateRepository.findLatestRecordsByPeriod(period, limit);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldEdmUtilizationRateDTO convertToDTO(MoldEdmUtilizationRate entity) {
        MoldEdmUtilizationRateDTO dto = new MoldEdmUtilizationRateDTO();
        dto.setId(entity.getId());
        dto.setPeriod(entity.getPeriod());
        dto.setOnce_utilization_rate(entity.getOnceUtilizationRate());
        dto.setEnd_utilization_rate(entity.getEndUtilizationRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncCuttingToolUsage7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncCuttingToolUsage7Days;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncCuttingToolUsage7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncCuttingToolUsage7DaysService {

    private final CncCuttingToolUsage7DaysRepository cuttingToolUsage7DaysRepository;

    /**
     * 获取指定车间的刀具用量近7天数据
     */
    public List<CncCuttingToolUsage7DaysDTO> getCuttingToolUsage7Days(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<CncCuttingToolUsage7Days> entities = cuttingToolUsage7DaysRepository.findLatest7RecordsByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncCuttingToolUsage7DaysDTO convertToDTO(CncCuttingToolUsage7Days entity) {
        CncCuttingToolUsage7DaysDTO dto = new CncCuttingToolUsage7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setCuttingtool_usage(entity.getCuttingtoolUsage());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

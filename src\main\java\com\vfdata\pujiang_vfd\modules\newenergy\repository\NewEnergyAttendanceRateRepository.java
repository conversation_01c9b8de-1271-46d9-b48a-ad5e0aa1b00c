package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyAttendanceRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NewEnergyAttendanceRateRepository extends JpaRepository<NewEnergyAttendanceRate, Long> {

    /**
     * 获取最新记录日期的出勤率数据
     * SQL: SELECT * from ioc_xnycj_attendance_rate where record_date = (select max(record_date) from ioc_xnycj_attendance_rate)
     */
    @Query("SELECT n FROM NewEnergyAttendanceRate n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyAttendanceRate n2)")
    List<NewEnergyAttendanceRate> findLatestAttendanceRate();
}

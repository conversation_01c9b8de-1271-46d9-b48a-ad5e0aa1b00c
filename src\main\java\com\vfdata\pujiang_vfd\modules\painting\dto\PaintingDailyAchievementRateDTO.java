package com.vfdata.pujiang_vfd.modules.painting.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "喷涂车间当日计划达成率DTO")
public class PaintingDailyAchievementRateDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "当日计划生产数量")
    private Integer planned_quantity;

    @Schema(description = "当日实际完成数量")
    private Integer actual_quantity;

    @Schema(description = "计划达成率")
    private Double achievement_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

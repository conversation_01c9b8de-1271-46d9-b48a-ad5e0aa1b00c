package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionEnvironmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/injection/environment-info")
@RequiredArgsConstructor
@Tag(name = "注塑车间页面接口")
public class InjectionEnvironmentInfoController {

    private final InjectionEnvironmentInfoService injectionEnvironmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新环境信息", description = "获取注塑车间最新的环境信息记录")
    public ResponseUtils.Result<InjectionEnvironmentInfoDTO> getLatestEnvironmentInfo() {
        return ResponseUtils.success(injectionEnvironmentInfoService.getLatestEnvironmentInfo());
    }
} 
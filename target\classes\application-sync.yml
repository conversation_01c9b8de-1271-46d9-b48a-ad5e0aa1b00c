# 数据同步配置
data-sync:
  # 是否启用数据同步
  enabled: false
  
  # 外部数据源基础URL
  base-url: http://*************:8081
  
  # HTTP连接超时时间（毫秒）
  connect-timeout: 30000
  
  # HTTP读取超时时间（毫秒）
  read-timeout: 60000
  
  # 批量处理大小
  batch-size: 1000
  
  # 是否跳过已存在的记录
  skip-existing: true
  
  # 同步间隔（分钟）
  sync-interval-minutes: 10
  
  # 最大重试次数
  max-retries: 3
  
  # 重试间隔（毫秒）
  retry-interval: 5000

# 异步任务配置
spring:
  task:
    execution:
      pool:
        core-size: 4
        max-size: 8
        queue-capacity: 100
      thread-name-prefix: sync-task-

# 日志配置
logging:
  level:
    com.vfdata.pujiang_vfd.sync: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

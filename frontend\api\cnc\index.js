import request from '@/utils/request'

/**
 * 获取CNC车间人员信息
 * @returns {Promise} 返回人员信息数据
 */
export const getPersonnelInfo = async () => {
  return await request.get('/cnc/personnel-info/latest')
}

/**
 * 获取CNC车间出勤率信息
 * @returns {Promise} 返回出勤率数据
 */
export const getAttendanceRate = async () => {
  return await request.get('/cnc/attendance-rate/latest')
}

/**
 * 获取CNC车间设备信息
 * @returns {Promise} 返回设备信息数据
 */
export const getEquipmentInfo = async () => {
  return await request.get('/cnc/equipment-info/latest')
}

/**
 * 获取CNC车间环境信息
 * @returns {Promise} 返回环境信息数据
 */
export const getEnvironmentInfo = async () => {
  return await request.get('/cnc/environment-info/latest')
}

/**
 * 获取CNC车间每日达成率
 * @returns {Promise} 返回每日达成率数据
 */
export const getDailyAchievementRate = async () => {
  return await request.get('/cnc/daily-achievement-rate/latest')
}

/**
 * 获取CNC车间质量率信息
 * @returns {Promise} 返回每个车间最新的质量率数据
 */
export const getQualityRate = async () => {
  return await request.get('/cnc/quality-rate/latest')
}

/**
 * 获取CNC车间质量率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天质量率数据
 */
export const getQualityRate7Days = async (workshopName) => {
  return await request.get('/cnc/quality-rate-7days/latest', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取CNC车间计划完成率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天计划完成率数据
 */
export const getPlanCompletionRate7Days = async (workshopName) => {
  return await request.get('/cnc/plan-completion-rate-7days/latest', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取CNC车间刀具离线7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天刀具离线数据
 */
export const getCuttingToolOffline7Days = async (workshopName) => {
  return await request.get('/cnc/cuttingtool-offline-7days/latest', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取CNC车间刀具用量7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天刀具用量数据
 */
export const getCuttingToolUsage7Days = async (workshopName) => {
  return await request.get('/cnc/cuttingtool-usage-7days', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取CNC车间刀具消耗指数
 * @returns {Promise} 返回每个车间最新的刀具消耗指数数据
 */
export const getCuttingToolUsageInfo = async () => {
  return await request.get('/cnc/quality-rate/latest')
}

/**
 * 获取CNC车间设备状态分布
 * @param {string} workshopName - 车间名称（可选）
 * @returns {Promise} 返回设备状态分布数据
 */
export const getEquipmentStatus = async (workshopName = '') => {
  const params = workshopName ? { workshop_name: workshopName } : {}
  return await request.get('/cnc/equipment-status/latest', { params })
}

/**
 * 获取CNC车间项目状态
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间项目状态数据
 */
export const getProjectStatus = async (workshopName) => {
  return await request.get('/cnc/project-status/latest', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取CNC车间设备状况
 * @returns {Promise} 返回每个车间最新的设备状况数据
 */
export const getWorkshopStatus = async () => {
  return await request.get('/cnc/workshop-status/latest')
}

/**
 * CNC车间相关API集合
 */
export default {
  // 基础信息接口（返回所有车间或单条数据）
  getPersonnelInfo,
  getAttendanceRate,
  getEquipmentInfo,
  getEnvironmentInfo,
  getDailyAchievementRate,
  getQualityRate,
  getCuttingToolUsageInfo,
  getWorkshopStatus,
  
  // 需要车间参数的接口
  getQualityRate7Days,
  getPlanCompletionRate7Days,
  getCuttingToolOffline7Days,
  getCuttingToolUsage7Days,
  getProjectStatus,
  
  // 可选车间参数的接口
  getEquipmentStatus
}

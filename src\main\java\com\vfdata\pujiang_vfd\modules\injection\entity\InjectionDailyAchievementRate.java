package com.vfdata.pujiang_vfd.modules.injection.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zscj_daily_achievement_rate")
@Schema(description = "注塑车间今日达成率")
public class InjectionDailyAchievementRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    @Column(name = "workshop_name")
    private String workshopName;

    @Schema(description = "计划数量")
    @Column(name = "planned_quantity")
    private Integer plannedQuantity;

    @Schema(description = "实际数量")
    @Column(name = "actual_quantity")
    private Integer actualQuantity;

    @Schema(description = "达成率")
    @Column(name = "achievement_rate")
    private Float achievementRate;

    @Schema(description = "记录日期")
    @Column(name = "record_date")
    private LocalDate recordDate;

    @Schema(description = "更新人")
    @Column(name = "updated_by")
    private String updatedBy;

    @Schema(description = "更新时间")
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Schema(description = "预留字段1")
    @Column(name = "reserved_field1")
    private String reservedField1;

    @Schema(description = "预留字段2")
    @Column(name = "reserved_field2")
    private String reservedField2;

    @Schema(description = "预留字段3")
    @Column(name = "reserved_field3")
    private String reservedField3;
} 
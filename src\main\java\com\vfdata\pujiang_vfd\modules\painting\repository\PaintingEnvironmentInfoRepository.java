package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingEnvironmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingEnvironmentInfoRepository extends JpaRepository<PaintingEnvironmentInfo, Long> {

    /**
     * 获取最新记录日期的所有环境信息
     */
    @Query("SELECT p FROM PaintingEnvironmentInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingEnvironmentInfo p2)")
    List<PaintingEnvironmentInfo> findLatestRecords();
}

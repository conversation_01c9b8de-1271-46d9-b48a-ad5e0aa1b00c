package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.MaterialPassRateTrend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MaterialPassRateTrendRepository extends JpaRepository<MaterialPassRateTrend, Long> {
    
    @Query("SELECT m FROM MaterialPassRateTrend m ORDER BY m.recordDate DESC, m.id DESC LIMIT 6")
    List<MaterialPassRateTrend> findLatest();
} 
package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyPassRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyPassRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/pass-rate-7days")
@RequiredArgsConstructor
public class NewEnergyPassRate7DaysController {

    private final NewEnergyPassRate7DaysService passRate7DaysService;

    @GetMapping
    @Operation(summary = "获取性能测试良率近7天数据", description = "获取最新7条性能测试良率数据")
    public ResponseUtils.Result<List<NewEnergyPassRate7DaysDTO>> getPassRate7Days() {
        try {
            List<NewEnergyPassRate7DaysDTO> data = passRate7DaysService.getPassRate7Days();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取性能测试良率近7天数据失败：" + e.getMessage());
        }
    }
}

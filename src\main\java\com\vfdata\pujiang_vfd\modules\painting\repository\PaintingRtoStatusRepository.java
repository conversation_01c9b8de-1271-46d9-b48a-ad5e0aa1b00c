package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingRtoStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PaintingRtoStatusRepository extends JpaRepository<PaintingRtoStatus, Long> {

    /**
     * 获取最新的RTO状态记录
     */
    @Query("SELECT p FROM PaintingRtoStatus p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingRtoStatus p2)")
    PaintingRtoStatus findLatestRecord();
}

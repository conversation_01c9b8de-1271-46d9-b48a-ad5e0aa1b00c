package com.vfdata.pujiang_vfd.modules.painting.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_ptcj_equipment_info")
@Schema(description = "喷涂车间设备信息")
public class PaintingEquipmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "operating_rate")
    @Schema(description = "设备开机率")
    private BigDecimal operatingRate;

    @Column(name = "total_equipment_count")
    @Schema(description = "设备/线体总数")
    private Integer totalEquipmentCount;

    @Column(name = "operating_equipment_count")
    @Schema(description = "当前开机/开线的设备数量")
    private Integer operatingEquipmentCount;

    @Column(name = "oee_rate")
    @Schema(description = "设备能源效率指标（OEE）")
    private BigDecimal oeeRate;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

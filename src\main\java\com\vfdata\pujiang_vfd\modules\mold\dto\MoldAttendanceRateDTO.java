package com.vfdata.pujiang_vfd.modules.mold.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "模具车间出勤率DTO")
public class MoldAttendanceRateDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "职员出勤率")
    private BigDecimal clerk_attendance_rate;

    @Schema(description = "普工出勤率")
    private BigDecimal worker_attendance_rate;

    @Schema(description = "职员出勤人数")
    private Integer clerk_attendance_num;

    @Schema(description = "普工出勤人数")
    private Integer worker_attendance_num;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

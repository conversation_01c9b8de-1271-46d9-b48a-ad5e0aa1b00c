package com.vfdata.pujiang_vfd.modules.safety.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_szhaq_vehicle_records")
@Schema(description = "车辆进出记录")
public class VehicleRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "license_plate", length = 100)
    @Schema(description = "车牌号")
    @JsonProperty("license_plate")
    private String licensePlate;

    @Column(name = "channel_name", length = 100)
    @Schema(description = "通道名称")
    @JsonProperty("channel_name")
    private String channelName;

    @Column(name = "entry_time")
    @Schema(description = "进入时间")
    @JsonProperty("entry_time")
    private LocalDateTime entryTime;

    @Column(name = "status", length = 50)
    @Schema(description = "状态（进/出）")
    private String status;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    @JsonProperty("updated_by")
    private String updatedBy;

    @Column(name = "update_date")
    @Schema(description = "更新日期")
    @JsonProperty("update_date")
    private LocalDate updateDate;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    @JsonProperty("reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    @JsonProperty("reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    @JsonProperty("reserved_field3")
    private String reservedField3;
}

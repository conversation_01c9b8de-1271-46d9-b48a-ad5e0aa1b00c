package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.WorkOrderTrendDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.WorkOrderTrendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/work-orders")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class WorkOrderTrendController {

    private final WorkOrderTrendService workOrderTrendService;

    @GetMapping("/trend/")
    @Operation(summary = "获取工单关闭率趋势", description = "获取最近6个日期的工单关闭率数据")
    public ResponseUtils.Result<List<WorkOrderTrendDTO>> getWorkOrderTrendInfo() {
        try {
            List<WorkOrderTrendDTO> trendInfo = workOrderTrendService.getWorkOrderTrendInfo();
            return ResponseUtils.success(trendInfo);
        } catch (Exception e) {
            return ResponseUtils.error("获取工单趋势信息失败：" + e.getMessage());
        }
    }
} 
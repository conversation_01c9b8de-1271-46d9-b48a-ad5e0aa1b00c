import os
from functools import lru_cache
from typing import Optional
from pydantic import BaseModel

# 获取项目根目录
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

class Settings(BaseModel):
    # 数据库配置
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: str = "5432"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "postgres"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 其他配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "FastAPI Project"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = os.path.join(BASE_DIR, "logs", "app.log")
    LOG_FILE_ERROR: str = os.path.join(BASE_DIR, "logs", "error.log")
    LOG_FILE_SIZE: int = 1024 * 1024 * 10  # 10MB
    LOG_FILE_COUNT: int = 5

    @property
    def SQLALCHEMY_DATABASE_URL(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

@lru_cache()
def get_settings() -> Settings:
    return Settings(
        POSTGRES_HOST="*************",
        POSTGRES_PORT="2345",
        POSTGRES_USER="dpbia",
        POSTGRES_PASSWORD="8ujshjsuwns#sUt",
        POSTGRES_DB="dpbia"
    )

settings = get_settings()

# 确保日志目录存在
os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True) 

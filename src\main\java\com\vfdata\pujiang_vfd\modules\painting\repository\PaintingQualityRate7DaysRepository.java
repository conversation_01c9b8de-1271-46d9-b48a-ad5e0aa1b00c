package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingQualityRate7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingQualityRate7DaysRepository extends JpaRepository<PaintingQualityRate7Days, Long> {

    /**
     * 获取指定车间的最新7条记录
     */
    @Query("SELECT p FROM PaintingQualityRate7Days p WHERE p.workshopName = :workshopName ORDER BY p.recordDate DESC LIMIT 7")
    List<PaintingQualityRate7Days> findLatest7RecordsByWorkshop(@Param("workshopName") String workshopName);
}

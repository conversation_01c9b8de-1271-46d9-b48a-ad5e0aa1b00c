package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionStopType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionStopTypeRepository extends JpaRepository<InjectionStopType, Long> {
    
    @Query("SELECT s FROM InjectionStopType s WHERE s.workshopName = :workshopName ORDER BY s.recordDate DESC, s.id DESC")
    List<InjectionStopType> findByWorkshopName(@Param("workshopName") String workshopName);
    
    @Query("SELECT s FROM InjectionStopType s ORDER BY s.workshopName, s.recordDate DESC, s.id DESC")
    List<InjectionStopType> findAllStopTypes();
} 
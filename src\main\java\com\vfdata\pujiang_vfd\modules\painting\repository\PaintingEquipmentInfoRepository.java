package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface PaintingEquipmentInfoRepository extends JpaRepository<PaintingEquipmentInfo, Long> {

    /**
     * 获取最新的设备信息记录
     */
    @Query("SELECT p FROM PaintingEquipmentInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingEquipmentInfo p2)")
    PaintingEquipmentInfo findLatestRecord();
}

package com.vfdata.pujiang_vfd.modules.assembly.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;

@Data
@Schema(description = "组装车间7天计划完成率DTO")
public class AssemblyPlanCompletionRate7DaysDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "完成率")
    private Float completion_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新日期")
    private LocalDate update_date;

    @Schema(description = "预留字段1")
    private String reserved_field1;

    @Schema(description = "预留字段2")
    private String reserved_field2;

    @Schema(description = "预留字段3")
    private String reserved_field3;
}

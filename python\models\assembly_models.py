from sqlalchemy import Column, Integer, String, Date, DECIMAL, TIMESTAMP, Text, Float, DateTime, ForeignKey
from database import Base
from datetime import datetime

class AssemblyBase(Base):
    """装配车间基础模型"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    record_date = Column(DateTime, nullable=False, comment="记录日期")
    updated_by = Column(String(50), nullable=False, comment="更新人")
    updated_at = Column(DateTime, nullable=False, default=datetime.now, comment="更新时间")
    reserved_1 = Column(String(200), nullable=True, comment="预留字段1")
    reserved_2 = Column(String(200), nullable=True, comment="预留字段2")
    reserved_3 = Column(String(200), nullable=True, comment="预留字段3")

class ProductionProgress(AssemblyBase):
    """生产进度表"""
    __tablename__ = "assembly_production_progress"
    
    product_name = Column(String(100), nullable=False, comment="产品名称")
    planned_quantity = Column(Integer, nullable=False, comment="计划数量")
    completed_quantity = Column(Integer, nullable=False, comment="完成数量")
    completion_rate = Column(Float, nullable=False, comment="完成率")

class LineStatus(AssemblyBase):
    """装配线状态表"""
    __tablename__ = "assembly_line_status"
    
    line_name = Column(String(100), nullable=False, comment="装配线名称")
    status = Column(String(50), nullable=False, comment="运行状态")
    efficiency = Column(Float, nullable=False, comment="效率")

class QualityInspection(AssemblyBase):
    """质量检验表"""
    __tablename__ = "assembly_quality_inspection"
    
    product_name = Column(String(100), nullable=False, comment="产品名称")
    batch_number = Column(String(50), nullable=False, comment="批次号")
    inspection_quantity = Column(Integer, nullable=False, comment="检验数量")
    defect_quantity = Column(Integer, nullable=False, comment="不良品数量")
    pass_rate = Column(Float, nullable=False, comment="合格率")

class StaffEfficiency(AssemblyBase):
    """人员效率表"""
    __tablename__ = "assembly_staff_efficiency"
    
    staff_name = Column(String(50), nullable=False, comment="人员姓名")
    position = Column(String(50), nullable=False, comment="岗位")
    output_quantity = Column(Integer, nullable=False, comment="产出数量")
    working_hours = Column(Float, nullable=False, comment="工作时长")
    efficiency_rate = Column(Float, nullable=False, comment="效率指标")

class AssemblyPersonnelInfo(AssemblyBase):
    """人员信息表"""
    __tablename__ = "assembly_personnel_info"
    
    personnel_type = Column(String(50), nullable=False, comment="人员类型")
    staff_count = Column(Integer, nullable=False, comment="职员数量")
    general_worker_count = Column(Integer, nullable=False, comment="普工数量")

class AssemblyAttendanceRate(AssemblyBase):
    """出勤率表"""
    __tablename__ = "assembly_attendance_rate"
    
    clerk_attendance_rate = Column(Float, nullable=False, comment="职员出勤率")
    worker_attendance_rate = Column(Float, nullable=False, comment="工人出勤率")

class AssemblyFQCQualityRate(AssemblyBase):
    """FQC质量表"""
    __tablename__ = "assembly_fqc_quality_rate"
    
    workshop_name = Column(String(50), nullable=False, comment="车间名称")
    quality_rate = Column(Float, nullable=False, comment="质量合格率")

class AssemblyFQCQualityRate7Days(AssemblyBase):
    """FQC 7天质量趋势表"""
    __tablename__ = "assembly_fqc_quality_rate_7days"
    
    workshop_name = Column(String(50), nullable=False, comment="车间名称")
    quality_rate = Column(Float, nullable=False, comment="质量合格率")

class AssemblyEnvironmentInfo(AssemblyBase):
    """环境信息表"""
    __tablename__ = "assembly_environment_info"
    
    dust_free_workshop_level = Column(String(50), nullable=False, comment="无尘车间等级")
    average_humidity = Column(Float, nullable=False, comment="平均湿度")
    average_temperature = Column(Float, nullable=False, comment="平均温度")
    workshop_name = Column(String(50), nullable=False, comment="车间名称") 
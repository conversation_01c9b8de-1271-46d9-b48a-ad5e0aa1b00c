package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.FactoryEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FactoryEquipmentInfoRepository extends JpaRepository<FactoryEquipmentInfo, Long> {

    @Query("SELECT f FROM FactoryEquipmentInfo f WHERE f.recordDate = (SELECT MAX(f2.recordDate) FROM FactoryEquipmentInfo f2)")
    List<FactoryEquipmentInfo> findLatestRecords();
} 
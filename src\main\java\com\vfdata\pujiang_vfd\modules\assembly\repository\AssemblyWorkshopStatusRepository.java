package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyWorkshopStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface AssemblyWorkshopStatusRepository extends JpaRepository<AssemblyWorkshopStatus, Long> {
    
    @Query("SELECT w FROM AssemblyWorkshopStatus w WHERE w.recordDate = (SELECT MAX(w2.recordDate) FROM AssemblyWorkshopStatus w2)")
    List<AssemblyWorkshopStatus> findLatest();

    @Query("SELECT w FROM AssemblyWorkshopStatus w WHERE w.workshopName = :workshopName AND w.recordDate = (SELECT MAX(w2.recordDate) FROM AssemblyWorkshopStatus w2 WHERE w2.workshopName = :workshopName)")
    List<AssemblyWorkshopStatus> findLatestByWorkshopName(@Param("workshopName") String workshopName);
} 
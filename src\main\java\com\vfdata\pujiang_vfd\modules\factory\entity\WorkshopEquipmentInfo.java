package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "ioc_qc_workshop_equipment_status")
public class WorkshopEquipmentInfo extends BaseEntity {

    @Column(name = "workshop_name", length = 20)
    private String workshopName;

    @Column(name = "operating_rate", precision = 5, scale = 2)
    private BigDecimal operatingRate;
} 
package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyEquipmentInfoService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/equipment-info")
@RequiredArgsConstructor
public class AssemblyEquipmentInfoController {

    private final AssemblyEquipmentInfoService equipmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新设备信息")
    public ResponseUtils.Result<AssemblyEquipmentInfoDTO> getLatestEquipmentInfo() {
        try {
            AssemblyEquipmentInfoDTO infoList = equipmentInfoService.getLatestEquipmentInfo();
            return ResponseUtils.success(infoList);
        } catch (Exception e) {
            return ResponseUtils.error("获取设备信息失败：" + e.getMessage());
        }
    }
} 
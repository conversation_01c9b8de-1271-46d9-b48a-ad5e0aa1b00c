package com.vfdata.pujiang_vfd.modules.newenergy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Schema(description = "铜铝用量数据DTO")
public class TongLvUsageDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "铜用量")
    private BigDecimal tongUsage;

    @Schema(description = "铝用量")
    private BigDecimal lvUsage;

    @Schema(description = "记录日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate recordDate;

    @Schema(description = "更新人")
    private String updatedBy;
}

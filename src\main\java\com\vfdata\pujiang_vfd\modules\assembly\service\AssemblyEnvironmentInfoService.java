package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyEnvironmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyEnvironmentInfo;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyEnvironmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AssemblyEnvironmentInfoService {
    
    private final AssemblyEnvironmentInfoRepository environmentInfoRepository;
    
    public AssemblyEnvironmentInfoDTO getLatestEnvironmentInfo() {
        Optional<AssemblyEnvironmentInfo> optionalInfo = environmentInfoRepository.findLatest();
        if (optionalInfo.isPresent()) {
            AssemblyEnvironmentInfo info = optionalInfo.get();
            AssemblyEnvironmentInfoDTO dto = new AssemblyEnvironmentInfoDTO();
            dto.setId(info.getId());
            dto.setDust_free_workshop_level(info.getDustFreeWorkshopLevel());
            dto.setAverage_humidity(info.getAverageHumidity());
            dto.setAverage_temperature(info.getAverageTemperature());
            dto.setRecord_date(info.getRecordDate());
            dto.setUpdated_by(info.getUpdatedBy());
            dto.setUpdated_at(info.getUpdatedAt());
            dto.setReserved_field1(info.getReservedField1());
            dto.setReserved_field2(info.getReservedField2());
            dto.setReserved_field3(info.getReservedField3());
            return dto;
        }
        return null;
    }
} 
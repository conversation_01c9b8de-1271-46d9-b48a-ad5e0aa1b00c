package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldProgressDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldProgressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/progress")
@RequiredArgsConstructor
public class MoldProgressController {

    private final MoldProgressService moldProgressService;

    @GetMapping("/latest")
    @Operation(summary = "获取模具车间模具进度", description = "获取模具车间最新的模具进度信息")
    public ResponseUtils.Result<List<MoldProgressDTO>> getLatestMoldProgress() {
        try {
            List<MoldProgressDTO> data = moldProgressService.getLatestMoldProgress();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间模具进度失败：" + e.getMessage());
        }
    }
}

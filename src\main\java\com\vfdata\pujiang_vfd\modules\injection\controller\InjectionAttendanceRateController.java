package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionAttendanceRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/injection/attendance")
@RequiredArgsConstructor
@Tag(name = "注塑车间页面接口")
public class InjectionAttendanceRateController {

    private final InjectionAttendanceRateService injectionAttendanceRateService;

    @GetMapping("/latest/v1")
    @Operation(summary = "获取最新出勤率", description = "获取注塑车间最新的出勤率记录")
    public ResponseUtils.Result<InjectionAttendanceRateDTO> getLatestAttendanceRate() {
        return ResponseUtils.success(injectionAttendanceRateService.getLatestAttendanceRate());
    }
} 


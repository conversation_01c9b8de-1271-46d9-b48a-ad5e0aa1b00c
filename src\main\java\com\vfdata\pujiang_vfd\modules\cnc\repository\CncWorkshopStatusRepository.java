package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncWorkshopStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncWorkshopStatusRepository extends JpaRepository<CncWorkshopStatus, Long> {

    /**
     * 获取每个车间最新的设备状况记录
     */
    @Query("SELECT c FROM CncWorkshopStatus c WHERE c.recordDate = (SELECT MAX(c2.recordDate) FROM CncWorkshopStatus c2 WHERE c2.workshopName = c.workshopName)")
    List<CncWorkshopStatus> findLatestRecords();
}

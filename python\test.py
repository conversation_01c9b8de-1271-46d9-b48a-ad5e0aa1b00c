from flask import Flask, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)  # 启用 CORS 支持

# 模拟数据存储（内存中）
mock_data = {
    "records": [
        {
            "id": f"184680840824204083{i}",
            "createTime": f"2024-10-17 15:00:1{i}",
            "updateTime": f"2024-10-28 12:45:3{i}",
            "createUser": "lee",
            "updateUser": f"F88JKT46UT{i}",
            "createUserId": f"F88JKT46UT{i}",
            "updateUserId": f"F88JKT46UT{i}",
            "isDelete": 0,
            "parentId": f"184463246386323046{i}",
            "objectName": f"AD钙_心理服务中心干预表 ({i})",
            "objectType": 2,          # 文件
            "objectPrivate": 1,       # 私人
            "objectHide": 1,          # 普通
            "fileLink": f"/test-bucket/D0001/2024/106710b5f8a5969751fce90c1b{i}.docx",
            "fileExt": "docx",
            "fileType": 4,            # 文档
            "objectFrom": 2,          # 用户上传
            "syncEs": 0,
            "ownerLevelOne": "D0001",
            "security": "内部",
            "rawUrl": f"http://172.16.12.100/file//test-bucket/D0001/2024/106710b5f8a5969751fce90c1b{i}.docx",
            "size": 14673 + i * 100,   # 假设每条记录的大小增加100字节
            "fileSize": f"{14.33 + i * 0.01:.2f} KB",
            "metadata": "{}",
            "userMetadata": "{}",
            "attr": "{}"
        }
        for i in range(10)
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "orders": [],
    "optimizeCountSql": True,
    "hitCount": False,
    "countId": None,
    "maxLimit": None,
    "searchCount": True,
    "pages": 1
}

@app.route('/disk-object/page', methods=['GET'])
def mock_search_interface():
    """模拟全文检索接口"""
    # 验证请求头授权（简化示例，仅校验格式）
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({"resCode": 401, "resMsg": "Unauthorized"}), 401

    # 解析查询参数（根据文档要求校验必填参数）
    required_params = ['documentKeywords', 'objectPrivate', 'objectType', 'pageNum', 'perPageRecordCount', 'searchType']
    for param in required_params:
        if param not in request.args:
            return jsonify({"resCode": 400, "resMsg": f"Missing required parameter: {param}"}), 400

    # 模拟业务逻辑（根据参数返回对应数据，此处直接返回固定模拟数据）
    response_data = {
        "resCode": 0,
        "resMsg": "OK",
        "data": mock_data
    }

    return jsonify(response_data), 200

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

def add_encoding_declaration(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if not content.startswith('# -*- coding: utf-8 -*-'):
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('# -*- coding: utf-8 -*-\n\n' + content)
        print(f'Added encoding declaration to {file_path}')

def process_directory(directory):
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                add_encoding_declaration(file_path)

if __name__ == '__main__':
    # 处理当前目录及其子目录下的所有Python文件
    process_directory('.') 
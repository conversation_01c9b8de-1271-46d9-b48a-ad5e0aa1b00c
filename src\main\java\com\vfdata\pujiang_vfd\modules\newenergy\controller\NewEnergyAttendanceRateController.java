package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyAttendanceRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/attendance")
@RequiredArgsConstructor
public class NewEnergyAttendanceRateController {

    private final NewEnergyAttendanceRateService attendanceRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新出勤率数据", description = "获取最新记录日期的新能源车间出勤率数据")
    public ResponseUtils.Result<NewEnergyAttendanceRateDTO> getLatestAttendanceRate() {
        try {
            NewEnergyAttendanceRateDTO rate = attendanceRateService.getLatestAttendanceRate();
            return ResponseUtils.success(rate);
        } catch (Exception e) {
            return ResponseUtils.error("获取出勤率数据失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.injection.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "停机类型分布")
public class InjectionStopTypeDTO {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "停机类型名称")
    private String stop_type_name;

    @Schema(description = "停机次数")
    private Integer type_count;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "预留字段1")
    private String reserved_field1;

    @Schema(description = "预留字段2")
    private String reserved_field2;

    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
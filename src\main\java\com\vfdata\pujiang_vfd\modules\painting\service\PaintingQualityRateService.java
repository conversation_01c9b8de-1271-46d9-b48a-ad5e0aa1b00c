package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingQualityRate;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingQualityRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingQualityRateService {

    private final PaintingQualityRateRepository qualityRateRepository;

    /**
     * 获取品质检验良率数据
     * @param workshopName 车间名称，为空时返回所有车间数据
     */
    public List<PaintingQualityRateDTO> getQualityRate(String workshopName) {
        List<PaintingQualityRate> entities;
        
        if (workshopName == null || workshopName.trim().isEmpty()) {
            // 返回所有车间的最新数据
            entities = qualityRateRepository.findLatestRecords();
        } else {
            // 返回指定车间的最新数据
            PaintingQualityRate entity = qualityRateRepository.findLatestRecordByWorkshop(workshopName.trim());
            entities = entity != null ? List.of(entity) : List.of();
        }
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingQualityRateDTO convertToDTO(PaintingQualityRate entity) {
        PaintingQualityRateDTO dto = new PaintingQualityRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

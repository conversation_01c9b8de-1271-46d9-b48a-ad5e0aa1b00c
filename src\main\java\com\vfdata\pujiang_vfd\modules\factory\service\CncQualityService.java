package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.CncQualityDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.CncQuality;
import com.vfdata.pujiang_vfd.modules.factory.repository.CncQualityRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncQualityService {
    
    private final CncQualityRepository cncQualityRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取CNC巡检检验合格率
     * 返回最新的7条数据
     */
    public List<CncQualityDTO> getCncQualityInfo() {
        List<CncQuality> entities = cncQualityRepository.findLatestByWorkshopName();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private CncQualityDTO convertToDTO(CncQuality entity) {
        CncQualityDTO dto = new CncQualityDTO();
        dto.setId(entity.getId());
        dto.setPass_rate(entity.getPassRate());
        if (entity.getRecordDate() != null) {
            dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        }
        dto.setUpdated_by(entity.getUpdatedBy());
        if (entity.getUpdatedAt() != null) {
            dto.setUpdated_at(entity.getUpdatedAt().toString());
        }
        return dto;
    }
} 
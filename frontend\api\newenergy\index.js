import request from '@/utils/request'

/**
 * 获取新能源车间人员信息
 * @returns {Promise} 返回人员信息数据
 */
export const getPersonnelInfo = async () => {
  return await request.get('/api/v1/newenergy/personnel-info/')
}

/**
 * 获取新能源车间出勤率信息
 * @returns {Promise} 返回出勤率数据
 */
export const getAttendanceRate = async () => {
  return await request.get('/api/v1/newenergy/attendance-rate/')
}

/**
 * 获取新能源车间设备信息
 * @returns {Promise} 返回设备信息数据
 */
export const getEquipmentInfo = async () => {
  return await request.get('/api/v1/newenergy/equipment-info/')
}

/**
 * 获取新能源车间状态信息
 * @returns {Promise} 返回车间状态数据
 */
export const getWorkshopStatus = async () => {
  return await request.get('/api/v1/newenergy/workshop-status/')
}

/**
 * 获取新能源车间每日达成率
 * @returns {Promise} 返回每日达成率数据
 */
export const getDailyAchievementRate = async () => {
  return await request.get('/api/v1/newenergy/daily-achievement-rate/')
}

/**
 * 获取新能源车间质量率信息
 * @returns {Promise} 返回质量率数据
 */
export const getQualityRate = async () => {
  return await request.get('/api/v1/newenergy/quality-rate/')
}

/**
 * 获取新能源车间质量率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天质量率数据
 */
export const getQualityRate7Days = async (workshopName) => {
  return await request.get('/api/v1/newenergy/quality-rate-7days/', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取新能源车间计划完成率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天计划完成率数据
 */
export const getPlanCompletionRate7Days = async (workshopName) => {
  return await request.get('/api/v1/newenergy/plan-completion-rate-7days/', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取新能源车间通过率7天数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间7天通过率数据
 */
export const getPassRate7Days = async (workshopName) => {
  return await request.get('/api/v1/newenergy/pass-rate-7days/', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 获取新能源车间测试缺陷分类数据
 * @returns {Promise} 返回测试缺陷分类数据
 */
export const getTestDefectClassification = async () => {
  return await request.get('/api/v1/newenergy/test-defect-classification/')
}

/**
 * 获取新能源车间通过率数据
 * @returns {Promise} 返回每个车间最新的通过率数据
 */
export const getPassRate = async () => {
  return await request.get('/api/v1/newenergy/pass-rate/latest')
}

/**
 * 获取新能源车间项目状态数据
 * @param {string} workshopName - 车间名称（必填）
 * @returns {Promise} 返回指定车间项目状态数据
 */
export const getProjectStatus = async (workshopName) => {
  return await request.get('/api/v1/newenergy/project-status/', {
    params: { workshop_name: workshopName }
  })
}

/**
 * 新能源车间相关API集合
 */
export default {
  // 基础信息接口
  getPersonnelInfo,
  getAttendanceRate,
  getEquipmentInfo,
  getWorkshopStatus,
  getDailyAchievementRate,
  getQualityRate,
  getPassRate,
  getTestDefectClassification,
  
  // 需要车间参数的接口
  getQualityRate7Days,
  getPlanCompletionRate7Days,
  getPassRate7Days,
  getProjectStatus
}

package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyDefectClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyDefectClassificationRepository extends JpaRepository<NewEnergyDefectClassification, Long> {
    
    /**
     * 获取所有车间最新记录日期的抽检不良分类数据
     * 当不填workshop_name参数时，返回每个workshop_name record_date最新数据
     */
    @Query("SELECT n FROM NewEnergyDefectClassification n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyDefectClassification n2 WHERE n2.workshopName = n.workshopName)")
    List<NewEnergyDefectClassification> findLatestByAllWorkshops();
    
    /**
     * 获取指定车间最新记录日期的抽检不良分类数据
     * 当填了workshop_name参数时，返回所填车间名称record_date最新数据
     */
    @Query("SELECT n FROM NewEnergyDefectClassification n WHERE n.workshopName = :workshopName AND n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyDefectClassification n2 WHERE n2.workshopName = :workshopName)")
    List<NewEnergyDefectClassification> findLatestByWorkshopName(@Param("workshopName") String workshopName);
}

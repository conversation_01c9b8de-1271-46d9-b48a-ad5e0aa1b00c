package com.vfdata.pujiang_vfd.modules.cnc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "CNC车间巡检检验合格率DTO")
public class CncQualityRateDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "当前合格率")
    private BigDecimal quality_rate;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;
}

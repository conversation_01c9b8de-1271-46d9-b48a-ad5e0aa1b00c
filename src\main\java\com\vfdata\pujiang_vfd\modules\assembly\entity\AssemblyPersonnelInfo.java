package com.vfdata.pujiang_vfd.modules.assembly.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zzcj_personnel_info")
@Schema(description = "组装车间人员信息")
public class AssemblyPersonnelInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "personnel_type", length = 20)
    @Schema(description = "人员类型")
    private String personnelType;

    @Column(name = "staff_count")
    @Schema(description = "职员人数")
    private Integer staffCount;

    @Column(name = "general_worker_count")
    @Schema(description = "一般工人人数")
    private Integer generalWorkerCount;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
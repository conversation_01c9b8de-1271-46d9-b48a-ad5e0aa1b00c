package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldWorkshopStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldWorkshopStatusRepository extends JpaRepository<MoldWorkshopStatus, Long> {

    /**
     * 获取每个车间最新记录日期的设备状况信息
     */
    @Query("SELECT m FROM MoldWorkshopStatus m WHERE m.recordDate = (SELECT MAX(m2.recordDate) FROM MoldWorkshopStatus m2)")
    List<MoldWorkshopStatus> findLatestRecords();
}

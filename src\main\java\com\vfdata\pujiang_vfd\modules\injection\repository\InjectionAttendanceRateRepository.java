package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionAttendanceRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface InjectionAttendanceRateRepository extends JpaRepository<InjectionAttendanceRate, Long> {

    @Query("SELECT a FROM InjectionAttendanceRate a WHERE a.recordDate = (SELECT MAX(a2.recordDate) FROM InjectionAttendanceRate a2)")
    Optional<InjectionAttendanceRate> findLatest();
} 
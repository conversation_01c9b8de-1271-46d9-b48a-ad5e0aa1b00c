package com.vfdata.pujiang_vfd.modules.dashboard.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_dp_hostel")
@Schema(description = "宿舍信息")
public class Hostel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "hostel_num")
    @Schema(description = "宿舍总数")
    private Integer hostelNum;

    @Column(name = "hostel_use")
    @Schema(description = "已使用数量")
    private Integer hostelUse;

    @Column(name = "hostel_remaining")
    @Schema(description = "剩余数量")
    private Integer hostelRemaining;

    @Column(name = "hostel_info", columnDefinition = "TEXT")
    @Schema(description = "宿舍信息")
    private String hostelInfo;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updateman", length = 20)
    @Schema(description = "更新人")
    private String updateman;

    @Column(name = "updatetime")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
}

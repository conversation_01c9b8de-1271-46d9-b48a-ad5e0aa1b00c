package com.vfdata.pujiang_vfd.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig {

    private final DataSyncConfig dataSyncConfig;

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(dataSyncConfig.getConnectTimeout());
        factory.setReadTimeout(dataSyncConfig.getReadTimeout());
        
        return new RestTemplate(factory);
    }
}

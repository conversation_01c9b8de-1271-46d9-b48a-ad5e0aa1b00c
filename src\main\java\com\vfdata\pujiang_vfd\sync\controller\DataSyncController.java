package com.vfdata.pujiang_vfd.sync.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.sync.scheduler.DataSyncScheduler;
import com.vfdata.pujiang_vfd.sync.service.DataSyncService;
import com.vfdata.pujiang_vfd.sync.task.DashboardSyncTask;
import com.vfdata.pujiang_vfd.sync.task.SafetySyncTask;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Tag(name = "数据同步管理接口")
@RestController
@RequestMapping("/sync")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "data-sync.enabled", havingValue = "true")
public class DataSyncController {

    private final DataSyncScheduler dataSyncScheduler;
    private final DashboardSyncTask dashboardSyncTask;
    private final SafetySyncTask safetySyncTask;

    @PostMapping("/manual")
    @Operation(summary = "手动触发数据同步", description = "立即执行一次完整的数据同步任务")
    public ResponseUtils.Result<String> manualSync() {
        try {
            log.info("收到手动同步请求");
            CompletableFuture<Void> future = dataSyncScheduler.manualSync();
            return ResponseUtils.success("数据同步任务已启动，请查看日志获取详细信息");
        } catch (Exception e) {
            log.error("手动触发数据同步失败: {}", e.getMessage(), e);
            return ResponseUtils.error("手动触发数据同步失败：" + e.getMessage());
        }
    }

    @PostMapping("/dashboard")
    @Operation(summary = "同步Dashboard模块数据", description = "仅同步Dashboard模块的数据")
    public ResponseUtils.Result<List<DataSyncService.SyncResult>> syncDashboard() {
        try {
            log.info("收到Dashboard模块同步请求");
            List<DataSyncService.SyncResult> results = dashboardSyncTask.syncAll();
            return ResponseUtils.success(results);
        } catch (Exception e) {
            log.error("Dashboard模块同步失败: {}", e.getMessage(), e);
            return ResponseUtils.error("Dashboard模块同步失败：" + e.getMessage());
        }
    }

    @PostMapping("/safety")
    @Operation(summary = "同步Safety模块数据", description = "仅同步Safety模块的数据")
    public ResponseUtils.Result<List<DataSyncService.SyncResult>> syncSafety() {
        try {
            log.info("收到Safety模块同步请求");
            List<DataSyncService.SyncResult> results = safetySyncTask.syncAll();
            return ResponseUtils.success(results);
        } catch (Exception e) {
            log.error("Safety模块同步失败: {}", e.getMessage(), e);
            return ResponseUtils.error("Safety模块同步失败：" + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "获取同步状态", description = "获取当前数据同步的状态信息")
    public ResponseUtils.Result<Map<String, Object>> getSyncStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("enabled", true);
            status.put("lastSyncTime", "通过日志查看");
            status.put("nextSyncTime", "根据定时任务配置");
            status.put("syncInterval", "10分钟（可配置）");
            
            Map<String, String> modules = new HashMap<>();
            modules.put("dashboard", "员工进出、停车场、宿舍、场所等数据");
            modules.put("safety", "访客记录、车辆记录、环境监控等数据");
            status.put("modules", modules);
            
            return ResponseUtils.success(status);
        } catch (Exception e) {
            log.error("获取同步状态失败: {}", e.getMessage(), e);
            return ResponseUtils.error("获取同步状态失败：" + e.getMessage());
        }
    }
}

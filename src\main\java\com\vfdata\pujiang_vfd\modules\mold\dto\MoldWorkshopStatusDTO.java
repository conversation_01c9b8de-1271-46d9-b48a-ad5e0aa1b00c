package com.vfdata.pujiang_vfd.modules.mold.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "模具车间设备状况DTO")
public class MoldWorkshopStatusDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "车间状态率")
    private BigDecimal workshop_rate;

    @Schema(description = "园区名称")
    private String park_name;

    @Schema(description = "园区状态率")
    private BigDecimal park_status_rate;

    @Schema(description = "设备总数")
    private Integer total_equipment_count;

    @Schema(description = "总开机数")
    private Integer operating_equipment_count;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

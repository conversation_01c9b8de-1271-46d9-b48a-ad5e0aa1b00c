package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingProjectStatus;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingProjectStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingProjectStatusService {

    private final PaintingProjectStatusRepository projectStatusRepository;

    /**
     * 获取指定车间的项目状态数据
     */
    public List<PaintingProjectStatusDTO> getProjectStatus(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<PaintingProjectStatus> entities = projectStatusRepository.findByWorkshopName(workshopName.trim());
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingProjectStatusDTO convertToDTO(PaintingProjectStatus entity) {
        PaintingProjectStatusDTO dto = new PaintingProjectStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setProject_name(entity.getProjectName());
        dto.setProduct_name(entity.getProductName());
        dto.setMachine_id(entity.getMachineId());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncQualityRate;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncQualityRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncQualityRateService {

    private final CncQualityRateRepository qualityRateRepository;

    /**
     * 获取每个车间最新的质量率信息
     */
    public List<CncQualityRateDTO> getLatestQualityRate() {
        List<CncQualityRate> qualityRates = qualityRateRepository.findLatestRecords();
        return qualityRates.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncQualityRateDTO convertToDTO(CncQualityRate entity) {
        CncQualityRateDTO dto = new CncQualityRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        return dto;
    }
}

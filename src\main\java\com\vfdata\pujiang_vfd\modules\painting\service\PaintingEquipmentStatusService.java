package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingEquipmentStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingEquipmentStatus;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingEquipmentStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingEquipmentStatusService {

    private final PaintingEquipmentStatusRepository equipmentStatusRepository;

    /**
     * 获取设备状态分布数据
     * @param workshopName 车间名称，为空时返回所有车间数据
     */
    public List<PaintingEquipmentStatusDTO> getEquipmentStatus(String workshopName) {
        List<PaintingEquipmentStatus> entities;
        
        if (workshopName == null || workshopName.trim().isEmpty()) {
            // 返回所有车间的最新数据
            entities = equipmentStatusRepository.findLatestRecords();
        } else {
            // 返回指定车间的最新数据
            entities = equipmentStatusRepository.findLatestRecordsByWorkshop(workshopName.trim());
        }
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingEquipmentStatusDTO convertToDTO(PaintingEquipmentStatus entity) {
        PaintingEquipmentStatusDTO dto = new PaintingEquipmentStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setStatus_name(entity.getStatusName());
        dto.setStatus_count(entity.getStatusCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

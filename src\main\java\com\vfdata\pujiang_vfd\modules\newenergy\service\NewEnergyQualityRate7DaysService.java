package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyQualityRate7Days;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyQualityRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyQualityRate7DaysService {

    private final NewEnergyQualityRate7DaysRepository qualityRate7DaysRepository;

    /**
     * 根据车间名称获取最新7条抽检检验合格率数据
     */
    public List<NewEnergyQualityRate7DaysDTO> getQualityRate7DaysByWorkshop(String workshopName) {
        List<NewEnergyQualityRate7Days> qualityRateList = qualityRate7DaysRepository.findTop7ByWorkshopNameOrderByRecordDateDesc(workshopName);
        
        return qualityRateList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyQualityRate7DaysDTO convertToDTO(NewEnergyQualityRate7Days entity) {
        NewEnergyQualityRate7DaysDTO dto = new NewEnergyQualityRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

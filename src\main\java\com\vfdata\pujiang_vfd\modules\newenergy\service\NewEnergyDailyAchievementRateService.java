package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyDailyAchievementRate;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyDailyAchievementRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyDailyAchievementRateService {

    private final NewEnergyDailyAchievementRateRepository dailyAchievementRateRepository;

    /**
     * 获取每个车间最新记录日期的日计划达成率数据
     */
    public List<NewEnergyDailyAchievementRateDTO> getLatestDailyAchievementRate() {
        List<NewEnergyDailyAchievementRate> achievementRateList = dailyAchievementRateRepository.findLatestByEachWorkshop();
        
        return achievementRateList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyDailyAchievementRateDTO convertToDTO(NewEnergyDailyAchievementRate entity) {
        NewEnergyDailyAchievementRateDTO dto = new NewEnergyDailyAchievementRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setAchievement_rate(entity.getAchievementRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

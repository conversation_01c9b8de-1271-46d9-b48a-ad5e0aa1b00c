package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.ShipmentTrendDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.ShipmentTrend;
import com.vfdata.pujiang_vfd.modules.factory.repository.ShipmentTrendRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ShipmentTrendService {

    private final ShipmentTrendRepository shipmentTrendRepository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS");

    /**
     * 获取发货趋势信息
     * 返回最新的12条数据
     */
    public List<ShipmentTrendDTO> getShipmentTrendInfo() {
        List<ShipmentTrend> trendList = shipmentTrendRepository.findLatest();
        return trendList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private ShipmentTrendDTO convertToDTO(ShipmentTrend entity) {
        ShipmentTrendDTO dto = new ShipmentTrendDTO();
        dto.setId(entity.getId());
        dto.setShipment_count(entity.getShipmentCount());
        dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt().format(DATETIME_FORMATTER));
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
from typing import Generic, TypeVar, Optional, List, Any
from pydantic import BaseModel
from datetime import datetime

T = TypeVar('T')

class ResponseBase(BaseModel):
    """
    基础响应模型
    """
    code: int = 200  # 响应状态码
    message: str = "操作成功"  # 响应消息
    success: bool = True  # 是否成功
    timestamp: datetime = datetime.now()  # 响应时间戳

class Response(ResponseBase, Generic[T]):
    """
    通用响应模型
    """
    data: Optional[T] = None  # 响应数据

class PageInfo(BaseModel):
    """
    分页信息
    """
    page: int  # 当前页码
    size: int  # 每页大小
    total: int  # 总记录数
    pages: int  # 总页数

class PageResponse(Response[T], Generic[T]):
    """
    分页响应模型
    """
    page_info: Optional[PageInfo] = None  # 分页信息

class ListResponse(Response[List[T]], Generic[T]):
    """
    列表响应模型
    """
    total: int = 0  # 总记录数

class ErrorResponse(ResponseBase):
    """
    错误响应模型
    """
    code: int = 500  # 错误状态码
    message: str = "操作失败"  # 错误消息
    success: bool = False  # 是否成功
    detail: Optional[str] = None  # 错误详情 
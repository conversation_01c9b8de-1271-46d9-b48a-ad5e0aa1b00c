package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPlanCompletionRate7Days;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyPlanCompletionRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyPlanCompletionRate7DaysService {

    private final NewEnergyPlanCompletionRate7DaysRepository planCompletionRate7DaysRepository;

    /**
     * 根据车间名称获取最新7条计划达成率数据
     */
    public List<NewEnergyPlanCompletionRate7DaysDTO> getPlanCompletionRate7DaysByWorkshop(String workshopName) {
        List<NewEnergyPlanCompletionRate7Days> planCompletionRateList = planCompletionRate7DaysRepository.findTop7ByWorkshopNameOrderByRecordDateDesc(workshopName);
        
        return planCompletionRateList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyPlanCompletionRate7DaysDTO convertToDTO(NewEnergyPlanCompletionRate7Days entity) {
        NewEnergyPlanCompletionRate7DaysDTO dto = new NewEnergyPlanCompletionRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setCompletion_rate(entity.getCompletionRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingDailyAchievementRate;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingDailyAchievementRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingDailyAchievementRateService {

    private final PaintingDailyAchievementRateRepository dailyAchievementRateRepository;

    /**
     * 获取当日计划达成率数据
     * @param workshopName 车间名称，为空时返回所有车间数据
     */
    public List<PaintingDailyAchievementRateDTO> getDailyAchievementRate(String workshopName) {
        List<PaintingDailyAchievementRate> entities;
        
        if (workshopName == null || workshopName.trim().isEmpty()) {
            // 返回所有车间的最新数据
            entities = dailyAchievementRateRepository.findLatestRecords();
        } else {
            // 返回指定车间的最新数据
            PaintingDailyAchievementRate entity = dailyAchievementRateRepository.findLatestRecordByWorkshop(workshopName.trim());
            entities = entity != null ? List.of(entity) : List.of();
        }
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingDailyAchievementRateDTO convertToDTO(PaintingDailyAchievementRate entity) {
        PaintingDailyAchievementRateDTO dto = new PaintingDailyAchievementRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setPlanned_quantity(entity.getPlannedQuantity());
        dto.setActual_quantity(entity.getActualQuantity());
        dto.setAchievement_rate(entity.getAchievementRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.assembly.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class AssemblyEquipmentInfoDTO {
    private Long id;
    private Integer total_equipment_count;
    private Integer operating_equipment_count;
    private BigDecimal equipment_overall_efficiency;
    private BigDecimal operating_rate;
    private LocalDate record_date;
    private String updated_by;
    private LocalDateTime updated_at;
    private String reserved_field1;
    private String reserved_field2;
    private String reserved_field3;
} 
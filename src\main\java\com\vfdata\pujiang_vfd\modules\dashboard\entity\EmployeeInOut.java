package com.vfdata.pujiang_vfd.modules.dashboard.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_dp_employeeinout")
@Schema(description = "员工进出记录")
public class EmployeeInOut {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "employee_name", length = 255)
    @Schema(description = "员工姓名")
    private String employeeName;

    @Column(name = "employee_addr", length = 255)
    @Schema(description = "员工地址")
    private String employeeAddr;

    @Column(name = "employee_state", length = 255)
    @Schema(description = "员工状态")
    private String employeeState;

    @Column(name = "record_date", columnDefinition = "TIMESTAMP")
    @Schema(description = "记录时间")
    private LocalDateTime recordDate;

    @Column(name = "updateman", length = 20)
    @Schema(description = "更新人")
    private String updateman;

    @Column(name = "updatetime", columnDefinition = "TIMESTAMP")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
}

package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "技术人员信息DTO")
public class TechnicalDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "人员类型")
    private String personnel_type;

    @Schema(description = "人员数量")
    private Integer personnel_num;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

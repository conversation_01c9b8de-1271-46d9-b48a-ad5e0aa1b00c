package com.vfdata.pujiang_vfd.modules.mold.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "模具车间模具进度DTO")
public class MoldProgressDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "模具号")
    private String mold_number;

    @Schema(description = "编程交期")
    private LocalDate processing_leadtime;

    @Schema(description = "加工交期")
    private LocalDate record_date;

    @Schema(description = "品质交期")
    private LocalDate complete_date;

    @Schema(description = "装配交期")
    private LocalDate assembly_delivery_time;

    @Schema(description = "备用字段1")
    private String current_process;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingQualityRate7Days;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingQualityRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingQualityRate7DaysService {

    private final PaintingQualityRate7DaysRepository qualityRate7DaysRepository;

    /**
     * 获取指定车间的品质检验良率近7天数据
     */
    public List<PaintingQualityRate7DaysDTO> getQualityRate7Days(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<PaintingQualityRate7Days> entities = qualityRate7DaysRepository.findLatest7RecordsByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingQualityRate7DaysDTO convertToDTO(PaintingQualityRate7Days entity) {
        PaintingQualityRate7DaysDTO dto = new PaintingQualityRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

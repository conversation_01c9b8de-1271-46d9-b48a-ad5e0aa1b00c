version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
    networks:
      - pujiang-network

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=dpbia
      - POSTGRES_USER=dpbia
      - POSTGRES_PASSWORD=8ujshjsuwns#sUt
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - pujiang-network

networks:
  pujiang-network:
    driver: bridge

volumes:
  postgres-data: 
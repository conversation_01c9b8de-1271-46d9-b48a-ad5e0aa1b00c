package com.vfdata.pujiang_vfd.modules.safety.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_szhaq_wastewater_monitoring")
@Schema(description = "废水监控")
public class WastewaterMonitoring {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "cod", precision = 10, scale = 2)
    @Schema(description = "COD值")
    private BigDecimal cod;

    @Column(name = "ammonia_nitrogen", precision = 10, scale = 2)
    @Schema(description = "氨氮值")
    @JsonProperty("ammonia_nitrogen")
    private BigDecimal ammoniaNitrogen;

    @Column(name = "record_time")
    @Schema(description = "记录时间")
    @JsonProperty("record_time")
    private LocalDateTime recordTime;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    @JsonProperty("updated_by")
    private String updatedBy;

    @Column(name = "update_date")
    @Schema(description = "更新日期")
    @JsonProperty("update_date")
    private LocalDate updateDate;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    @JsonProperty("reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    @JsonProperty("reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    @JsonProperty("reserved_field3")
    private String reservedField3;
}

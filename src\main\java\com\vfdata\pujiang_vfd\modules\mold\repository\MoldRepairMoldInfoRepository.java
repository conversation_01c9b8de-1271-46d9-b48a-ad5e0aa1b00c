package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldRepairMoldInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldRepairMoldInfoRepository extends JpaRepository<MoldRepairMoldInfo, Long> {

    /**
     * 获取修模信息数据，根据period参数返回不同数量
     * @param period 周期类型：month返回6条，day返回7条
     */
    @Query("SELECT m FROM MoldRepairMoldInfo m WHERE m.period = :period ORDER BY m.recordDate DESC LIMIT :limit")
    List<MoldRepairMoldInfo> findLatestRecordsByPeriod(@Param("period") String period, @Param("limit") int limit);
}

package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.FactoryEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.FactoryEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.factory.repository.FactoryEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FactoryEquipmentInfoService {

    private final FactoryEquipmentInfoRepository factoryEquipmentInfoRepository;

    /**
     * 获取最新的设备信息
     */
    public List<FactoryEquipmentInfoDTO> getLatestEquipmentInfo() {
        List<FactoryEquipmentInfo> latestRecords = factoryEquipmentInfoRepository.findLatestRecords();

        // 如果没有记录，返回空列表而不是抛出异常，与Python实现保持一致
        return latestRecords.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private FactoryEquipmentInfoDTO convertToDTO(FactoryEquipmentInfo entity) {
        FactoryEquipmentInfoDTO dto = new FactoryEquipmentInfoDTO();
        dto.setId(entity.getId());
        dto.setTotal_equipment_count(entity.getTotalEquipmentCount());
        dto.setOperating_equipment_count(entity.getOperatingEquipmentCount());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
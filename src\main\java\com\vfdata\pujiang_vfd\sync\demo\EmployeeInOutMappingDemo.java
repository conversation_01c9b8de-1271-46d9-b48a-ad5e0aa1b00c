package com.vfdata.pujiang_vfd.sync.demo;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.EmployeeInOut;
import com.vfdata.pujiang_vfd.sync.mapper.dashboard.EmployeeInOutMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 员工进出记录映射演示
 * 展示修复后的时间保存功能
 */
@Slf4j
public class EmployeeInOutMappingDemo {

    public static void main(String[] args) {
        log.info("=== 员工进出记录时间映射演示 ===");
        
        EmployeeInOutMapper mapper = new EmployeeInOutMapper();
        
        // 模拟原始API返回的数据
        Map<String, Object> originalData = new HashMap<>();
        originalData.put("id", 1655014L);
        originalData.put("employeeName", "龚武");
        originalData.put("employeeAddr", "5号宿舍闸机右3出");
        originalData.put("employeeState", "出");
        originalData.put("recordDate", "2025-06-23 00:00:13");  // 原始完整时间
        originalData.put("updateman", null);
        originalData.put("updatetime", null);
        originalData.put("reservedField1", null);
        originalData.put("reservedField2", null);
        originalData.put("reservedField3", null);
        
        log.info("原始API数据: {}", originalData);
        
        // 映射为实体对象
        EmployeeInOut entity = mapper.mapToEntity(originalData);
        
        log.info("映射后的实体对象:");
        log.info("  ID: {}", entity.getId());
        log.info("  员工姓名: {}", entity.getEmployeeName());
        log.info("  员工地址: {}", entity.getEmployeeAddr());
        log.info("  员工状态: {}", entity.getEmployeeState());
        log.info("  记录时间: {}", entity.getRecordDate());  // 现在保存完整时间
        log.info("  更新人: {}", entity.getUpdateman());
        log.info("  更新时间: {}", entity.getUpdatetime());
        
        // 验证时间保存
        if (entity.getRecordDate() != null) {
            log.info("✅ 成功保存完整的原始时间: {}", entity.getRecordDate());
            log.info("   - 年: {}", entity.getRecordDate().getYear());
            log.info("   - 月: {}", entity.getRecordDate().getMonthValue());
            log.info("   - 日: {}", entity.getRecordDate().getDayOfMonth());
            log.info("   - 时: {}", entity.getRecordDate().getHour());
            log.info("   - 分: {}", entity.getRecordDate().getMinute());
            log.info("   - 秒: {}", entity.getRecordDate().getSecond());
        } else {
            log.error("❌ 时间信息丢失");
        }
        
        log.info("=== 演示完成 ===");
    }
}

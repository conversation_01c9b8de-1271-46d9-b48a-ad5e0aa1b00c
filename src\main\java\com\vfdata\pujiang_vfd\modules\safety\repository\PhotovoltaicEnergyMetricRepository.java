package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.PhotovoltaicEnergyMetric;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface PhotovoltaicEnergyMetricRepository extends JpaRepository<PhotovoltaicEnergyMetric, Long> {
    
    List<PhotovoltaicEnergyMetric> findAllByOrderByRecordTimeDesc();
}

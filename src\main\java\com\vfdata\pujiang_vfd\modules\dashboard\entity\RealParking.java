package com.vfdata.pujiang_vfd.modules.dashboard.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_dp_realparking")
@Schema(description = "实时停车信息")
public class RealParking {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "parking_addr", length = 255)
    @Schema(description = "停车场地址")
    private String parkingAddr;

    @Column(name = "parking_num")
    @Schema(description = "停车位总数")
    private Integer parkingNum;

    @Column(name = "parking_use")
    @Schema(description = "已使用数量")
    private Integer parkingUse;

    @Column(name = "parking_remaining")
    @Schema(description = "剩余数量")
    private Integer parkingRemaining;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updateman", length = 20)
    @Schema(description = "更新人")
    private String updateman;

    @Column(name = "updatetime")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
}

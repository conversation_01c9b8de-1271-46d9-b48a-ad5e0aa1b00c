package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncCuttingToolUsage7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncCuttingToolUsage7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/cuttingtool-usage-7days")
@RequiredArgsConstructor
public class CncCuttingToolUsage7DaysController {

    private final CncCuttingToolUsage7DaysService cuttingToolUsage7DaysService;

    @GetMapping
    @Operation(summary = "获取CNC车间刀具用量近7天数据", description = "根据车间名称获取指定CNC车间刀具用量近7天数据")
    public ResponseUtils.Result<List<CncCuttingToolUsage7DaysDTO>> getCuttingToolUsage7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<CncCuttingToolUsage7DaysDTO> dataList = cuttingToolUsage7DaysService.getCuttingToolUsage7Days(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间刀具用量7天数据失败：" + e.getMessage());
        }
    }
}

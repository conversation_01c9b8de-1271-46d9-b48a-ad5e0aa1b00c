package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingQualityRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/quality-rate-7days")
@RequiredArgsConstructor
public class PaintingQualityRate7DaysController {

    private final PaintingQualityRate7DaysService qualityRate7DaysService;

    @GetMapping
    @Operation(summary = "获取喷涂车间品质检验良率近7天数据", description = "根据车间名称获取指定喷涂车间品质检验良率近7天数据")
    public ResponseUtils.Result<List<PaintingQualityRate7DaysDTO>> getQualityRate7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<PaintingQualityRate7DaysDTO> dataList = qualityRate7DaysService.getQualityRate7Days(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间品质检验良率7天数据失败：" + e.getMessage());
        }
    }
}

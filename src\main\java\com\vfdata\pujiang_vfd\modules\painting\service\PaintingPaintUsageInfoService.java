package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingPaintUsageInfoDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingPaintUsageInfo;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingPaintUsageInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingPaintUsageInfoService {

    private final PaintingPaintUsageInfoRepository paintUsageInfoRepository;

    /**
     * 获取油漆用量信息数据
     * @param workshopName 车间名称，为空时返回所有车间数据
     */
    public List<PaintingPaintUsageInfoDTO> getPaintUsageInfo(String workshopName) {
        List<PaintingPaintUsageInfo> entities;
        
        if (workshopName == null || workshopName.trim().isEmpty()) {
            // 返回所有车间的最新数据
            entities = paintUsageInfoRepository.findLatestRecords();
        } else {
            // 返回指定车间的最新数据
            PaintingPaintUsageInfo entity = paintUsageInfoRepository.findLatestRecordByWorkshop(workshopName.trim());
            entities = entity != null ? List.of(entity) : List.of();
        }
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingPaintUsageInfoDTO convertToDTO(PaintingPaintUsageInfo entity) {
        PaintingPaintUsageInfoDTO dto = new PaintingPaintUsageInfoDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setPaint_usage(entity.getPaintUsage());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

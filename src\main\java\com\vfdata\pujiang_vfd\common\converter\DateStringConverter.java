package com.vfdata.pujiang_vfd.common.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Converter(autoApply = true)
public class DateStringConverter implements AttributeConverter<LocalDate, String> {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YEAR_FORMATTER = DateTimeFormatter.ofPattern("yyyy");

    @Override
    public String convertToDatabaseColumn(LocalDate attribute) {
        if (attribute == null) {
            return null;
        }
        // 如果日期是1月1日，则只返回年份
        if (attribute.getMonthValue() == 1 && attribute.getDayOfMonth() == 1) {
            return attribute.format(YEAR_FORMATTER);
        }
        // 否则返回完整日期
        return attribute.format(DATE_FORMATTER);
    }

    @Override
    public LocalDate convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        try {
            // 尝试解析完整日期格式
            return LocalDate.parse(dbData, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            try {
                // 尝试解析年份格式
                return LocalDate.parse(dbData + "-01-01", DATE_FORMATTER);
            } catch (DateTimeParseException ex) {
                return null;
            }
        }
    }
} 
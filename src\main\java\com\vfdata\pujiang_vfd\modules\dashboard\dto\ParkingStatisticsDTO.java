package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "停车场统计DTO")
public class ParkingStatisticsDTO {

    @Schema(description = "停车场地址")
    private String parking_addr;

    @Schema(description = "总车位数")
    private Integer total_parking;

    @Schema(description = "已使用车位数")
    private Integer total_used;

    @Schema(description = "剩余车位数")
    private Integer total_remaining;

    @Schema(description = "使用率（百分比）")
    private Double usage_rate;
}

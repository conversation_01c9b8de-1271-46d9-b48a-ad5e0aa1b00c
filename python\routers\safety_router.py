from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Dict
from database import get_db
from models import safety_models
from schemas import safety_schemas
from schemas.common_schemas import Response, ListResponse, ErrorResponse
from datetime import datetime, date, timedelta

router = APIRouter(
    prefix="/safety",
    tags=["安全管理"],
    responses={
        200: {"description": "操作成功"},
        404: {"description": "未找到数据", "model": ErrorResponse},
        500: {"description": "服务器内部错误", "model": ErrorResponse}
    }
)

# 访客记录相关接口
@router.post("/visitor-records/", response_model=Response[safety_schemas.VisitorRecord],
    summary="创建访客记录",
    description="创建新的访客记录",
    response_description="返回创建的访客记录")
def create_visitor_record(record: safety_schemas.VisitorRecordCreate, db: Session = Depends(get_db)):
    """
    创建访客记录
    
    参数说明：
    - **visitor_name**: 访客姓名
    - **channel_name**: 通道名称
    - **visit_time**: 访问时间
    - **status**: 状态
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_record = safety_models.VisitorRecord(**record.dict())
        db.add(db_record)
        db.commit()
        db.refresh(db_record)
        return Response(data=db_record)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/visitor-records/", response_model=ListResponse[safety_schemas.VisitorRecord],
    summary="获取访客记录列表",
    description="获取所有访客记录",
    response_description="返回访客记录列表")
def read_visitor_records(db: Session = Depends(get_db)):
    """获取所有访客记录"""
    result = db.query(safety_models.VisitorRecord).order_by(
        safety_models.VisitorRecord.visit_time.desc()
    ).limit(15).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 车辆记录相关接口
@router.post("/vehicle-records/", response_model=Response[safety_schemas.VehicleRecord],
    summary="创建车辆记录",
    description="创建新的车辆进出记录",
    response_description="返回创建的车辆记录")
def create_vehicle_record(record: safety_schemas.VehicleRecordCreate, db: Session = Depends(get_db)):
    """
    创建车辆记录
    
    参数说明：
    - **license_plate**: 车牌号
    - **channel_name**: 通道名称
    - **entry_time**: 进入时间
    - **status**: 状态
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_record = safety_models.VehicleRecord(**record.dict())
        db.add(db_record)
        db.commit()
        db.refresh(db_record)
        return Response(data=db_record)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/vehicle-records/", response_model=ListResponse[safety_schemas.VehicleRecord],
    summary="获取车辆记录列表",
    description="获取所有车辆进出记录",
    response_description="返回车辆记录列表")
def read_vehicle_records(db: Session = Depends(get_db)):
    """获取所有车辆进出记录"""
    result = db.query(safety_models.VehicleRecord).order_by(
        safety_models.VehicleRecord.entry_time.desc()
    ).limit(15).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 废水监控相关接口
@router.post("/wastewater-monitoring/", response_model=Response[safety_schemas.WastewaterMonitoring],
    summary="创建废水监控记录",
    description="创建新的废水监控记录",
    response_description="返回创建的废水监控记录")
def create_wastewater_monitoring(monitoring: safety_schemas.WastewaterMonitoringCreate, db: Session = Depends(get_db)):
    """
    创建废水监控记录
    
    参数说明：
    - **cod**: COD值
    - **ammonia_nitrogen**: 氨氮值
    - **record_time**: 记录时间
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_monitoring = safety_models.WastewaterMonitoring(**monitoring.dict())
        db.add(db_monitoring)
        db.commit()
        db.refresh(db_monitoring)
        return Response(data=db_monitoring)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/wastewater-monitoring/", response_model=ListResponse[safety_schemas.WastewaterMonitoring],
    summary="获取废水监控记录列表",
    description="获取所有废水监控记录",
    response_description="返回废水监控记录列表")
def read_wastewater_monitoring(db: Session = Depends(get_db)):
    """获取所有废水监控记录"""
    result = db.query(safety_models.WastewaterMonitoring).order_by(
        safety_models.WastewaterMonitoring.record_time.desc()
    ).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 消防设备相关接口
@router.post("/fire-equipment/", response_model=Response[safety_schemas.FireEquipmentInfo],
    summary="创建消防设备记录",
    description="创建新的消防设备记录",
    response_description="返回创建的消防设备记录")
def create_fire_equipment(equipment: safety_schemas.FireEquipmentInfoCreate, db: Session = Depends(get_db)):
    """
    创建消防设备记录
    
    参数说明：
    - **fire_extinguisher**: 灭火器状态
    - **fire_sprinkler**: 喷淋系统状态
    - **fire_hydrant**: 消防栓状态
    - **fire_alarm_device**: 火警报警器状态
    - **record_date**: 记录时间
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_equipment = safety_models.FireEquipmentInfo(**equipment.dict())
        db.add(db_equipment)
        db.commit()
        db.refresh(db_equipment)
        return Response(data=db_equipment)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/fire-equipment/", response_model=ListResponse[safety_schemas.FireEquipmentInfo],
    summary="获取消防设备列表",
    description="获取所有消防设备记录",
    response_description="返回消防设备列表")
def read_fire_equipment(db: Session = Depends(get_db)):
    """获取所有消防设备列表"""
    result = db.query(safety_models.FireEquipmentInfo).order_by(
        safety_models.FireEquipmentInfo.record_date.desc()
    ).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 能耗统计相关接口
@router.post("/energy-consumption/", response_model=Response[safety_schemas.EnergyConsumptionRecord],
    summary="创建能耗统计记录",
    description="创建新的能耗统计记录",
    response_description="返回创建的能耗统计记录")
def create_energy_consumption(consumption: safety_schemas.EnergyConsumptionRecordCreate, db: Session = Depends(get_db)):
    """
    创建能耗统计记录
    
    参数说明：
    - **water_usage**: 水用量
    - **electricity_usage**: 电用量
    - **gas_usage**: 气用量
    - **record_time**: 记录时间
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_consumption = safety_models.EnergyConsumptionRecord(**consumption.dict())
        db.add(db_consumption)
        db.commit()
        db.refresh(db_consumption)
        return Response(data=db_consumption)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/energy-consumption/", response_model=ListResponse[safety_schemas.EnergyConsumptionRecord],
    summary="获取能耗统计记录列表",
    description="获取所有能耗统计记录",
    response_description="返回能耗统计记录列表")
def read_energy_consumption(db: Session = Depends(get_db)):
    """获取所有能耗统计记录"""
    result = db.query(safety_models.EnergyConsumptionRecord).order_by(
        safety_models.EnergyConsumptionRecord.record_time.desc()
    ).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 实际能耗统计相关接口
@router.post("/actual-energy-consumption/", response_model=Response[safety_schemas.ActualEnergyConsumption],
    summary="创建实际能耗统计记录",
    description="创建新的实际能耗统计记录",
    response_description="返回创建的实际能耗统计记录")
def create_actual_energy_consumption(consumption: safety_schemas.ActualEnergyConsumptionCreate, db: Session = Depends(get_db)):
    """
    创建实际能耗统计记录
    
    参数说明：
    - **water_usage**: 水使用量
    - **electricity_usage**: 电使用量
    - **gas_usage**: 气使用量
    - **record_time**: 记录时间
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_consumption = safety_models.ActualEnergyConsumption(**consumption.dict())
        db.add(db_consumption)
        db.commit()
        db.refresh(db_consumption)
        return Response(data=db_consumption)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/actual-energy-consumption/", response_model=ListResponse[safety_schemas.ActualEnergyConsumption],
    summary="获取实际能耗统计记录列表",
    description="获取所有实际能耗统计记录",
    response_description="返回实际能耗统计记录列表")
def read_actual_energy_consumption(db: Session = Depends(get_db)):
    """获取所有实际能耗统计记录"""
    result = db.query(safety_models.ActualEnergyConsumption).order_by(
        safety_models.ActualEnergyConsumption.record_time.desc()
    ).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 光伏能源指标相关接口
@router.post("/photovoltaic-energy-metrics/", response_model=Response[safety_schemas.PhotovoltaicEnergyMetric],
    summary="创建光伏能源指标记录",
    description="创建新的光伏能源指标记录",
    response_description="返回创建的光伏能源指标记录")
def create_photovoltaic_energy_metric(metric: safety_schemas.PhotovoltaicEnergyMetricCreate, db: Session = Depends(get_db)):
    """
    创建光伏能源指标记录
    
    参数说明：
    - **daily_generation**: 今日发电量
    - **total_generation**: 历史总发电量
    - **daily_co2_reduction**: 日减少CO2排放量
    - **record_time**: 记录时间
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_metric = safety_models.PhotovoltaicEnergyMetric(**metric.dict())
        db.add(db_metric)
        db.commit()
        db.refresh(db_metric)
        return Response(data=db_metric)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/photovoltaic-energy-metrics/", response_model=ListResponse[safety_schemas.PhotovoltaicEnergyMetric],
    summary="获取光伏能源指标记录列表",
    description="获取所有光伏能源指标记录",
    response_description="返回光伏能源指标记录列表")
def read_photovoltaic_energy_metrics(db: Session = Depends(get_db)):
    """获取所有光伏能源指标记录"""
    result = db.query(safety_models.PhotovoltaicEnergyMetric).order_by(
        safety_models.PhotovoltaicEnergyMetric.record_time.desc()
    ).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 光伏能源统计相关接口
@router.post("/photovoltaic-energy-stats/", response_model=Response[safety_schemas.PhotovoltaicEnergyStat],
    summary="创建光伏能源统计记录",
    description="创建新的光伏能源统计记录",
    response_description="返回创建的光伏能源统计记录")
def create_photovoltaic_energy_stat(stat: safety_schemas.PhotovoltaicEnergyStatCreate, db: Session = Depends(get_db)):
    """
    创建光伏能源统计记录
    
    参数说明：
    - **daily_generation**: 日发电量
    - **record_time**: 记录时间
    - **updated_by**: 更新人
    - **update_date**: 更新日期
    """
    try:
        db_stat = safety_models.PhotovoltaicEnergyStat(**stat.dict())
        db.add(db_stat)
        db.commit()
        db.refresh(db_stat)
        return Response(data=db_stat)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/photovoltaic-energy-stats/", response_model=ListResponse[safety_schemas.PhotovoltaicEnergyStat],
    summary="获取光伏能源统计记录列表",
    description="获取所有光伏能源统计记录",
    response_description="返回光伏能源统计记录列表")
def read_photovoltaic_energy_stats(db: Session = Depends(get_db)):
    """获取所有光伏能源统计记录"""
    result = db.query(safety_models.PhotovoltaicEnergyStat).order_by(
        safety_models.PhotovoltaicEnergyStat.record_time.desc()
    ).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    ) 
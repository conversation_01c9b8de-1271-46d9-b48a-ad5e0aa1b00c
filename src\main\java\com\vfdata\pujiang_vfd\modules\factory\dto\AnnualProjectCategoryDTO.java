package com.vfdata.pujiang_vfd.modules.factory.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "年度项目分类信息DTO")
public class AnnualProjectCategoryDTO {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "项目名称")
    private String project_name;

    @Schema(description = "项目数量")
    private Integer project_count;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private String updated_at;

    @Schema(description = "预留字段1")
    private String reserved_field1;

    @Schema(description = "预留字段2")
    private String reserved_field2;

    @Schema(description = "预留字段3")
    private String reserved_field3;
} 
package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyEnvironmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface AssemblyEnvironmentInfoRepository extends JpaRepository<AssemblyEnvironmentInfo, Long> {
    
    @Query("SELECT e FROM AssemblyEnvironmentInfo e WHERE e.recordDate = (SELECT MAX(e2.recordDate) FROM AssemblyEnvironmentInfo e2)")
    Optional<AssemblyEnvironmentInfo> findLatest();
} 
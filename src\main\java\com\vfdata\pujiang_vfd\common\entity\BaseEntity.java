package com.vfdata.pujiang_vfd.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vfdata.pujiang_vfd.common.converter.DateStringConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 基础实体类
 */
@MappedSuperclass
@Getter
@Setter
public abstract class BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "记录日期")
    @Column(name = "record_date")
    @Convert(converter = DateStringConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate recordDate;

    @Schema(description = "更新人")
    @Column(name = "updated_by")
    private String updatedBy;

    @Schema(description = "更新时间")
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Schema(description = "备用字段1")
    @Column(name = "reserved_field1")
    private String reservedField1;

    @Schema(description = "备用字段2")
    @Column(name = "reserved_field2")
    private String reservedField2;

    @Schema(description = "备用字段3")
    @Column(name = "reserved_field3")
    private String reservedField3;
} 
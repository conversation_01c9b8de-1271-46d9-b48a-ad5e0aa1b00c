package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyQualityRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/quality-rate-7days")
@RequiredArgsConstructor
public class NewEnergyQualityRate7DaysController {

    private final NewEnergyQualityRate7DaysService qualityRate7DaysService;

    @GetMapping
    @Operation(summary = "获取抽检检验合格率近7天数据", description = "根据车间名称获取该车间最新7条抽检检验合格率数据")
    public ResponseUtils.Result<List<NewEnergyQualityRate7DaysDTO>> getQualityRate7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam("workshop_name") String workshopName) {
        try {
            if (workshopName == null || workshopName.trim().isEmpty()) {
                return ResponseUtils.error("车间名称不能为空");
            }
            
            List<NewEnergyQualityRate7DaysDTO> data = qualityRate7DaysService.getQualityRate7DaysByWorkshop(workshopName.trim());
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取抽检检验合格率近7天数据失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "ioc_qc_notification_info")
@EqualsAndHashCode(callSuper = true)
public class FactoryNotification extends BaseEntity {
    
    @Column(name = "content", length = 500)
    private String notificationContent;
} 
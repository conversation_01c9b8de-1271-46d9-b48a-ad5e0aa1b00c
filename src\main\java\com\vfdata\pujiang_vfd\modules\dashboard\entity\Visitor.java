package com.vfdata.pujiang_vfd.modules.dashboard.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_dp_visitor")
@Schema(description = "访客信息")
public class Visitor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "visitor_type", length = 255)
    @Schema(description = "类型（在园员工、离园员工、访客数）")
    @JsonProperty("visitor_type")
    private String visitorType;

    @Column(name = "visitor_date")
    @Schema(description = "访客日期")
    @JsonProperty("visitor_date")
    private LocalDateTime visitorDate;

    @Column(name = "visitor_num")
    @Schema(description = "人数")
    @JsonProperty("visitor_num")
    private Integer visitorNum;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    @JsonProperty("record_date")
    private LocalDate recordDate;

    @Column(name = "updateman", length = 20)
    @Schema(description = "更新人")
    private String updateman;

    @Column(name = "updatetime")
    @Schema(description = "更新时间")
    private LocalDateTime updatetime;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "供应商数量")
    @JsonProperty("reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "客户数量")
    @JsonProperty("reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "其他访客数量")
    @JsonProperty("reserved_field3")
    private String reservedField3;
}

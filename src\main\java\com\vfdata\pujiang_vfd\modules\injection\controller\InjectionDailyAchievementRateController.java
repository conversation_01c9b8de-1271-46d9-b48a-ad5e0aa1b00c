package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionDailyAchievementRateService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.common.util.ParamCheckUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "注塑车间页面接口")
@RestController
@RequestMapping("/injection/achievement-rate")
@RequiredArgsConstructor
public class InjectionDailyAchievementRateController {

    private final InjectionDailyAchievementRateService injectionDailyAchievementRateService;

    @GetMapping("/today")
    @Operation(summary = "获取各车间当天达成率")
    public ResponseUtils.Result<List<InjectionDailyAchievementRateDTO>> getTodayAchievementRate() {
        return ResponseUtils.success(injectionDailyAchievementRateService.getTodayAchievementRate());
    }

    @GetMapping("/week")
    @Operation(summary = "获取指定车间最近7条达成率")
    public ResponseUtils.Result<List<InjectionPlanCompletionRate7DaysDTO>> getWeekAchievementRateByWorkshop(
            @RequestParam(required = false) String workshop_name) {
        String errMsg = ParamCheckUtils.checkRequired(workshop_name, "车间名称(workshop_name)");
        if (errMsg != null) {
            return ResponseUtils.error(errMsg);
        }
        return ResponseUtils.success(injectionDailyAchievementRateService.getWeekAchievementRateByWorkshop(workshop_name));
    }
} 




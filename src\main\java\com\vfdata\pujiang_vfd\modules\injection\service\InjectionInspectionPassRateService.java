package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionInspectionPassRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionInspectionPassRate;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionInspectionPassRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionInspectionPassRateService {

    private final InjectionInspectionPassRateRepository injectionInspectionPassRateRepository;

    public List<InjectionInspectionPassRateDTO> getPassRates(String workshopName) {
        List<InjectionInspectionPassRate> rateList;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            rateList = injectionInspectionPassRateRepository.findByWorkshopName(workshopName);
            if (rateList.isEmpty()) {
                return null;
            }
        } else {
            rateList = injectionInspectionPassRateRepository.findAllPassRates();
        }
        
        return rateList.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    private InjectionInspectionPassRateDTO convertToDTO(InjectionInspectionPassRate rate) {
        InjectionInspectionPassRateDTO dto = new InjectionInspectionPassRateDTO();
        dto.setId(rate.getId());
        dto.setWorkshop_name(rate.getWorkshopName());
        dto.setPass_rate(rate.getPassRate());
        dto.setRecord_date(rate.getRecordDate());
        dto.setUpdated_by(rate.getUpdatedBy());
        dto.setUpdated_at(rate.getUpdatedAt());
        dto.setReserved_field1(rate.getReservedField1());
        dto.setReserved_field2(rate.getReservedField2());
        dto.setReserved_field3(rate.getReservedField3());
        return dto;
    }
} 
package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.FqcQualityRateDTO;
import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyFqcQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyFqcQualityRate;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyFqcQualityRate7Days;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyFqcQualityRateRepository;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyFqcQualityRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyFqcQualityRateService {

    private final AssemblyFqcQualityRateRepository fqcQualityRateRepository;
    private final AssemblyFqcQualityRate7DaysRepository fqcQualityRate7DaysRepository;

    public List<FqcQualityRateDTO> getLatestFqcQualityRate() {
        List<AssemblyFqcQualityRate> rates = fqcQualityRateRepository.findLatest();
        return rates.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    public List<AssemblyFqcQualityRate7DaysDTO> getWeekFqcQualityRate(String workshopName) {
        LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);

        List<AssemblyFqcQualityRate7Days> rates;
        if (workshopName != null && !workshopName.isEmpty()) {
            rates = fqcQualityRate7DaysRepository.findByWorkshopNameAndRecordDateAfter(workshopName, sevenDaysAgo);
        } else {
            rates = fqcQualityRate7DaysRepository.findByRecordDateAfter(sevenDaysAgo);
        }

        return rates.stream()
            .map(this::convertTo7DaysDTO)
            .collect(Collectors.toList());
    }

    private FqcQualityRateDTO convertToDTO(AssemblyFqcQualityRate rate) {
        FqcQualityRateDTO dto = new FqcQualityRateDTO();
        dto.setWorkshop_name(rate.getWorkshopName());
        dto.setQuality_rate(rate.getQualityRate());
        dto.setRecord_date(rate.getRecordDate().toString());
        return dto;
    }

    private AssemblyFqcQualityRate7DaysDTO convertTo7DaysDTO(AssemblyFqcQualityRate7Days rate) {
        AssemblyFqcQualityRate7DaysDTO dto = new AssemblyFqcQualityRate7DaysDTO();
        dto.setId(rate.getId());
        dto.setWorkshop_name(rate.getWorkshopName());
        dto.setQuality_rate(rate.getQualityRate());
        dto.setRecord_date(rate.getRecordDate());
        dto.setUpdated_by(rate.getUpdatedBy());
        dto.setUpdated_at(rate.getUpdatedAt());
        dto.setReserved_field1(rate.getReservedField1());
        dto.setReserved_field2(rate.getReservedField2());
        dto.setReserved_field3(rate.getReservedField3());
        return dto;
    }
} 
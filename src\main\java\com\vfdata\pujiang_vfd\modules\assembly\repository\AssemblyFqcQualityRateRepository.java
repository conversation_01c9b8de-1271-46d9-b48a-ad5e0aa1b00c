package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyFqcQualityRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface AssemblyFqcQualityRateRepository extends JpaRepository<AssemblyFqcQualityRate, Long> {
    
    @Query("SELECT f FROM AssemblyFqcQualityRate f WHERE f.recordDate = (SELECT MAX(f2.recordDate) FROM AssemblyFqcQualityRate f2)")
    List<AssemblyFqcQualityRate> findLatest();
    
    List<AssemblyFqcQualityRate> findByRecordDateBetween(LocalDate startDate, LocalDate endDate);
    
    List<AssemblyFqcQualityRate> findByWorkshopNameAndRecordDateBetween(
        @Param("workshopName") String workshopName,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
} 
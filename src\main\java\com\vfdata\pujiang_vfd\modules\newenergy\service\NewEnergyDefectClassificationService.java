package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyDefectClassification;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyDefectClassificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyDefectClassificationService {

    private final NewEnergyDefectClassificationRepository defectClassificationRepository;

    /**
     * 获取抽检不良分析数据
     * @param workshopName 车间名称（可选）
     * @return 抽检不良分类数据列表
     */
    public List<NewEnergyDefectClassificationDTO> getDefectClassification(String workshopName) {
        List<NewEnergyDefectClassification> defectList;
        
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            // 填了参数：返回指定车间最新记录日期的数据
            defectList = defectClassificationRepository.findLatestByWorkshopName(workshopName.trim());
        } else {
            // 不填参数：返回每个车间最新记录日期的数据
            defectList = defectClassificationRepository.findLatestByAllWorkshops();
        }
        
        return defectList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyDefectClassificationDTO convertToDTO(NewEnergyDefectClassification entity) {
        NewEnergyDefectClassificationDTO dto = new NewEnergyDefectClassificationDTO();
        dto.setId(entity.getId());
        dto.setClassification_name(entity.getClassificationName());
        dto.setDefect_count(entity.getDefectCount());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.injection.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.injection.dto.PersonnelCountDTO;
import com.vfdata.pujiang_vfd.modules.injection.service.InjectionPersonnelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@RequestMapping("/injection/personnel")
@RequiredArgsConstructor
@Tag(name = "注塑车间页面接口")
public class InjectionPersonnelInfoController {

    private final InjectionPersonnelInfoService injectionPersonnelInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新人员信息", description = "获取注塑车间最新日期的人员信息记录")
    public ResponseUtils.Result<List<PersonnelCountDTO>> getLatestPersonnelInfo() {
        return ResponseUtils.success(injectionPersonnelInfoService.getLatestPersonnelInfo());
    }
} 
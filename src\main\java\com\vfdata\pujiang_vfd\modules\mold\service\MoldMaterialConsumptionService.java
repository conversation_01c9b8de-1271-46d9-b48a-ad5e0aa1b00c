package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldMaterialConsumptionDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldMaterialConsumption;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldMaterialConsumptionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldMaterialConsumptionService {

    private final MoldMaterialConsumptionRepository materialConsumptionRepository;

    /**
     * 获取最新的物料消耗数据
     */
    public List<MoldMaterialConsumptionDTO> getLatestMaterialConsumption() {
        List<MoldMaterialConsumption> entities = materialConsumptionRepository.findLatestRecords();
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldMaterialConsumptionDTO convertToDTO(MoldMaterialConsumption entity) {
        MoldMaterialConsumptionDTO dto = new MoldMaterialConsumptionDTO();
        dto.setId(entity.getId());
        dto.setMaterial_type(entity.getMaterialType());
        dto.setPujian_consumption(entity.getPujiangConsumption());
        dto.setBoluo_consumption(entity.getBoluoConsumption());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

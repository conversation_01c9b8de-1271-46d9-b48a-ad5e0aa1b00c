package com.vfdata.pujiang_vfd.common.util;

import java.io.*;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件工具类
 */
public class FileUtils {
    
    /**
     * 创建目录
     */
    public static void createDirectory(String path) throws IOException {
        Files.createDirectories(Paths.get(path));
    }
    
    /**
     * 创建文件
     */
    public static void createFile(String path) throws IOException {
        Path filePath = Paths.get(path);
        if (!Files.exists(filePath)) {
            Files.createFile(filePath);
        }
    }
    
    /**
     * 删除文件或目录
     */
    public static void delete(String path) throws IOException {
        Files.deleteIfExists(Paths.get(path));
    }
    
    /**
     * 复制文件
     */
    public static void copy(String source, String target) throws IOException {
        Files.copy(Paths.get(source), Paths.get(target), StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * 移动文件
     */
    public static void move(String source, String target) throws IOException {
        Files.move(Paths.get(source), Paths.get(target), StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * 读取文件内容
     */
    public static String readFile(String path) throws IOException {
        return new String(Files.readAllBytes(Paths.get(path)));
    }
    
    /**
     * 写入文件内容
     */
    public static void writeFile(String path, String content) throws IOException {
        Files.write(Paths.get(path), content.getBytes());
    }
    
    /**
     * 追加文件内容
     */
    public static void appendFile(String path, String content) throws IOException {
        Files.write(Paths.get(path), content.getBytes(), StandardOpenOption.APPEND);
    }
    
    /**
     * 获取文件大小
     */
    public static long getFileSize(String path) throws IOException {
        return Files.size(Paths.get(path));
    }
    
    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String path) {
        int lastDotIndex = path.lastIndexOf('.');
        return lastDotIndex > 0 ? path.substring(lastDotIndex + 1) : "";
    }
    
    /**
     * 获取文件名（不含扩展名）
     */
    public static String getFileNameWithoutExtension(String path) {
        String fileName = Paths.get(path).getFileName().toString();
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }
    
    /**
     * 列出目录下的所有文件
     */
    public static List<String> listFiles(String path) throws IOException {
        List<String> files = new ArrayList<>();
        Files.walk(Paths.get(path))
            .filter(Files::isRegularFile)
            .forEach(file -> files.add(file.toString()));
        return files;
    }
    
    /**
     * 判断文件是否存在
     */
    public static boolean exists(String path) {
        return Files.exists(Paths.get(path));
    }
    
    /**
     * 判断是否为目录
     */
    public static boolean isDirectory(String path) {
        return Files.isDirectory(Paths.get(path));
    }
} 
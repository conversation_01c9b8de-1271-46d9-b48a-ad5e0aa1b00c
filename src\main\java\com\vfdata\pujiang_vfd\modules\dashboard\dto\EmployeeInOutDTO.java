package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "员工进出记录DTO")
public class EmployeeInOutDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "员工姓名")
    private String employee_name;

    @Schema(description = "员工地址")
    private String employee_addr;

    @Schema(description = "员工状态")
    private String employee_state;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

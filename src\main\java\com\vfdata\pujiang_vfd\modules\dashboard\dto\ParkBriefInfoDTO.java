package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "园区简介信息DTO")
public class ParkBriefInfoDTO {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "数值")
    private Double num;

    @Schema(description = "单位")
    private String reserved_field1;
}

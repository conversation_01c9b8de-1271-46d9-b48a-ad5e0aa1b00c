package com.vfdata.pujiang_vfd.modules.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "设备信息DTO")
public class DeviceDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "设备类型")
    private String device_type;

    @Schema(description = "设备编号")
    private String device_num;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updateman;

    @Schema(description = "更新时间")
    private String updatetime;
}

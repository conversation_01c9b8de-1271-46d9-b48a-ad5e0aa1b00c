package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.ProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyProjectStatusService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/project-status")
@RequiredArgsConstructor
public class AssemblyProjectStatusController {

    private final AssemblyProjectStatusService projectStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新项目状态")
    public ResponseUtils.Result<List<ProjectStatusDTO>> getLatestProjectStatus(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<ProjectStatusDTO> statusList = projectStatusService.getLatestProjectStatus(workshop_name);
            return ResponseUtils.success(statusList);
        } catch (Exception e) {
            return ResponseUtils.error("获取项目状态失败：" + e.getMessage());
        }
    }
} 
package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionSamplingPassRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionSamplingPassRate;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionSamplingPassRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionSamplingPassRateService {
    
    private final InjectionSamplingPassRateRepository samplingPassRateRepository;

    public List<InjectionSamplingPassRateDTO> getLatestPassRates() {
        List<InjectionSamplingPassRate> rates = samplingPassRateRepository.findLatestByWorkshop();
        return rates.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private InjectionSamplingPassRateDTO convertToDTO(InjectionSamplingPassRate rate) {
        InjectionSamplingPassRateDTO dto = new InjectionSamplingPassRateDTO();
        dto.setId(rate.getId());
        dto.setWorkshop_name(rate.getWorkshopName());
        dto.setPass_rate(rate.getPassRate());
        dto.setRecord_date(rate.getRecordDate());
        dto.setUpdated_by(rate.getUpdatedBy());
        dto.setUpdated_at(rate.getUpdatedAt());
        dto.setReserved_field1(rate.getReservedField1());
        dto.setReserved_field2(rate.getReservedField2());
        dto.setReserved_field3(rate.getReservedField3());
        return dto;
    }
} 
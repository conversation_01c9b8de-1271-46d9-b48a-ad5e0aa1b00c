package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.ProjectClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectClassificationRepository extends JpaRepository<ProjectClassification, Long> {

    @Query("SELECT p FROM ProjectClassification p WHERE p.projectName = :projectName ORDER BY p.recordDate DESC")
    List<ProjectClassification> findByProjectName(@Param("projectName") String projectName);

    @Query("SELECT p FROM ProjectClassification p ORDER BY p.recordDate DESC")
    List<ProjectClassification> findAllOrderByRecordDateDesc();
} 
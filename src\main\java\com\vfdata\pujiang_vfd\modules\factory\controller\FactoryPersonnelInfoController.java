package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.FactoryPersonnelInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.FactoryPersonnelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 全厂人员信息Controller
 */
@Tag(name = "全厂页面接口")
@RestController
@RequestMapping("/factory/personnel")
@RequiredArgsConstructor
public class FactoryPersonnelInfoController {

    private final FactoryPersonnelInfoService factoryPersonnelInfoService;

    @Operation(summary = "获取最新人员信息")
    @GetMapping
    public ResponseUtils.Result<List<FactoryPersonnelInfoDTO>> getLatestPersonnelInfo() {
        return ResponseUtils.success(factoryPersonnelInfoService.getLatestPersonnelInfo());
    }
} 
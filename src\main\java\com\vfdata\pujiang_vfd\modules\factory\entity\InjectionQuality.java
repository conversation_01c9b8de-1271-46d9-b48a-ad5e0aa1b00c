package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "ioc_qc_injection_sampling_quality_rate")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "注塑抽检合格率信息")
public class InjectionQuality extends BaseEntity {

    @Schema(description = "车间名称")
    @Column(name = "workshop_name", length = 20)
    private String workshopName;

    @Schema(description = "合格率")
    @Column(name = "quality_rate", precision = 5, scale = 2)
    private BigDecimal qualityRate;
} 
package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionEquipmentStatusDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionEquipmentStatus;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionEquipmentStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionEquipmentStatusService {
    
    private final InjectionEquipmentStatusRepository equipmentStatusRepository;

    public List<InjectionEquipmentStatusDTO> getEquipmentStatus(String workshopName) {
        List<InjectionEquipmentStatus> statusList;
        if (workshopName == null || workshopName.trim().isEmpty()) {
            statusList = equipmentStatusRepository.findAllStatus();
        } else {
            statusList = equipmentStatusRepository.findByWorkshopName(workshopName);
        }

        return statusList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    public List<InjectionEquipmentStatusDTO> getLatestEquipmentStatusDistribution(String workshopName) {
        List<InjectionEquipmentStatus> statusList;
        if (workshopName == null || workshopName.trim().isEmpty()) {
            statusList = equipmentStatusRepository.findLatestDistribution();
        } else {
            statusList = equipmentStatusRepository.findLatestDistributionByWorkshopName(workshopName);
        }

        if (statusList.isEmpty()) {
            throw new RuntimeException("没有找到设备状态分布记录");
        }

        return statusList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private InjectionEquipmentStatusDTO convertToDTO(InjectionEquipmentStatus status) {
        InjectionEquipmentStatusDTO dto = new InjectionEquipmentStatusDTO();
        dto.setId(status.getId());
        dto.setWorkshop_name(status.getWorkshopName());
        dto.setStatus_name(status.getStatusName());
        dto.setStatus_count(status.getStatusCount());
        dto.setRecord_date(status.getRecordDate());
        dto.setUpdated_by(status.getUpdatedBy());
        dto.setUpdated_at(status.getUpdatedAt());
        dto.setReserved_field1(status.getReservedField1());
        dto.setReserved_field2(status.getReservedField2());
        dto.setReserved_field3(status.getReservedField3());
        return dto;
    }
} 
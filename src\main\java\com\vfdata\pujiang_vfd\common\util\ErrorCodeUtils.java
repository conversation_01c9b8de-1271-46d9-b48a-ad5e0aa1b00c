package com.vfdata.pujiang_vfd.common.util;

import lombok.Getter;

/**
 * 错误码工具类
 */
public class ErrorCodeUtils {
    
    /**
     * 错误码枚举
     */
    @Getter
    public enum ErrorCode {
        // 系统级错误码 (1000-1999)
        SYSTEM_ERROR(1000, "系统错误"),
        PARAM_ERROR(1001, "参数错误"),
        UNAUTHORIZED(1002, "未授权"),
        FORBIDDEN(1003, "禁止访问"),
        NOT_FOUND(1004, "资源不存在"),
        METHOD_NOT_ALLOWED(1005, "请求方法不允许"),
        REQUEST_TIMEOUT(1006, "请求超时"),
        TOO_MANY_REQUESTS(1007, "请求过于频繁"),
        
        // 业务级错误码 (2000-2999)
        BUSINESS_ERROR(2000, "业务处理错误"),
        DATA_NOT_FOUND(2001, "数据不存在"),
        DATA_ALREADY_EXISTS(2002, "数据已存在"),
        DATA_INVALID(2003, "数据无效"),
        OPERATION_FAILED(2004, "操作失败"),
        
        // 数据库错误码 (3000-3999)
        DB_ERROR(3000, "数据库错误"),
        DB_CONNECTION_ERROR(3001, "数据库连接错误"),
        DB_QUERY_ERROR(3002, "数据库查询错误"),
        DB_UPDATE_ERROR(3003, "数据库更新错误"),
        DB_DELETE_ERROR(3004, "数据库删除错误"),
        
        // 文件操作错误码 (4000-4999)
        FILE_ERROR(4000, "文件操作错误"),
        FILE_NOT_FOUND(4001, "文件不存在"),
        FILE_READ_ERROR(4002, "文件读取错误"),
        FILE_WRITE_ERROR(4003, "文件写入错误"),
        FILE_DELETE_ERROR(4004, "文件删除错误"),
        
        // 网络错误码 (5000-5999)
        NETWORK_ERROR(5000, "网络错误"),
        NETWORK_TIMEOUT(5001, "网络超时"),
        NETWORK_CONNECTION_ERROR(5002, "网络连接错误"),
        
        // 第三方服务错误码 (6000-6999)
        THIRD_PARTY_ERROR(6000, "第三方服务错误"),
        API_CALL_ERROR(6001, "API调用错误"),
        API_RESPONSE_ERROR(6002, "API响应错误"),
        
        // 用户相关错误码 (7000-7999)
        USER_ERROR(7000, "用户错误"),
        USER_NOT_FOUND(7001, "用户不存在"),
        USER_ALREADY_EXISTS(7002, "用户已存在"),
        USER_PASSWORD_ERROR(7003, "用户密码错误"),
        USER_TOKEN_ERROR(7004, "用户令牌错误"),
        
        // 权限相关错误码 (8000-8999)
        PERMISSION_ERROR(8000, "权限错误"),
        PERMISSION_DENIED(8001, "权限不足"),
        ROLE_NOT_FOUND(8002, "角色不存在"),
        ROLE_ALREADY_EXISTS(8003, "角色已存在");

        private final int code;
        private final String message;

        ErrorCode(int code, String message) {
            this.code = code;
            this.message = message;
        }
    }

    /**
     * 获取错误码对应的错误信息
     */
    public static String getMessage(int code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode.getMessage();
            }
        }
        return "未知错误";
    }

    /**
     * 获取错误码枚举
     */
    public static ErrorCode getErrorCode(int code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return ErrorCode.SYSTEM_ERROR;
    }

    /**
     * 判断是否为系统级错误
     */
    public static boolean isSystemError(int code) {
        return code >= 1000 && code < 2000;
    }

    /**
     * 判断是否为业务级错误
     */
    public static boolean isBusinessError(int code) {
        return code >= 2000 && code < 3000;
    }

    /**
     * 判断是否为数据库错误
     */
    public static boolean isDatabaseError(int code) {
        return code >= 3000 && code < 4000;
    }

    /**
     * 判断是否为文件操作错误
     */
    public static boolean isFileError(int code) {
        return code >= 4000 && code < 5000;
    }

    /**
     * 判断是否为网络错误
     */
    public static boolean isNetworkError(int code) {
        return code >= 5000 && code < 6000;
    }

    /**
     * 判断是否为第三方服务错误
     */
    public static boolean isThirdPartyError(int code) {
        return code >= 6000 && code < 7000;
    }

    /**
     * 判断是否为用户相关错误
     */
    public static boolean isUserError(int code) {
        return code >= 7000 && code < 8000;
    }

    /**
     * 判断是否为权限相关错误
     */
    public static boolean isPermissionError(int code) {
        return code >= 8000 && code < 9000;
    }
} 
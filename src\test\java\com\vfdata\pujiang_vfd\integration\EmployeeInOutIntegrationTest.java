package com.vfdata.pujiang_vfd.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
class EmployeeInOutIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testEmployeeInOutApiTimeFormat() throws Exception {
        // 调用API
        MvcResult result = mockMvc.perform(get("/dashboard/employee-inout/"))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("完整API响应: " + responseContent);

        // 解析JSON
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        
        // 检查基本结构
        assertTrue(jsonNode.has("code"), "响应应包含code字段");
        assertTrue(jsonNode.has("data"), "响应应包含data字段");
        
        JsonNode dataArray = jsonNode.get("data");
        assertTrue(dataArray.isArray(), "data应该是数组");
        
        System.out.println("数据记录数量: " + dataArray.size());
        
        // 检查每条记录的时间格式
        for (int i = 0; i < Math.min(5, dataArray.size()); i++) {
            JsonNode record = dataArray.get(i);
            
            System.out.println("记录 " + (i + 1) + ":");
            System.out.println("  完整记录: " + record.toString());
            
            if (record.has("recordDate") && !record.get("recordDate").isNull()) {
                String recordDate = record.get("recordDate").asText();
                System.out.println("  recordDate: " + recordDate);
                
                // 验证时间格式
                assertFalse(recordDate.contains("+08"), "recordDate不应包含+08时区信息");
                assertFalse(recordDate.contains("+0800"), "recordDate不应包含+0800时区信息");
                
                // 验证格式是否正确
                if (recordDate.contains("T")) {
                    // 如果包含T，说明还是ISO格式，需要进一步检查
                    System.out.println("  警告: recordDate包含T分隔符: " + recordDate);
                } else {
                    // 应该是标准格式 yyyy-MM-dd HH:mm:ss
                    assertTrue(recordDate.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"), 
                        "recordDate应使用yyyy-MM-dd HH:mm:ss格式，实际: " + recordDate);
                }
            }
            
            if (record.has("updatetime") && !record.get("updatetime").isNull()) {
                String updatetime = record.get("updatetime").asText();
                System.out.println("  updatetime: " + updatetime);
                
                // 验证时间格式
                assertFalse(updatetime.contains("+08"), "updatetime不应包含+08时区信息");
                assertFalse(updatetime.contains("+0800"), "updatetime不应包含+0800时区信息");
            }
            
            System.out.println("---");
        }
        
        // 验证整个响应不包含时区信息
        assertFalse(responseContent.contains("+08"), "整个API响应不应包含+08时区信息");
        assertFalse(responseContent.contains("+0800"), "整个API响应不应包含+0800时区信息");
    }
}

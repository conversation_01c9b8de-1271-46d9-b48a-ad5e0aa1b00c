package com.vfdata.pujiang_vfd.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vfdata.pujiang_vfd.modules.dashboard.dto.EmployeeInOutDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class JacksonSerializationTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testEmployeeInOutDTOSerialization() throws Exception {
        // 创建测试DTO
        EmployeeInOutDTO dto = new EmployeeInOutDTO();
        dto.setId(1L);
        dto.setEmployee_name("测试员工");
        dto.setEmployee_addr("测试地址");
        dto.setEmployee_state("进");
        dto.setRecord_date("2025-06-23 18:31:54");  // 标准格式
        dto.setUpdateman("admin");
        dto.setUpdatetime("2025-06-23 18:31:54");  // 标准格式

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(dto);
        
        System.out.println("序列化结果: " + json);
        
        // 验证不包含时区信息
        assertFalse(json.contains("+08"), "JSON不应包含+08时区信息");
        assertFalse(json.contains("+0800"), "JSON不应包含+0800时区信息");
        assertFalse(json.contains("T"), "JSON不应包含ISO格式的T分隔符");
        
        // 验证包含正确的时间格式
        assertTrue(json.contains("2025-06-23 18:31:54"), "JSON应包含正确的时间格式");
        
        // 反序列化验证
        EmployeeInOutDTO deserializedDto = objectMapper.readValue(json, EmployeeInOutDTO.class);
        assertEquals(dto.getRecord_date(), deserializedDto.getRecord_date());
        assertEquals(dto.getUpdatetime(), deserializedDto.getUpdatetime());
    }

    @Test
    void testTimeFormatConsistency() throws Exception {
        // 测试各种时间格式的一致性
        String[] timeFormats = {
            "2025-06-23 18:31:54",
            "2025-01-01 00:00:00",
            "2025-12-31 23:59:59"
        };
        
        for (String timeFormat : timeFormats) {
            EmployeeInOutDTO dto = new EmployeeInOutDTO();
            dto.setRecord_date(timeFormat);
            dto.setUpdatetime(timeFormat);
            
            String json = objectMapper.writeValueAsString(dto);
            
            // 验证序列化后的格式
            assertFalse(json.contains("+08"), "时间格式 " + timeFormat + " 序列化后不应包含时区信息");
            assertTrue(json.contains(timeFormat), "时间格式 " + timeFormat + " 应保持不变");
        }
    }
}

package com.vfdata.pujiang_vfd.modules.painting.repository;

import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaintingPersonnelInfoRepository extends JpaRepository<PaintingPersonnelInfo, Long> {

    /**
     * 获取最新记录日期的所有人员信息
     */
    @Query("SELECT p FROM PaintingPersonnelInfo p WHERE p.recordDate = (SELECT MAX(p2.recordDate) FROM PaintingPersonnelInfo p2)")
    List<PaintingPersonnelInfo> findLatestRecords();
}

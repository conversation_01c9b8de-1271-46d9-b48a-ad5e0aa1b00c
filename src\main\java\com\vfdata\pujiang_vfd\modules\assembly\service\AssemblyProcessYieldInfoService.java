package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyProcessYieldInfoDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyProcessYieldInfo;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyProcessYieldInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyProcessYieldInfoService {

    private final AssemblyProcessYieldInfoRepository processYieldInfoRepository;

    /**
     * 获取最近7天的工序良率数据
     */
    public List<AssemblyProcessYieldInfoDTO> getWeeklyProcessYield(String workshopName) {
        LocalDate startDate = LocalDate.now().minusDays(6); // 最近7天
        List<AssemblyProcessYieldInfo> records;

        if (workshopName != null && !workshopName.trim().isEmpty()) {
            records = processYieldInfoRepository.findWeeklyDataByWorkshopName(workshopName, startDate);
        } else {
            records = processYieldInfoRepository.findWeeklyData(startDate);
        }

        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有工序良率数据
     */
    public List<AssemblyProcessYieldInfoDTO> getAllProcessYield() {
        List<AssemblyProcessYieldInfo> records = processYieldInfoRepository.findAllByOrderByRecordDateDesc();
        
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private AssemblyProcessYieldInfoDTO convertToDTO(AssemblyProcessYieldInfo entity) {
        AssemblyProcessYieldInfoDTO dto = new AssemblyProcessYieldInfoDTO();
        dto.setId(entity.getId());
        dto.setProcess_yield(entity.getProcessYield());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

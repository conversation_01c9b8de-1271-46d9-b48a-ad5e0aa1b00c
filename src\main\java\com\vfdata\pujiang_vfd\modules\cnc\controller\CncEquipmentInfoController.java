package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncEquipmentInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/equipment-info")
@RequiredArgsConstructor
public class CncEquipmentInfoController {

    private final CncEquipmentInfoService equipmentInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间设备信息", description = "获取CNC车间最新的设备运行状态信息")
    public ResponseUtils.Result<CncEquipmentInfoDTO> getLatestEquipmentInfo() {
        try {
            CncEquipmentInfoDTO data = equipmentInfoService.getLatestEquipmentInfo();
            if (data == null) {
                return ResponseUtils.success(new CncEquipmentInfoDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间设备信息失败：" + e.getMessage());
        }
    }
}

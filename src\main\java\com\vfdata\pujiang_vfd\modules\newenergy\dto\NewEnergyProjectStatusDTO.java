package com.vfdata.pujiang_vfd.modules.newenergy.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "新能源车间项目开机分布DTO")
public class NewEnergyProjectStatusDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "项目名称")
    private String project_name;

    @Schema(description = "产品名称")
    private String product_name;

    @Schema(description = "机台号")
    private String machine_id;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

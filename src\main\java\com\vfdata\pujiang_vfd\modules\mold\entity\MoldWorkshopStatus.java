package com.vfdata.pujiang_vfd.modules.mold.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_mjcj_workshop_status")
@Schema(description = "模具车间设备状况")
public class MoldWorkshopStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name")
    @Schema(description = "车间名称")
    private String workshopName;

    @Column(name = "workshop_rate")
    @Schema(description = "车间状态率")
    private BigDecimal workshopRate;

    @Column(name = "park_name")
    @Schema(description = "园区名称")
    private String parkName;

    @Column(name = "park_status_rate")
    @Schema(description = "园区状态率")
    private BigDecimal parkStatusRate;

    @Column(name = "total_equipment_count")
    @Schema(description = "设备总数")
    private Integer totalEquipmentCount;

    @Column(name = "operating_equipment_count")
    @Schema(description = "总开机数")
    private Integer operatingEquipmentCount;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

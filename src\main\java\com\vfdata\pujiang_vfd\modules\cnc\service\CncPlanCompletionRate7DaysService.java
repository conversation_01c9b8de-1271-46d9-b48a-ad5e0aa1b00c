package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncPlanCompletionRate7Days;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncPlanCompletionRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncPlanCompletionRate7DaysService {

    private final CncPlanCompletionRate7DaysRepository planCompletionRate7DaysRepository;

    /**
     * 获取指定车间的计划达成率近7天数据
     */
    public List<CncPlanCompletionRate7DaysDTO> getPlanCompletionRate7Days(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<CncPlanCompletionRate7Days> entities = planCompletionRate7DaysRepository.findLatest7RecordsByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncPlanCompletionRate7DaysDTO convertToDTO(CncPlanCompletionRate7Days entity) {
        CncPlanCompletionRate7DaysDTO dto = new CncPlanCompletionRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setCompletion_rate(entity.getCompletionRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

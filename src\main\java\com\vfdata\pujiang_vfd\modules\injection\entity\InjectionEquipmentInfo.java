package com.vfdata.pujiang_vfd.modules.injection.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zscj_equipment_info")
@Schema(description = "注塑车间设备信息")
public class InjectionEquipmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "total_equipment_count")
    @Schema(description = "设备总数")
    private Integer totalEquipmentCount;

    @Column(name = "operating_equipment_count")
    @Schema(description = "运行设备数")
    private Integer operatingEquipmentCount;

    @Column(name = "equipment_overall_efficiency")
    @Schema(description = "设备综合效率")
    private BigDecimal equipmentOverallEfficiency;

    @Column(name = "operating_rate")
    @Schema(description = "运行率")
    private BigDecimal operatingRate;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
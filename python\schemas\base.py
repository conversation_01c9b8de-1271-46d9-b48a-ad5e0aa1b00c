from typing import TypeVar, Generic, Optional, Any
from pydantic import BaseModel

DataT = TypeVar("DataT")

class Response(BaseModel, Generic[DataT]):
    """标准响应模型"""
    code: int = 200
    message: str = "success"
    success: bool = True
    data: Optional[DataT] = None

class ErrorResponse(BaseModel):
    """错误响应模型"""
    code: int
    message: str
    success: bool = False
    data: Any = None 
package com.vfdata.pujiang_vfd.modules.factory.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "注塑抽检合格率DTO")
public class InjectionQualityDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "车间名称")
    private String workshop_name;

    @Schema(description = "合格率")
    private BigDecimal quality_rate;

    @Schema(description = "记录日期")
    private String record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private String updated_at;

    @Schema(description = "预留字段1")
    private String reserved1;

    @Schema(description = "预留字段2")
    private String reserved2;

    @Schema(description = "预留字段3")
    private String reserved3;
} 
package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.ProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyProjectStatus;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyProjectStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyProjectStatusService {

    private final AssemblyProjectStatusRepository projectStatusRepository;

    public List<ProjectStatusDTO> getLatestProjectStatus(String workshopName) {
        List<AssemblyProjectStatus> statusList;
        if (workshopName != null && !workshopName.isEmpty()) {
            statusList = projectStatusRepository.findLatestByWorkshopName(workshopName);
        } else {
            statusList = projectStatusRepository.findLatest();
        }
        
        return statusList.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    private ProjectStatusDTO convertToDTO(AssemblyProjectStatus status) {
        ProjectStatusDTO dto = new ProjectStatusDTO();
        dto.setId(status.getId());
        dto.setWorkshop_name(status.getWorkshopName());
        dto.setProject_name(status.getProjectName());
        dto.setProduct_name(status.getProductName());
        dto.setMachine_id(status.getMachineId());
        dto.setRecord_date(status.getRecordDate());
        dto.setUpdated_by(status.getUpdatedBy());
        dto.setUpdated_at(status.getUpdatedAt());
        dto.setReserved_field1(status.getReservedField1());
        dto.setReserved_field2(status.getReservedField2());
        dto.setReserved_field3(status.getReservedField3());
        return dto;
    }
} 
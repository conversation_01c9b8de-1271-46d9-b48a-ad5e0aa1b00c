package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PersonnelCountDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingPersonnelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/personnel-info")
@RequiredArgsConstructor
public class PaintingPersonnelInfoController {

    private final PaintingPersonnelInfoService personnelInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取喷涂车间人员信息", description = "获取喷涂车间最新的人员统计信息")
    public ResponseUtils.Result<List<PersonnelCountDTO>> getLatestPersonnelInfo() {
        try {
            List<PersonnelCountDTO> infoList = personnelInfoService.getLatestPersonnelInfo();
            return ResponseUtils.success(infoList);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间人员信息失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncPlanCompletionRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/plan-completion-rate-7days")
@RequiredArgsConstructor
public class CncPlanCompletionRate7DaysController {

    private final CncPlanCompletionRate7DaysService planCompletionRate7DaysService;

    @GetMapping
    @Operation(summary = "获取CNC车间计划达成率近7天", description = "获取指定CNC车间计划达成率近7天数据")
    public ResponseUtils.Result<List<CncPlanCompletionRate7DaysDTO>> getPlanCompletionRate7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<CncPlanCompletionRate7DaysDTO> dataList = planCompletionRate7DaysService.getPlanCompletionRate7Days(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间计划达成率7天数据失败：" + e.getMessage());
        }
    }
}

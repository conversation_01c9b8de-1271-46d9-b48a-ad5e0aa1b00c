package com.vfdata.pujiang_vfd.modules.assembly.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zzcj_project_status")
public class AssemblyProjectStatus {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "workshop_name")
    private String workshopName;
    
    @Column(name = "project_name")
    private String projectName;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "machine_id")
    private String machineId;

    @Column(name = "record_date")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    private String reservedField3;
} 
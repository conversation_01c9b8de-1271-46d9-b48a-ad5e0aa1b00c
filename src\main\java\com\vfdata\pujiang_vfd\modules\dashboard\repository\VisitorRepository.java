package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.Visitor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface VisitorRepository extends JpaRepository<Visitor, Long> {
    
    /**
     * 根据日期范围查询访客信息，按记录日期降序排列
     */
    @Query("SELECT v FROM Visitor v WHERE " +
           "(:startDate IS NULL OR v.recordDate >= :startDate) AND " +
           "(:endDate IS NULL OR v.recordDate <= :endDate) " +
           "ORDER BY v.recordDate DESC")
    List<Visitor> findByDateRange(@Param("startDate") LocalDate startDate, 
                                  @Param("endDate") LocalDate endDate);
    
    /**
     * 查询所有访客信息，按记录日期降序排列
     */
    List<Visitor> findAllByOrderByRecordDateDesc();
}

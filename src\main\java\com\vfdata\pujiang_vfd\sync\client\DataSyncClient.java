package com.vfdata.pujiang_vfd.sync.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vfdata.pujiang_vfd.config.DataSyncConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataSyncClient {

    private final DataSyncConfig config;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 从外部API获取数据
     * 
     * @param endpoint API端点
     * @return 数据列表
     */
    public List<Map<String, Object>> fetchData(String endpoint) {
        try {
            log.info("开始从接口获取数据: {}", endpoint);
            
            String url = config.getBaseUrl() + endpoint;
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("page", 1);
            requestBody.put("size", 1000);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                request, 
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                // 解析响应数据
                Map<String, Object> responseData = objectMapper.readValue(
                    response.getBody(), 
                    new TypeReference<Map<String, Object>>() {}
                );
                
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> data = (List<Map<String, Object>>) responseData.get("data");
                
                if (data != null) {
                    log.info("成功从接口获取数据: {}, 数据量: {}", endpoint, data.size());
                    return data;
                } else {
                    log.warn("接口返回数据为空: {}", endpoint);
                    return List.of();
                }
            } else {
                log.error("接口请求失败: {}, 状态码: {}", endpoint, response.getStatusCode());
                return List.of();
            }
            
        } catch (Exception e) {
            log.error("从接口获取数据失败: {}, 错误: {}", endpoint, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 带重试机制的数据获取
     * 
     * @param endpoint API端点
     * @return 数据列表
     */
    public List<Map<String, Object>> fetchDataWithRetry(String endpoint) {
        int retries = 0;
        while (retries < config.getMaxRetries()) {
            try {
                List<Map<String, Object>> data = fetchData(endpoint);
                if (!data.isEmpty()) {
                    return data;
                }
            } catch (Exception e) {
                log.warn("第{}次获取数据失败: {}, 错误: {}", retries + 1, endpoint, e.getMessage());
            }
            
            retries++;
            if (retries < config.getMaxRetries()) {
                try {
                    Thread.sleep(config.getRetryInterval());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        log.error("重试{}次后仍无法获取数据: {}", config.getMaxRetries(), endpoint);
        return List.of();
    }
}

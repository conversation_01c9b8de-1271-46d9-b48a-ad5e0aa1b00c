package com.vfdata.pujiang_vfd.modules.cnc.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_cnccj_daily_achievement_rate")
@Schema(description = "CNC车间计划达成率")
public class CncDailyAchievementRate {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name", length = 20)
    @Schema(description = "所属车间")
    private String workshopName;

    @Column(name = "planned_quantity")
    @Schema(description = "计划数量")
    private Integer plannedQuantity;

    @Column(name = "actual_quantity")
    @Schema(description = "实际完成数量")
    private Integer actualQuantity;

    @Column(name = "achievement_rate")
    @Schema(description = "计划达成率")
    private Float achievementRate;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by", length = 20)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "备用字段3")
    private String reservedField3;
}

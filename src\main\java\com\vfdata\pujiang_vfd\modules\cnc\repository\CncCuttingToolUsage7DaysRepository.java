package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncCuttingToolUsage7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncCuttingToolUsage7DaysRepository extends JpaRepository<CncCuttingToolUsage7Days, Long> {

    /**
     * 获取指定车间的最新7条记录
     */
    @Query("SELECT c FROM CncCuttingToolUsage7Days c WHERE c.workshopName = :workshopName ORDER BY c.recordDate DESC LIMIT 7")
    List<CncCuttingToolUsage7Days> findLatest7RecordsByWorkshop(@Param("workshopName") String workshopName);
}

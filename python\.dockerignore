# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local development
.env.local
.env.development
.env.test
.env.production

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
docs/
*.md

# Tests
tests/
test/
testing/
coverage/
.coverage
.pytest_cache/
htmlcov/

# Other
*.bak
*.tmp
.DS_Store 
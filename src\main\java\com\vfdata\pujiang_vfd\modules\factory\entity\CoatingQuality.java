package com.vfdata.pujiang_vfd.modules.factory.entity;

import com.vfdata.pujiang_vfd.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Table(name = "ioc_qc_coating_inspection_pass_rate")
@Schema(description = "涂装巡检检验合格率信息")
@Getter
@Setter
public class CoatingQuality extends BaseEntity {
    
    @Schema(description = "合格率")
    @Column(name = "pass_rate")
    private BigDecimal passRate;
} 
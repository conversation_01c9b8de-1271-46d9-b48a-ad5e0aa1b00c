package com.vfdata.pujiang_vfd.modules.assembly.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "ioc_zzcj_fqc_inspection_defect_analysis")
public class AssemblyFqcDefectAnalysis {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "workshop_name")
    private String workshopName;

    @Column(name = "defect_type_name")
    private String defectTypeName;

    @Column(name = "defect_type_count")
    private Integer defectTypeCount;

    @Column(name = "record_date")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "updated_at")
    private java.time.LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    private String reservedField3;
} 
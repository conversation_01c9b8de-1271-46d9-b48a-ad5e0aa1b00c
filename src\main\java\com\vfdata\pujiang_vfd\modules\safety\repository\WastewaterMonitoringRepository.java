package com.vfdata.pujiang_vfd.modules.safety.repository;

import com.vfdata.pujiang_vfd.modules.safety.entity.WastewaterMonitoring;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface WastewaterMonitoringRepository extends JpaRepository<WastewaterMonitoring, Long> {
    
    List<WastewaterMonitoring> findAllByOrderByRecordTimeDesc();
}

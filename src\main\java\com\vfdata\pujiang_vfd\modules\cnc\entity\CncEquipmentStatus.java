package com.vfdata.pujiang_vfd.modules.cnc.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_cnccj_equipment_status")
@Schema(description = "CNC车间设备状态分布")
public class CncEquipmentStatus {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "workshop_name")
    @Schema(description = "所属车间")
    private String workshopName;

    @Column(name = "status_name")
    @Schema(description = "状态名称")
    private String statusName;

    @Column(name = "status_count")
    @Schema(description = "状态数量")
    private Integer statusCount;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

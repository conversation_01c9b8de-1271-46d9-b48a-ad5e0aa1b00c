package com.vfdata.pujiang_vfd.modules.cnc.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_cnccj_equipment_info")
@Schema(description = "CNC车间设备信息")
public class CncEquipmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "自增主键")
    private Long id;

    @Column(name = "operating_rate")
    @Schema(description = "开机率")
    private BigDecimal operatingRate;

    @Column(name = "total_equipment_count")
    @Schema(description = "设备总数")
    private Integer totalEquipmentCount;

    @Column(name = "operating_equipment_count")
    @Schema(description = "运行设备数量")
    private Integer operatingEquipmentCount;

    @Column(name = "oee_rate")
    @Schema(description = "OEE率")
    private BigDecimal oeeRate;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDate recordDate;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1")
    @Schema(description = "备用字段1")
    private String reservedField1;

    @Column(name = "reserved_field2")
    @Schema(description = "备用字段2")
    private String reservedField2;

    @Column(name = "reserved_field3")
    @Schema(description = "备用字段3")
    private String reservedField3;
}

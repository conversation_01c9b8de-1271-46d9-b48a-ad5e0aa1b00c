package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.WorkshopPersonnelInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.WorkshopPersonnelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "全厂页面接口")
@RestController
@RequestMapping("/factory/workshop/personnel")
@RequiredArgsConstructor
public class WorkshopPersonnelInfoController {

    private final WorkshopPersonnelInfoService workshopPersonnelInfoService;

    @Operation(summary = "车间人员信息")
    @GetMapping
    public ResponseUtils.Result<List<WorkshopPersonnelInfoDTO>> getLatestPersonnelInfo(
            @Parameter(description = "车间名称") @RequestParam(required = false) String workshop_name) {
        return ResponseUtils.success(workshopPersonnelInfoService.getLatestPersonnelInfo(workshop_name));
    }
} 
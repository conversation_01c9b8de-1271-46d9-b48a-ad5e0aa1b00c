package com.vfdata.pujiang_vfd.modules.cnc.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;

@Data
@Schema(description = "CNC车间计划达成率近7天DTO")
public class CncPlanCompletionRate7DaysDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "达成率")
    private Float completion_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新日期")
    private LocalDate updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionDailyAchievementRate;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionPlanCompletionRate7Days;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionDailyAchievementRateRepository;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionPlanCompletionRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionDailyAchievementRateService {

    private final InjectionDailyAchievementRateRepository injectionDailyAchievementRateRepository;
    private final InjectionPlanCompletionRate7DaysRepository injectionPlanCompletionRate7DaysRepository;

    public List<InjectionDailyAchievementRateDTO> getLatestAchievementRateByWorkshop() {
        List<InjectionDailyAchievementRate> latestInfoList = injectionDailyAchievementRateRepository.findLatestByWorkshop();
        
        return latestInfoList.stream().map(info -> {
            InjectionDailyAchievementRateDTO dto = new InjectionDailyAchievementRateDTO();
            dto.setId(info.getId());
            dto.setWorkshop_name(info.getWorkshopName());
            dto.setPlanned_quantity(info.getPlannedQuantity());
            dto.setActual_quantity(info.getActualQuantity());
            dto.setAchievement_rate(info.getAchievementRate());
            dto.setRecord_date(info.getRecordDate());
            dto.setUpdated_by(info.getUpdatedBy());
            dto.setUpdated_at(info.getUpdatedAt());
            dto.setReserved_field1(info.getReservedField1());
            dto.setReserved_field2(info.getReservedField2());
            dto.setReserved_field3(info.getReservedField3());
            return dto;
        }).collect(Collectors.toList());
    }

    public List<InjectionDailyAchievementRateDTO> getTodayAchievementRate() {
        List<InjectionDailyAchievementRate> todayInfoList = injectionDailyAchievementRateRepository.findTodayAchievementRate();
        
        return todayInfoList.stream().map(info -> {
            InjectionDailyAchievementRateDTO dto = new InjectionDailyAchievementRateDTO();
            dto.setId(info.getId());
            dto.setWorkshop_name(info.getWorkshopName());
            dto.setPlanned_quantity(info.getPlannedQuantity());
            dto.setActual_quantity(info.getActualQuantity());
            dto.setAchievement_rate(info.getAchievementRate());
            dto.setRecord_date(info.getRecordDate());
            dto.setUpdated_by(info.getUpdatedBy());
            dto.setUpdated_at(info.getUpdatedAt());
            dto.setReserved_field1(info.getReservedField1());
            dto.setReserved_field2(info.getReservedField2());
            dto.setReserved_field3(info.getReservedField3());
            return dto;
        }).collect(Collectors.toList());
    }

    public List<InjectionPlanCompletionRate7DaysDTO> getWeekAchievementRateByWorkshop(String workshopName) {
        List<InjectionPlanCompletionRate7Days> weekInfoList = injectionPlanCompletionRate7DaysRepository.findByWorkshopName(workshopName);

        return weekInfoList.stream()
                .limit(7)
                .map(info -> {
                    InjectionPlanCompletionRate7DaysDTO dto = new InjectionPlanCompletionRate7DaysDTO();
                    dto.setId(info.getId());
                    dto.setWorkshop_name(info.getWorkshopName());
                    dto.setCompletion_rate(info.getCompletionRate());
                    dto.setRecord_date(info.getRecordDate());
                    dto.setUpdated_by(info.getUpdatedBy());
                    dto.setUpdated_at(info.getUpdatedAt());
                    dto.setReserved_field1(info.getReservedField1());
                    dto.setReserved_field2(info.getReservedField2());
                    dto.setReserved_field3(info.getReservedField3());
                    return dto;
                }).collect(Collectors.toList());
    }
} 
package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyDailyAchievementRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/daily-achievement-rate")
@RequiredArgsConstructor
public class NewEnergyDailyAchievementRateController {

    private final NewEnergyDailyAchievementRateService dailyAchievementRateService;

    @GetMapping
    @Operation(summary = "获取日计划达成率", description = "获取每个车间最新记录日期的日计划达成率数据")
    public ResponseUtils.Result<List<NewEnergyDailyAchievementRateDTO>> getLatestDailyAchievementRate() {
        try {
            List<NewEnergyDailyAchievementRateDTO> data = dailyAchievementRateService.getLatestDailyAchievementRate();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取日计划达成率失败：" + e.getMessage());
        }
    }
}

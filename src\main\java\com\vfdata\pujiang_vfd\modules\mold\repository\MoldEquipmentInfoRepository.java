package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MoldEquipmentInfoRepository extends JpaRepository<MoldEquipmentInfo, Long> {

    /**
     * 获取最新的设备信息记录
     */
    @Query("SELECT m FROM MoldEquipmentInfo m WHERE m.recordDate = (SELECT MAX(m2.recordDate) FROM MoldEquipmentInfo m2)")
    MoldEquipmentInfo findLatestRecord();
}

package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.TestDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyTestDefectClassification;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyTestDefectClassificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyTestDefectClassificationService {

    private final AssemblyTestDefectClassificationRepository testDefectClassificationRepository;

    public List<TestDefectClassificationDTO> getLatestTestDefectClassification(String workshopName) {
        List<AssemblyTestDefectClassification> classificationList;
        if (workshopName != null && !workshopName.isEmpty()) {
            classificationList = testDefectClassificationRepository.findLatestByWorkshopName(workshopName);
        } else {
            classificationList = testDefectClassificationRepository.findLatest();
        }
        
        return classificationList.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    private TestDefectClassificationDTO convertToDTO(AssemblyTestDefectClassification classification) {
        TestDefectClassificationDTO dto = new TestDefectClassificationDTO();
        dto.setWorkshop_name(classification.getWorkshopName());
        dto.setClassification_name(classification.getClassificationName());
        dto.setDefect_count(classification.getDefectCount());
        dto.setRecord_date(classification.getRecordDate().toString());
        return dto;
    }
} 
package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingDailyAchievementRateDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingDailyAchievementRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/daily-achievement-rate")
@RequiredArgsConstructor
public class PaintingDailyAchievementRateController {

    private final PaintingDailyAchievementRateService dailyAchievementRateService;

    @GetMapping
    @Operation(summary = "获取喷涂车间当日计划达成率", description = "获取指定车间或所有车间的当日计划达成率数据")
    public ResponseUtils.Result<List<PaintingDailyAchievementRateDTO>> getDailyAchievementRate(
            @Parameter(description = "车间名称，可选参数") 
            @RequestParam(required = false) String workshop_name) {
        try {
            List<PaintingDailyAchievementRateDTO> dataList = dailyAchievementRateService.getDailyAchievementRate(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间当日计划达成率数据失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.CncEquipmentStatusDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncEquipmentStatus;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncEquipmentStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CncEquipmentStatusService {

    private final CncEquipmentStatusRepository equipmentStatusRepository;

    /**
     * 获取指定车间的设备状态分布数据
     */
    public List<CncEquipmentStatusDTO> getEquipmentStatus(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<CncEquipmentStatus> entities = equipmentStatusRepository.findLatestByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private CncEquipmentStatusDTO convertToDTO(CncEquipmentStatus entity) {
        CncEquipmentStatusDTO dto = new CncEquipmentStatusDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setStatus_name(entity.getStatusName());
        dto.setStatus_count(entity.getStatusCount());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.common.util;

import lombok.Data;
import java.util.List;

/**
 * 分页工具类
 */
public class PageUtils {
    
    /**
     * 分页结果类
     */
    @Data
    public static class PageResult<T> {
        private List<T> records;        // 数据列表
        private long total;             // 总记录数
        private long size;              // 每页大小
        private long current;           // 当前页码
        private long pages;             // 总页数
        private boolean hasNext;        // 是否有下一页
        private boolean hasPrevious;    // 是否有上一页
        
        public PageResult(List<T> records, long total, long size, long current) {
            this.records = records;
            this.total = total;
            this.size = size;
            this.current = current;
            this.pages = (total + size - 1) / size;
            this.hasNext = current < pages;
            this.hasPrevious = current > 1;
        }
    }
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> createPageResult(List<T> records, long total, long size, long current) {
        return new PageResult<>(records, total, size, current);
    }
    
    /**
     * 计算起始索引
     */
    public static long getStartIndex(long current, long size) {
        return (current - 1) * size;
    }
    
    /**
     * 计算结束索引
     */
    public static long getEndIndex(long current, long size) {
        return current * size;
    }
    
    /**
     * 计算总页数
     */
    public static long getTotalPages(long total, long size) {
        return (total + size - 1) / size;
    }
    
    /**
     * 验证页码是否有效
     */
    public static boolean isValidPage(long current, long totalPages) {
        return current > 0 && current <= totalPages;
    }
    
    /**
     * 获取有效的页码
     */
    public static long getValidPage(long current, long totalPages) {
        if (current < 1) {
            return 1;
        }
        return Math.min(current, totalPages);
    }
} 
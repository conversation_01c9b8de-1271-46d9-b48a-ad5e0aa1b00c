package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingPlanCompletionRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingPlanCompletionRate7Days;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingPlanCompletionRate7DaysRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingPlanCompletionRate7DaysService {

    private final PaintingPlanCompletionRate7DaysRepository planCompletionRate7DaysRepository;

    /**
     * 获取指定车间的计划达成率近7天数据
     */
    public List<PaintingPlanCompletionRate7DaysDTO> getPlanCompletionRate7Days(String workshopName) {
        if (workshopName == null || workshopName.trim().isEmpty()) {
            throw new IllegalArgumentException("车间名称不能为空");
        }

        List<PaintingPlanCompletionRate7Days> entities = planCompletionRate7DaysRepository.findLatest7RecordsByWorkshop(workshopName);
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingPlanCompletionRate7DaysDTO convertToDTO(PaintingPlanCompletionRate7Days entity) {
        PaintingPlanCompletionRate7DaysDTO dto = new PaintingPlanCompletionRate7DaysDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setCompletion_rate(entity.getCompletionRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncWorkshopStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cnc/workshop-status")
@RequiredArgsConstructor
@Tag(name = "CNC车间接口", description = "CNC车间设备状况相关接口")
public class CncWorkshopStatusController {

    private final CncWorkshopStatusService workshopStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间设备状况", description = "获取每个CNC车间最新的设备状况信息")
    public ResponseUtils.Result<List<CncWorkshopStatusDTO>> getLatestWorkshopStatus() {
        try {
            List<CncWorkshopStatusDTO> data = workshopStatusService.getLatestWorkshopStatus();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间设备状况信息失败：" + e.getMessage());
        }
    }
}

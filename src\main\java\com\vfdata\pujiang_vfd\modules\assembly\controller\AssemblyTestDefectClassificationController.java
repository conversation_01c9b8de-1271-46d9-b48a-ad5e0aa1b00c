package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.TestDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyTestDefectClassificationService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/test-defect/classification")
@RequiredArgsConstructor
public class AssemblyTestDefectClassificationController {

    private final AssemblyTestDefectClassificationService testDefectClassificationService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新测试缺陷分类")
    public ResponseUtils.Result<List<TestDefectClassificationDTO>> getLatestTestDefectClassification(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<TestDefectClassificationDTO> classificationList = testDefectClassificationService.getLatestTestDefectClassification(workshop_name);
            return ResponseUtils.success(classificationList);
        } catch (Exception e) {
            return ResponseUtils.error("获取测试缺陷分类失败：" + e.getMessage());
        }
    }
} 
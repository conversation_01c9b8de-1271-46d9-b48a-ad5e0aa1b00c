package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldAttendanceRate;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldAttendanceRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MoldAttendanceRateService {

    private final MoldAttendanceRateRepository attendanceRateRepository;

    /**
     * 获取最新的出勤率信息
     */
    public MoldAttendanceRateDTO getLatestAttendanceRate() {
        MoldAttendanceRate entity = attendanceRateRepository.findLatestRecord();
        return entity != null ? convertToDTO(entity) : null;
    }

    /**
     * 实体转DTO
     */
    private MoldAttendanceRateDTO convertToDTO(MoldAttendanceRate entity) {
        MoldAttendanceRateDTO dto = new MoldAttendanceRateDTO();
        dto.setId(entity.getId());
        dto.setClerk_attendance_rate(entity.getClerkAttendanceRate());
        dto.setWorker_attendance_rate(entity.getWorkerAttendanceRate());
        dto.setClerk_attendance_num(entity.getClerkAttendanceNum());
        dto.setWorker_attendance_num(entity.getWorkerAttendanceNum());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

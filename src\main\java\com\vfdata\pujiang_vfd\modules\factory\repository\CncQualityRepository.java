package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.CncQuality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncQualityRepository extends JpaRepository<CncQuality, Long> {
    
    @Query("SELECT c FROM CncQuality c ORDER BY c.recordDate DESC, c.id DESC LIMIT 7")
    List<CncQuality> findLatestByWorkshopName();
} 
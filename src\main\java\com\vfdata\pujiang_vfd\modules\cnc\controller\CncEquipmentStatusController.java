package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncEquipmentStatusDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncEquipmentStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/equipment-status")
@RequiredArgsConstructor
public class CncEquipmentStatusController {

    private final CncEquipmentStatusService equipmentStatusService;

    @GetMapping
    @Operation(summary = "获取CNC车间设备状态分布", description = "获取指定CNC车间设备状态分布数据")
    public ResponseUtils.Result<List<CncEquipmentStatusDTO>> getEquipmentStatus(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<CncEquipmentStatusDTO> dataList = equipmentStatusService.getEquipmentStatus(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间设备状态分布数据失败：" + e.getMessage());
        }
    }
}

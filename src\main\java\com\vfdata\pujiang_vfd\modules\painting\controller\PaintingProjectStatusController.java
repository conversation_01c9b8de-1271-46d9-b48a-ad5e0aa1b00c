package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingProjectStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/project-status")
@RequiredArgsConstructor
public class PaintingProjectStatusController {

    private final PaintingProjectStatusService projectStatusService;

    @GetMapping
    @Operation(summary = "获取喷涂车间项目开机分布", description = "根据车间名称获取指定喷涂车间的项目开机分布数据")
    public ResponseUtils.Result<List<PaintingProjectStatusDTO>> getProjectStatus(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<PaintingProjectStatusDTO> dataList = projectStatusService.getProjectStatus(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间项目开机分布数据失败：" + e.getMessage());
        }
    }
}

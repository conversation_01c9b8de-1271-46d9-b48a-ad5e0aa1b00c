package com.vfdata.pujiang_vfd.modules.injection.repository;

import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface InjectionProjectStatusRepository extends JpaRepository<InjectionProjectStatus, Long> {
    
    @Query(value = "SELECT * FROM ioc_zscj_project_status WHERE workshop_name = :workshopName ORDER BY record_date DESC, id DESC", nativeQuery = true)
    List<InjectionProjectStatus> findByWorkshopName(@Param("workshopName") String workshopName);

    @Query(value = "SELECT * FROM ioc_zscj_project_status ORDER BY workshop_name, record_date DESC, id DESC", nativeQuery = true)
    List<InjectionProjectStatus> findAllProjectStatus();
} 
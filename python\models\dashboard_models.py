from sqlalchemy import Column, Integer, String, Date, DECIMAL, TIMESTAMP, Text, Float, DateTime
from database import Base

class CompanyInfo(Base):
    __tablename__ = "ioc_dp_companyinfo"

    id = Column(Integer, primary_key=True, index=True)
    serial_num = Column(Integer)
    company_info = Column(Text)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class Device(Base):
    __tablename__ = "ioc_dp_device"

    id = Column(Integer, primary_key=True, index=True)
    device_type = Column(String(255))
    device_num = Column(String(255))
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class EmployeeInOut(Base):
    __tablename__ = "ioc_dp_employeeinout"

    id = Column(Integer, primary_key=True, index=True)
    employee_name = Column(String(255))
    employee_addr = Column(String(255))
    employee_state = Column(String(255))
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class Hostel(Base):
    __tablename__ = "ioc_dp_hostel"

    id = Column(Integer, primary_key=True, index=True)
    hostel_num = Column(Integer)
    hostel_use = Column(Integer)
    hostel_remaining = Column(Integer)
    hostel_info = Column(Text)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class HostelInfo(Base):
    """宿舍文字介绍模型"""
    __tablename__ = "ioc_dp_hostel_info"
    
    id = Column(Integer, primary_key=True, index=True, comment="自增主键")
    hostel_info = Column(Text, comment="文字介绍")
    record_date = Column(Date, comment="日期")
    updateman = Column(String(20), comment="更新人")
    updatetime = Column(DateTime, comment="更新时间")
    reserved_field1 = Column(String, comment="备用字段1")
    reserved_field2 = Column(String, comment="备用字段2")
    reserved_field3 = Column(String, comment="备用字段3")

class ParkConstruct(Base):
    __tablename__ = "ioc_dp_parkconstruct"

    id = Column(Integer, primary_key=True, index=True)
    serial_number = Column(Integer)
    project_name = Column(String(255))
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class ParkInfo(Base):
    __tablename__ = "ioc_dp_parkinfo"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(String(255))
    num = Column(Float)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    record_date = Column(Date)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class PrivateRoom(Base):
    __tablename__ = "ioc_dp_privateroom"

    id = Column(Integer, primary_key=True, index=True)
    privateroom_num = Column(Integer)
    privateroom_use = Column(Integer)
    privateroom_remaining = Column(Integer)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class RealParking(Base):
    __tablename__ = "ioc_dp_realparking"

    id = Column(Integer, primary_key=True, index=True)
    parking_addr = Column(String(255))
    parking_num = Column(Integer)
    parking_use = Column(Integer)
    parking_remaining = Column(Integer)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class Technical(Base):
    __tablename__ = "ioc_dp_technical"

    id = Column(Integer, primary_key=True, index=True)
    personnel_type = Column(String(255))
    personnel_num = Column(Integer)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    record_date = Column(Date)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class TechnicalEdu(Base):
    __tablename__ = "ioc_dp_technical_edu"

    id = Column(Integer, primary_key=True, index=True)
    edu_background = Column(String(255))
    edu_num = Column(Integer)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))

class TechnicalPatent(Base):
    __tablename__ = "ioc_dp_technical_patent"

    id = Column(Integer, primary_key=True, index=True)
    patent_year = Column(String(255))
    patent_num = Column(Integer)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255)) 


class Venues(Base):
    __tablename__ = "ioc_dp_venues"

    id = Column(Integer, primary_key=True, index=True)
    venues_type = Column(String(255))
    venues_remaining = Column(Integer)
    venues_num = Column(Integer)
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))           
class StaffVisitor(Base):
    __tablename__ = "ioc_dp_staff_visitor"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(String(255))
    num = Column(Integer)           
    record_date = Column(Date)
    updateman = Column(String(20))
    updatetime = Column(TIMESTAMP)
    reserved_field1 = Column(String(255))
    reserved_field2 = Column(String(255))
    reserved_field3 = Column(String(255))   

class PersonnelDistribution(Base):
    """厂区人员分布模型"""
    __tablename__ = "ioc_dp_personnel_distribution"
    
    id = Column(Integer, primary_key=True, index=True, comment="自增主键")
    park_name = Column(String(20), comment="园区名称")
    total_num = Column(Integer, comment="园区总人数")
    manager_num = Column(Integer, comment="主管人数")
    employee_num = Column(Integer, comment="职员人数")
    general_worker_num = Column(Integer, comment="普工人数")
    updateman = Column(String(20), comment="更新人")
    updatetime = Column(DateTime, comment="更新时间")
    record_date = Column(Date, comment="日期")
    reserved_field1 = Column(String, comment="备用字段1")
    reserved_field2 = Column(String, comment="备用字段2")
    reserved_field3 = Column(String, comment="备用字段3")   

class Visitor(Base):
    """员工访客信息表"""
    __tablename__ = "ioc_dp_visitor"

    id = Column(Integer, primary_key=True, index=True, comment="自增主键")
    visitor_type = Column(String(255), comment="类型（在园员工、离园员工、访客数）")
    visitor_date = Column(TIMESTAMP, comment="访客日期")
    visitor_num = Column(Integer, comment="人数")
    record_date = Column(Date, comment="日期")
    updateman = Column(String(20), comment="更新人")
    updatetime = Column(TIMESTAMP, comment="更新时间")
    reserved_field1 = Column(String(255), comment="供应商数量")
    reserved_field2 = Column(String(255), comment="客户数量")
    reserved_field3 = Column(String(255), comment="其他访客数量")   

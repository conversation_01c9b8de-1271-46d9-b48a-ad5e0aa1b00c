package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.WorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyWorkshopStatus;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyWorkshopStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyWorkshopStatusService {

    private final AssemblyWorkshopStatusRepository workshopStatusRepository;

    public List<WorkshopStatusDTO> getLatestWorkshopStatus(String workshopName) {
        List<AssemblyWorkshopStatus> statusList;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            statusList = workshopStatusRepository.findLatestByWorkshopName(workshopName);
        } else {
            statusList = workshopStatusRepository.findLatest();
        }

        if (statusList.isEmpty()) {
            throw new RuntimeException("未找到车间状态记录");
        }

        return statusList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private WorkshopStatusDTO convertToDTO(AssemblyWorkshopStatus status) {
        WorkshopStatusDTO dto = new WorkshopStatusDTO();
        dto.setId(status.getId());
        dto.setWorkshop_name(status.getWorkshopName());
        dto.setTotal_equipment(status.getTotalEquipment());
        dto.setOperating_equipment(status.getOperatingEquipment());
        dto.setOperating_rate(status.getOperatingRate());
        dto.setRecord_date(status.getRecordDate());
        dto.setUpdated_by(status.getUpdatedBy());
        dto.setUpdated_at(status.getUpdatedAt());
        dto.setReserved_field1(status.getReservedField1());
        dto.setReserved_field2(status.getReservedField2());
        dto.setReserved_field3(status.getReservedField3());
        return dto;
    }
} 
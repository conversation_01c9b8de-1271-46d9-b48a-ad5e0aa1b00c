package com.vfdata.pujiang_vfd.modules.cnc.repository;

import com.vfdata.pujiang_vfd.modules.cnc.entity.CncProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CncProjectStatusRepository extends JpaRepository<CncProjectStatus, Long> {

    /**
     * 获取指定车间的所有项目开机分布数据
     */
    @Query("SELECT c FROM CncProjectStatus c WHERE c.workshopName = :workshopName")
    List<CncProjectStatus> findByWorkshop(@Param("workshopName") String workshopName);
}

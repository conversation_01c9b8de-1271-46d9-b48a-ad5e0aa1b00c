package com.vfdata.pujiang_vfd.modules.factory.repository;

import com.vfdata.pujiang_vfd.modules.factory.entity.WorkshopPersonnelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkshopPersonnelInfoRepository extends JpaRepository<WorkshopPersonnelInfo, Long> {
    
    @Query("SELECT w FROM WorkshopPersonnelInfo w WHERE w.recordDate = (SELECT MAX(w2.recordDate) FROM WorkshopPersonnelInfo w2)")
    List<WorkshopPersonnelInfo> findLatestRecords();
    
    @Query("SELECT w FROM WorkshopPersonnelInfo w WHERE w.workshopName = :workshopName AND w.recordDate = (SELECT MAX(w2.recordDate) FROM WorkshopPersonnelInfo w2 WHERE w2.workshopName = :workshopName)")
    List<WorkshopPersonnelInfo> findLatestRecordsByWorkshopName(@Param("workshopName") String workshopName);
} 
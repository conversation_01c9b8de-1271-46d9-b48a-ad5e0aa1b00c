package com.vfdata.pujiang_vfd.modules.assembly.controller;

import com.vfdata.pujiang_vfd.modules.assembly.dto.FqcDefectAnalysisDTO;
import com.vfdata.pujiang_vfd.modules.assembly.service.AssemblyFqcDefectAnalysisService;
import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "组装车间接口")
@RestController
@RequestMapping("/assembly/fqc/defect-analysis")
@RequiredArgsConstructor
public class AssemblyFqcDefectAnalysisController {

    private final AssemblyFqcDefectAnalysisService fqcDefectAnalysisService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新FQC缺陷分析")
    public ResponseUtils.Result<List<FqcDefectAnalysisDTO>> getLatestFqcDefectAnalysis(
            @RequestParam(required = false) String workshop_name) {
        try {
            List<FqcDefectAnalysisDTO> analysisList = fqcDefectAnalysisService.getLatestFqcDefectAnalysis(workshop_name);
            return ResponseUtils.success(analysisList);
        } catch (Exception e) {
            return ResponseUtils.error("获取FQC缺陷分析失败：" + e.getMessage());
        }
    }
}
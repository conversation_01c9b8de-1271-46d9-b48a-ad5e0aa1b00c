package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.StaffVisitor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface StaffVisitorRepository extends JpaRepository<StaffVisitor, Long> {
    
    @Query("SELECT s FROM StaffVisitor s WHERE s.id IN (" +
           "SELECT MAX(s1.id) FROM StaffVisitor s1 WHERE s1.type = '在园员工' " +
           "UNION " +
           "SELECT MAX(s2.id) FROM StaffVisitor s2 WHERE s2.type = '访客数' " +
           "UNION " +
           "SELECT MAX(s3.id) FROM StaffVisitor s3 WHERE s3.type = '离园员工'" +
           ")")
    List<StaffVisitor> findLatest();
    
    List<StaffVisitor> findAllByOrderByRecordDateDesc();
}

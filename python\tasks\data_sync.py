import httpx
import asyncio
from datetime import datetime, date
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from database import SessionLocal
from models import dashboard_models, safety_models
from utils.logger import logger

class DataSyncTask:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0, limits=httpx.Limits(max_connections=20))
        self.base_url = "http://10.134.160.63:8081"

    async def fetch_data(self, endpoint: str) -> Dict[str, Any]:
        """从外部接口获取数据"""
        try:
            logger.info(f"开始从接口获取数据: {endpoint}")
            # 修改为POST请求，添加超时控制
            response = await self.client.post(
                f"{self.base_url}{endpoint}", 
                json={"page": 1, "size": 1000},
                timeout=60.0  # 增加超时时间
            )
            response.raise_for_status()
            response_data = response.json()
            logger.info(f"成功从接口获取数据: {endpoint}, 数据量: {len(response_data.get('data', []))}")
            # 返回data字段中的数据
            return response_data.get("data", [])
        except Exception as e:
            logger.error(f"从接口获取数据失败: {endpoint}, 错误: {str(e)}")
            return None

    async def sync_to_table(self, endpoint: str, model_class: str, data_mapping: Dict, unique_fields: List[str] = None, model_module: str = "dashboard_models", batch_size: int = 1000, skip_existing: bool = True):
        """
        通用的数据同步方法
        :param endpoint: API端点
        :param model_class: 模型类名称
        :param data_mapping: 数据映射配置
        :param unique_fields: 用于判断记录唯一性的字段列表，如果为None则使用id
        :param model_module: 模型所在的模块名称，默认为"dashboard_models"
        :param batch_size: 批量处理的大小，默认1000条
        :param skip_existing: 是否跳过已存在的记录，默认为True
        """
        start_time = datetime.now()
        logger.info(f"开始同步数据到 {model_class}, 时间: {start_time}")
        
        try:
            data = await self.fetch_data(endpoint)
            if not data:
                logger.error(f"从接口获取数据为空: {endpoint}")
                return
            
            data_count = len(data)
            logger.info(f"准备同步数据到模型 {model_class}, 数据量: {data_count}")
            
            db = SessionLocal()
            try:
                # 获取对应的模型类
                if model_module == "dashboard_models":
                    model_class = getattr(dashboard_models, model_class)
                else:
                    model_class = getattr(safety_models, model_class)
                
                logger.info(f"开始预处理数据: {model_class.__name__}")
                # 预处理所有数据
                all_records = []
                existing_ids = set()
                
                # 构建所有记录的数据
                for item in data:
                    record_data = {}
                    for db_field, api_field in data_mapping.items():
                        if isinstance(api_field, str):
                            record_data[db_field] = item.get(api_field)
                        elif callable(api_field):
                            try:
                                record_data[db_field] = api_field(item)
                            except Exception as e:
                                logger.error(f"字段转换失败: {db_field}, 错误: {str(e)}, 数据: {item}")
                                record_data[db_field] = None
                    all_records.append(record_data)
                
                logger.info(f"数据预处理完成, 开始查询现有记录")
                
                # 如果需要跳过已存在的记录，获取所有现有ID
                if skip_existing and 'id' in unique_fields:
                    # 获取所有ID
                    ids = [r['id'] for r in all_records if 'id' in r and r['id'] is not None]
                    if ids:
                        # 查询已存在的ID
                        existing_records = db.query(model_class.id).filter(model_class.id.in_(ids)).all()
                        existing_ids = set(r[0] for r in existing_records)
                        logger.info(f"找到已存在的ID: {len(existing_ids)}个")
                
                # 批量处理记录
                records_to_insert = []
                skipped_count = 0
                inserted_count = 0
                
                logger.info(f"开始处理记录, 总数: {len(all_records)}")
                
                for i, record_data in enumerate(all_records):
                    # 每处理1000条记录输出一次日志
                    if i > 0 and i % 1000 == 0:
                        logger.info(f"已处理 {i}/{len(all_records)} 条记录, 跳过: {skipped_count}, 新增: {inserted_count}")
                    
                    # 如果ID已存在且需要跳过，则跳过此记录
                    if skip_existing and 'id' in record_data and record_data['id'] in existing_ids:
                        skipped_count += 1
                        continue
                    
                    # 创建新记录
                    new_record = model_class(**record_data)
                    records_to_insert.append(new_record)
                    inserted_count += 1
                    
                    # 批量提交插入
                    if len(records_to_insert) >= batch_size:
                        logger.info(f"批量插入记录, 数量: {len(records_to_insert)}")
                        db.bulk_save_objects(records_to_insert)
                        db.commit()
                        records_to_insert = []
                
                # 处理剩余记录
                if records_to_insert:
                    logger.info(f"批量插入剩余记录, 数量: {len(records_to_insert)}")
                    db.bulk_save_objects(records_to_insert)
                
                # 最后提交所有更新
                logger.info(f"提交最终事务")
                db.commit()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.info(f"成功同步数据到模型 {model_class.__name__}: 跳过 {skipped_count} 条记录，新增 {inserted_count} 条记录, 耗时: {duration}秒")
            
            except Exception as e:
                db.rollback()
                logger.error(f"保存数据失败: {str(e)}")
                raise
            finally:
                db.close()
        except Exception as e:
            logger.error(f"同步数据失败: {str(e)}")
            raise

    async def sync_employee_inout(self):
        """同步员工出入记录数据"""
        mapping = {
            'id': 'id',
            'employee_name': 'employeeName',
            'employee_addr': 'employeeAddr',
            'employee_state': 'employeeState',
            'record_date': 'recordDate',
            'updateman': 'updateman',
            'updatetime': 'updatetime',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用ID作为唯一标识，忽略重复记录
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/employeeInout",
            model_class="EmployeeInOut",
            data_mapping=mapping,
            unique_fields=unique_fields,
            batch_size=500  # 减小批量大小，提高处理速度
        )

    async def sync_real_parking(self):
        """同步实时车位数据"""
        mapping = {
            'id': 'id',
            'parking_addr': 'parkingAddr',
            'parking_num': 'parkingNum',
            'parking_use': 'parkingUse',
            'parking_remaining': 'parkingRemaining',
            'record_date': 'recordDate',
            'updateman': 'updateman',
            'updatetime': 'updatetime',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用停车场地址和日期作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/realParking",
            model_class="RealParking",
            data_mapping=mapping,
            unique_fields=unique_fields
        )

    async def sync_private_room(self):
        """同步盈旺之家包间数量数据"""
        mapping = {
            'id': 'id',
            'privateroom_num': 'privateroomNum',
            'privateroom_use': 'privateroomUse',
            'privateroom_remaining': 'privateroomRemaining',
            'record_date': lambda item: datetime.strptime(item['recordDate'], '%Y-%m-%d').date() if item['recordDate'] else None,
            'updateman': 'updateman',
            'updatetime': 'updatetime',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用日期作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/privateRoom",
            model_class="PrivateRoom",
            data_mapping=mapping,
            unique_fields=unique_fields
        )

    async def sync_hostel(self):
        """同步宿舍数量及文字介绍数据"""
        mapping = {
            'id': 'id',
            'hostel_num': 'hostelNum',
            'hostel_use': 'hosteluse',
            'hostel_remaining': 'hostelRemaining',
            'hostel_info': 'hostelInfo',
            'record_date': lambda item: datetime.strptime(item['recordDate'], '%Y-%m-%d').date() if item['recordDate'] else None,
            'updateman': 'updateman',
            'updatetime': 'updatetime',
            'reserved_field1': 'reservedFeld1',
            'reserved_field2': 'reservedFeld2',
            'reserved_field3': 'reservedFeld3'
        }
        # 使用日期作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/hostel",
            model_class="Hostel",
            data_mapping=mapping,
            unique_fields=unique_fields
        )

    async def sync_staff_visitor(self):
        """同步员工访客信息数据"""
        mapping = {
            'id': 'id',
            'type': 'type',
            'num': 'num',
            'record_date': lambda item: datetime.strptime(item['recordDate'], '%Y-%m-%d').date() if item['recordDate'] else None,
            'updateman': 'updateman',
            'updatetime': 'updatetime',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用类型和日期作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/staffVisitor",
            model_class="StaffVisitor",
            data_mapping=mapping,
            unique_fields=unique_fields
        )

    async def sync_venues(self):
        """同步盈旺之家娱乐会议场所数据"""
        mapping = {
            'id': 'id',
            'venues_type': 'venuesType',
            'venues_remaining': 'venuesRemaining',
            'venues_num': 'venuesNum',
            'record_date': lambda item: datetime.strptime(item['recordDate'], '%Y-%m-%d').date() if item['recordDate'] else None,
            'updateman': 'updateman',
            'updatetime': 'updatetime',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用场所类型和日期作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/venues",
            model_class="Venues",
            data_mapping=mapping,
            unique_fields=unique_fields
        )

    async def sync_visitor_records(self):
        """同步访客记录数据"""
        mapping = {
            'id': 'id',
            'visitor_name': 'visitorName',
            'channel_name': 'channelName',
            'visit_time': lambda item: datetime.strptime(item['visitTime'], '%Y-%m-%d %H:%M:%S') if item['visitTime'] else None,
            'status': 'status',
            'updated_by': 'updatedBy',
            'update_date': lambda item: datetime.strptime(item['updateDate'], '%Y-%m-%d').date() if item['updateDate'] else None,
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用访客姓名、通道名称和访问时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/visitorRecords",
            model_class="VisitorRecord",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_vehicle_records(self):
        """同步车辆进出记录数据"""
        mapping = {
            'id': 'id',
            'license_plate': 'licensePlate',
            'channel_name': 'channelName',
            'entry_time': lambda item: datetime.strptime(item['entryTime'], '%Y-%m-%d %H:%M:%S') if item['entryTime'] else None,
            'status': 'status',
            'updated_by': 'updatedBy',
            'update_date': lambda item: datetime.strptime(item['updateDate'], '%Y-%m-%d').date() if item['updateDate'] else None,
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用车牌号、通道名称和进入时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/vehicleRecords",
            model_class="VehicleRecord",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_wastewater_monitoring(self):
        """同步废水监控数据"""
        mapping = {
            'id': 'id',
            'cod': 'cod',
            'ammonia_nitrogen': 'ammoniaNitrogen',
            'record_time': lambda item: datetime.strptime(item['recordTime'], '%Y-%m-%d %H:%M:%S') if item['recordTime'] else None,
            'updated_by': 'updatedBy',
            'update_date': lambda item: datetime.strptime(item['updateDate'], '%Y-%m-%d').date() if item['updateDate'] else None,
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用记录时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/wastewaterMonitoring",
            model_class="WastewaterMonitoring",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_fire_equipment_info(self):
        """同步消防设备信息数据"""
        mapping = {
            'id': 'id',
            'fire_extinguisher': 'fireExtinguisher',
            'fire_sprinkler': 'fireSprinkler',
            'fire_hydrant': 'fireHydrant',
            'fire_alarm_device': 'fireAlarmDevice',
            'record_date': lambda item: datetime.strptime(item['recordDate'], '%Y-%m-%d %H:%M:%S') if item['recordDate'] else None,
            'updated_by': 'updatedBy',
            'update_date': lambda item: datetime.strptime(item['updateDate'], '%Y-%m-%d').date() if item['updateDate'] else None,
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用记录日期作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/fireEquipmentInfo",
            model_class="FireEquipmentInfo",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_energy_consumption_records(self):
        """同步能耗统计记录数据"""
        mapping = {
            'id': 'id',
            'water_usage': 'waterUsage',
            'electricity_usage': 'electricityUsage',
            'gas_usage': 'gasUsage',
            'record_time': lambda item: datetime.strptime(item['recordTime'], '%Y-%m-%d %H:%M:%S') if item['recordTime'] else None,
            'updated_by': 'updatedBy',
            'update_date': lambda item: datetime.strptime(item['updateDate'], '%Y-%m-%d').date() if item['updateDate'] else None,
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用记录时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/energyConsumptionRecords",
            model_class="EnergyConsumptionRecord",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_actual_energy_consumption(self):
        """同步实际能耗统计数据"""
        mapping = {
            'id': 'id',
            'water_usage': 'waterUsage',
            'electricity_usage': 'electricityUsage',
            'gas_usage': 'gasUsage',
            'record_time': lambda item: datetime.strptime(item['recordTime'], '%Y-%m-%d %H:%M:%S') if item['recordTime'] else None,
            'updated_by': 'updatedBy',
            'update_date': lambda item: datetime.strptime(item['updateDate'], '%Y-%m-%d').date() if item['updateDate'] else None,
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用记录时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/actualEnergyConsumption",
            model_class="ActualEnergyConsumption",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_photovoltaic_energy_metrics(self):
        """同步光伏能源指标数据"""
        mapping = {
            'id': 'id',
            'daily_generation': 'dailyGeneration',
            'total_generation': 'totalGeneration',
            'daily_co2_reduction': 'dailyCo2Reduction',
            'record_time': 'recordTime',
            'updated_by': 'updatedBy',
            'update_date': 'updateDate',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用记录时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/photovoltaicEnergyMetrics",
            model_class="PhotovoltaicEnergyMetric",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_photovoltaic_energy_stats(self):
        """同步光伏能源统计数据"""
        mapping = {
            'id': 'id',
            'daily_generation': 'dailyGeneration',
            'record_time': 'recordTime',
            'updated_by': 'updatedBy',
            'update_date': 'updateDate',
            'reserved_field1': 'reservedField1',
            'reserved_field2': 'reservedField2',
            'reserved_field3': 'reservedField3'
        }
        # 使用记录时间作为唯一标识
        unique_fields = ['id']
        await self.sync_to_table(
            endpoint="/ioc/bigScreen/photovoltaicEnergyStats",
            model_class="PhotovoltaicEnergyStat",
            data_mapping=mapping,
            unique_fields=unique_fields,
            model_module="safety_models"
        )

    async def sync_staff_visitor_info_monthly(self):
        """同步员工访客月度统计数据"""
        try:
            endpoint = "/ioc/bigScreen/staffVisitorInfo?type=month"
            data = await self.fetch_data(endpoint)
            if not data:
                logger.error(f"从接口获取数据为空: {endpoint}")
                return
            
            db = SessionLocal()
            try:
                # 获取模型类
                model_class = getattr(dashboard_models, "Visitor")
                
                for item in data:
                    # 通过yearmonth查找是否已存在对应记录
                    year_month_date = datetime.strptime(item['yearmonth'] + '-01', '%Y-%m-%d').date() if item['yearmonth'] else None
                    
                    if year_month_date:
                        existing_record = db.query(model_class).filter(
                            model_class.visitor_type == '月度访客统计',
                            model_class.record_date == year_month_date
                        ).first()
                        
                        # 构建记录数据
                        record_data = {
                            'visitor_type': '月度访客统计',
                            'visitor_date': datetime.now(),  # 使用当前时间作为访客日期
                            'visitor_num': 0,  # 默认值
                            'record_date': year_month_date,
                            'updateman': 'system',
                            'updatetime': datetime.now(),
                            'supplier_count': str(item.get('supplier', 0)),
                            'customer_count': str(item.get('customer', 0)),
                            'other_count': str(item.get('other', 0))
                        }
                        
                        if existing_record:
                            # 更新现有记录
                            for key, value in record_data.items():
                                setattr(existing_record, key, value)
                            logger.info(f"更新月度访客统计记录: {item['yearmonth']}")
                        else:
                            # 创建新记录
                            new_record = model_class(**record_data)
                            db.add(new_record)
                            logger.info(f"创建新月度访客统计记录: {item['yearmonth']}")
                
                # 提交更改
                db.commit()
                logger.info(f"员工访客月度统计数据同步完成")
                
            except Exception as e:
                db.rollback()
                logger.error(f"同步员工访客月度统计数据失败: {str(e)}")
                raise
            finally:
                db.close()
        except Exception as e:
            logger.error(f"员工访客月度统计数据同步失败: {str(e)}")
            raise

    async def sync_park_distribution(self):
        """同步厂区人员分布数据"""
        try:
            endpoint = "/ioc/bigScreen/parkDistribution"
            data = await self.fetch_data(endpoint)
            if not data:
                logger.error(f"从接口获取数据为空: {endpoint}")
                return
            
            db = SessionLocal()
            try:
                # 获取模型类
                model_class = getattr(dashboard_models, "PersonnelDistribution")
                
                # 获取当前日期
                current_date = datetime.now().date()
                
                # 处理浦江和惠州两个园区的数据
                for park_id, park_name in [(1, "浦江"), (2, "惠州")]:
                    # 初始化数据
                    park_data = {
                        'id': park_id,
                        'park_name': park_name,
                        'manager_num': 0,
                        'employee_num': 0,
                        'general_worker_num': 0,
                        'total_num': 0,
                        'updateman': 'system',
                        'updatetime': datetime.now(),
                        'record_date': current_date
                    }
                    
                    # 统计各类型人数
                    for item in data:
                        if item['position_level'] == '主管':
                            park_data['manager_num'] = item['pujiang' if park_name == '浦江' else 'huizhou']
                        elif item['position_level'] == '职员':
                            park_data['employee_num'] = item['pujiang' if park_name == '浦江' else 'huizhou']
                        elif item['position_level'] == '普工':
                            park_data['general_worker_num'] = item['pujiang' if park_name == '浦江' else 'huizhou']
                    
                    # 计算总人数
                    park_data['total_num'] = (
                        park_data['manager_num'] + 
                        park_data['employee_num'] + 
                        park_data['general_worker_num']
                    )
                    
                    # 查询现有记录
                    existing_record = db.query(model_class).filter(
                        model_class.id == park_id
                    ).first()
                    
                    if existing_record:
                        # 更新现有记录
                        for key, value in park_data.items():
                            setattr(existing_record, key, value)
                        logger.info(f"更新{park_name}园区人员分布数据")
                    else:
                        # 创建新记录
                        new_record = model_class(**park_data)
                        db.add(new_record)
                        logger.info(f"创建{park_name}园区人员分布数据")
                
                # 提交更改
                db.commit()
                logger.info("厂区人员分布数据同步完成")
                
            except Exception as e:
                db.rollback()
                logger.error(f"同步厂区人员分布数据失败: {str(e)}")
                raise
            finally:
                db.close()
        except Exception as e:
            logger.error(f"厂区人员分布数据同步失败: {str(e)}")
            raise

    async def sync_all(self):
        """并行同步所有数据"""
        total_start_time = datetime.now()
        logger.info(f"开始全部同步任务, 时间: {total_start_time}")
        
        try:
            # 将任务分组，避免同时运行太多任务
            task_groups = [
                # 第一组：基础设施数据
                [
                    self.sync_real_parking(),
                    self.sync_private_room(),
                    self.sync_hostel(),
                    self.sync_venues(),
                    self.sync_staff_visitor_info_monthly(),
                    self.sync_park_distribution(),  # 添加新的同步任务
                ],
                # 第二组：人员相关数据
                [
                    self.sync_employee_inout(),
                    self.sync_staff_visitor(),
                    self.sync_visitor_records(),
                ],
                # 第三组：安全监控数据
                [
                    self.sync_vehicle_records(),
                    # self.sync_wastewater_monitoring(), # 暂停同步
                    self.sync_fire_equipment_info(),
                ],
                # 第四组：能源数据
                [
                    self.sync_energy_consumption_records(),
                    self.sync_actual_energy_consumption(),
                    # self.sync_photovoltaic_energy_metrics(), # 暂停同步
                    # self.sync_photovoltaic_energy_stats() # 暂停同步
                ]
            ]
            
            # 按组执行任务
            for i, group in enumerate(task_groups, 1):
                group_start_time = datetime.now()
                logger.info(f"开始执行第{i}组同步任务, 时间: {group_start_time}")
                
                try:
                    # 设置超时时间为10分钟
                    await asyncio.wait_for(asyncio.gather(*group), timeout=600)
                    
                    group_end_time = datetime.now()
                    duration = (group_end_time - group_start_time).total_seconds()
                    logger.info(f"第{i}组同步任务完成, 耗时: {duration}秒")
                    
                    if i < len(task_groups):  # 如果不是最后一组，添加延迟
                        logger.info(f"等待2秒后开始下一组任务")
                        await asyncio.sleep(2)  # 在组之间添加短暂延迟，避免资源竞争
                except asyncio.TimeoutError:
                    logger.error(f"第{i}组同步任务执行超时")
                    continue  # 继续执行下一组任务
                except Exception as e:
                    logger.error(f"第{i}组同步任务执行失败: {str(e)}")
                    continue  # 继续执行下一组任务
                
        except Exception as e:
            logger.error(f"同步任务执行失败: {str(e)}")
        finally:
            await self.client.aclose()
            
            total_end_time = datetime.now()
            total_duration = (total_end_time - total_start_time).total_seconds()
            logger.info(f"全部同步任务完成, 总耗时: {total_duration}秒")

def run_sync():
    """运行数据同步任务"""
    task = DataSyncTask()
    asyncio.run(task.sync_all())

async def run_sync_with_interval(interval_seconds=3600):
    """
    定时运行数据同步任务
    :param interval_seconds: 同步间隔，单位为秒，默认3600秒（1小时）
    """
    while True:
        start_time = datetime.now()
        logger.info(f"开始定时同步任务, 时间: {start_time}, 间隔: {interval_seconds}秒")
        
        task = DataSyncTask()
        try:
            await task.sync_all()
        except Exception as e:
            logger.error(f"定时同步任务执行失败: {str(e)}")
        
        # 计算下次执行时间
        elapsed = (datetime.now() - start_time).total_seconds()
        wait_time = max(0, interval_seconds - elapsed)
        
        logger.info(f"同步任务完成，将在{wait_time}秒后再次执行")
        await asyncio.sleep(wait_time)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--daemon":
        # 作为守护进程运行，定时执行
        interval = 3600  # 默认1小时
        if len(sys.argv) > 2:
            try:
                interval = int(sys.argv[2])
            except ValueError:
                logger.error(f"无效的间隔时间: {sys.argv[2]}，使用默认值: {interval}秒")
        
        logger.info(f"以守护进程模式启动，同步间隔: {interval}秒")
        asyncio.run(run_sync_with_interval(interval))
    else:
        # 单次执行
        run_sync() 
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
from database import get_db
from models import assembly_models
from schemas import assembly_schemas
from schemas.common_schemas import Response, ListResponse, ErrorResponse
from datetime import datetime

router = APIRouter(
    prefix="/assembly",
    tags=["装配车间"],
    responses={
        200: {"description": "操作成功"},
        404: {"description": "未找到数据", "model": ErrorResponse},
        500: {"description": "服务器内部错误", "model": ErrorResponse}
    }
)

# 生产进度相关接口
@router.post("/production-progress/", 
    response_model=Response[assembly_schemas.ProductionProgress],
    summary="创建生产进度",
    description="创建新的生产进度记录",
    response_description="返回创建的生产进度")
def create_production_progress(progress: assembly_schemas.ProductionProgressCreate, db: Session = Depends(get_db)):
    """
    创建生产进度
    
    参数说明：
    - **product_name**: 产品名称
    - **planned_quantity**: 计划数量
    - **completed_quantity**: 完成数量
    - **completion_rate**: 完成率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_progress = assembly_models.ProductionProgress(**progress.dict())
        db.add(db_progress)
        db.commit()
        db.refresh(db_progress)
        return Response(data=db_progress)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/production-progress/latest/", 
    response_model=Response[assembly_schemas.ProductionProgress],
    summary="获取最新生产进度",
    description="获取最新的生产进度记录",
    response_description="返回最新生产进度")
def read_latest_production_progress(db: Session = Depends(get_db)):
    """获取最新生产进度"""
    latest = db.query(assembly_models.ProductionProgress).order_by(
        assembly_models.ProductionProgress.record_date.desc()
    ).first()
    if latest is None:
        return ErrorResponse(
            code=404,
            message="没有找到生产进度",
            success=False
        )
    return Response(data=latest)

# 装配线状态相关接口
@router.post("/line-status/", 
    response_model=Response[assembly_schemas.LineStatus],
    summary="创建装配线状态",
    description="创建新的装配线状态记录",
    response_description="返回创建的装配线状态")
def create_line_status(status: assembly_schemas.LineStatusCreate, db: Session = Depends(get_db)):
    """
    创建装配线状态
    
    参数说明：
    - **line_name**: 装配线名称
    - **status**: 运行状态
    - **efficiency**: 效率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_status = assembly_models.LineStatus(**status.dict())
        db.add(db_status)
        db.commit()
        db.refresh(db_status)
        return Response(data=db_status)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/line-status/", 
    response_model=ListResponse[assembly_schemas.LineStatus],
    summary="获取装配线状态列表",
    description="获取所有装配线的状态记录",
    response_description="返回装配线状态列表")
def read_line_status(db: Session = Depends(get_db)):
    """获取所有装配线状态列表"""
    result = db.query(assembly_models.LineStatus).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 质量检验相关接口
@router.post("/quality-inspection/", 
    response_model=Response[assembly_schemas.QualityInspection],
    summary="创建质量检验记录",
    description="创建新的质量检验记录",
    response_description="返回创建的质量检验记录")
def create_quality_inspection(inspection: assembly_schemas.QualityInspectionCreate, db: Session = Depends(get_db)):
    """
    创建质量检验记录
    
    参数说明：
    - **product_name**: 产品名称
    - **batch_number**: 批次号
    - **inspection_quantity**: 检验数量
    - **defect_quantity**: 不良品数量
    - **pass_rate**: 合格率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_inspection = assembly_models.QualityInspection(**inspection.dict())
        db.add(db_inspection)
        db.commit()
        db.refresh(db_inspection)
        return Response(data=db_inspection)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/quality-inspection/", 
    response_model=ListResponse[assembly_schemas.QualityInspection],
    summary="获取质量检验记录列表",
    description="获取所有质量检验记录",
    response_description="返回质量检验记录列表")
def read_quality_inspection(db: Session = Depends(get_db)):
    """获取所有质量检验记录列表"""
    result = db.query(assembly_models.QualityInspection).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 人员效率相关接口
@router.post("/staff-efficiency/", 
    response_model=Response[assembly_schemas.StaffEfficiency],
    summary="创建人员效率记录",
    description="创建新的人员效率记录",
    response_description="返回创建的人员效率记录")
def create_staff_efficiency(efficiency: assembly_schemas.StaffEfficiencyCreate, db: Session = Depends(get_db)):
    """
    创建人员效率记录
    
    参数说明：
    - **staff_name**: 人员姓名
    - **position**: 岗位
    - **output_quantity**: 产出数量
    - **working_hours**: 工作时长
    - **efficiency_rate**: 效率指标
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    try:
        db_efficiency = assembly_models.StaffEfficiency(**efficiency.dict())
        db.add(db_efficiency)
        db.commit()
        db.refresh(db_efficiency)
        return Response(data=db_efficiency)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/staff-efficiency/", 
    response_model=ListResponse[assembly_schemas.StaffEfficiency],
    summary="获取人员效率记录列表",
    description="获取所有人员效率记录",
    response_description="返回人员效率记录列表")
def read_staff_efficiency(db: Session = Depends(get_db)):
    """获取所有人员效率记录列表"""
    result = db.query(assembly_models.StaffEfficiency).all()
    total = len(result)
    return ListResponse(
        data=result,
        total=total,
        message=f"成功获取{total}条记录"
    )

# 人员信息相关接口
@router.post("/personnel/", response_model=assembly_schemas.AssemblyPersonnelInfo,
    summary="创建人员信息",
    description="创建新的组装车间人员信息记录",
    response_description="返回创建的人员信息")
def create_personnel_info(personnel: assembly_schemas.AssemblyPersonnelInfoCreate, db: Session = Depends(get_db)):
    """
    创建组装车间人员信息
    
    参数说明：
    - **personnel_type**: 人员类型（如：正式工、临时工等）
    - **staff_count**: 职员数量
    - **general_worker_count**: 普工数量
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    db_personnel = assembly_models.AssemblyPersonnelInfo(**personnel.dict())
    db.add(db_personnel)
    db.commit()
    db.refresh(db_personnel)
    return db_personnel

@router.get("/personnel/", response_model=List[assembly_schemas.AssemblyPersonnelInfo],
    summary="获取人员信息列表",
    description="获取组装车间所有人员信息记录",
    response_description="返回人员信息列表")
def read_personnel_info(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """
    获取组装车间人员信息列表
    
    参数说明：
    - **skip**: 跳过记录数
    - **limit**: 返回记录数限制
    """
    return db.query(assembly_models.AssemblyPersonnelInfo).offset(skip).limit(limit).all()

# 出勤率相关接口
@router.post("/attendance/", response_model=assembly_schemas.AssemblyAttendanceRate,
    summary="创建出勤率记录",
    description="创建新的组装车间出勤率记录",
    response_description="返回创建的出勤率记录")
def create_attendance_rate(attendance: assembly_schemas.AssemblyAttendanceRateCreate, db: Session = Depends(get_db)):
    """
    创建组装车间出勤率记录
    
    参数说明：
    - **clerk_attendance_rate**: 职员出勤率
    - **worker_attendance_rate**: 工人出勤率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    db_attendance = assembly_models.AssemblyAttendanceRate(**attendance.dict())
    db.add(db_attendance)
    db.commit()
    db.refresh(db_attendance)
    return db_attendance

@router.get("/attendance/latest/", response_model=assembly_schemas.AssemblyAttendanceRate,
    summary="获取最新出勤率",
    description="获取组装车间最新的出勤率记录",
    response_description="返回最新出勤率记录")
def read_latest_attendance_rate(db: Session = Depends(get_db)):
    """获取组装车间最新出勤率记录"""
    latest_attendance = db.query(assembly_models.AssemblyAttendanceRate).order_by(
        assembly_models.AssemblyAttendanceRate.record_date.desc()
    ).first()
    if latest_attendance is None:
        raise HTTPException(status_code=404, detail="没有找到出勤率记录")
    return latest_attendance

# FQC质量相关接口
@router.post("/fqc/quality/", response_model=assembly_schemas.AssemblyFQCQualityRate,
    summary="创建FQC质量记录",
    description="创建新的组装车间FQC质量记录",
    response_description="返回创建的FQC质量记录")
def create_fqc_quality_rate(quality: assembly_schemas.AssemblyFQCQualityRateCreate, db: Session = Depends(get_db)):
    """
    创建组装车间FQC质量记录
    
    参数说明：
    - **workshop_name**: 车间名称
    - **quality_rate**: 质量合格率
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    db_quality = assembly_models.AssemblyFQCQualityRate(**quality.dict())
    db.add(db_quality)
    db.commit()
    db.refresh(db_quality)
    return db_quality

@router.get("/fqc/quality/latest/", response_model=assembly_schemas.AssemblyFQCQualityRate,
    summary="获取最新FQC质量记录",
    description="获取组装车间最新的FQC质量记录",
    response_description="返回最新FQC质量记录")
def read_latest_fqc_quality_rate(db: Session = Depends(get_db)):
    """获取组装车间最新FQC质量记录"""
    latest_quality = db.query(assembly_models.AssemblyFQCQualityRate).order_by(
        assembly_models.AssemblyFQCQualityRate.record_date.desc()
    ).first()
    if latest_quality is None:
        raise HTTPException(status_code=404, detail="没有找到FQC质量记录")
    return latest_quality

# FQC 7天质量趋势
@router.get("/fqc/quality/trend/", response_model=List[assembly_schemas.AssemblyFQCQualityRate7Days],
    summary="获取FQC质量趋势",
    description="获取组装车间最近7天的FQC质量趋势",
    response_description="返回7天FQC质量趋势数据")
def read_fqc_quality_trend(db: Session = Depends(get_db)):
    """获取组装车间最近7天FQC质量趋势"""
    return db.query(assembly_models.AssemblyFQCQualityRate7Days).order_by(
        assembly_models.AssemblyFQCQualityRate7Days.record_date.desc()
    ).limit(7).all()

# 环境信息相关接口
@router.post("/environment/", response_model=assembly_schemas.AssemblyEnvironmentInfo,
    summary="创建环境信息",
    description="创建新的组装车间环境信息记录",
    response_description="返回创建的环境信息")
def create_environment_info(environment: assembly_schemas.AssemblyEnvironmentInfoCreate, db: Session = Depends(get_db)):
    """
    创建组装车间环境信息
    
    参数说明：
    - **dust_free_workshop_level**: 无尘车间等级
    - **average_humidity**: 平均湿度
    - **average_temperature**: 平均温度
    - **workshop_name**: 车间名称
    - **record_date**: 记录日期
    - **updated_by**: 更新人
    - **updated_at**: 更新时间
    """
    db_environment = assembly_models.AssemblyEnvironmentInfo(**environment.dict())
    db.add(db_environment)
    db.commit()
    db.refresh(db_environment)
    return db_environment

@router.get("/environment/latest", 
    response_model=Response[assembly_schemas.EnvironmentInfo],
    summary="获取最新环境信息",
    description="获取组装车间最新的环境信息记录")
def read_latest_environment_info(db: Session = Depends(get_db)):
    """
    获取最新环境信息记录
    
    返回字段说明：
    - dust_free_workshop_level: 无尘车间等级
    - average_humidity: 平均湿度
    - average_temperature: 平均温度
    - workshop_name: 车间名称
    """
    try:
        sql = """
            SELECT 
                id,
                dust_free_workshop_level,
                average_humidity,
                average_temperature,
                workshop_name,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_environment_info 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_environment_info
            )
        """
        
        result = db.execute(text(sql)).first()
        if not result:
            return ErrorResponse(code=404, message="没有找到环境信息记录", success=False)
            
        return Response(
            code=200,
            message="获取环境信息记录成功",
            data={
                **result._asdict(),
                "average_humidity": float(result.average_humidity),
                "average_temperature": float(result.average_temperature)
            }
        )
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取环境信息记录失败: {str(e)}", success=False)

@router.get("/personnel/latest", 
    response_model=Response[List[assembly_schemas.PersonnelCount]],
    summary="获取最新人员信息",
    description="获取组装车间最新日期的人员信息记录")
def read_latest_personnel_info(db: Session = Depends(get_db)):
    """
    获取组装车间最新人员信息
    
    返回字段说明：
    - count: 人数
    - personnel_type: 人员类型（包括：普工、职员、总人数）
    """
    try:
        sql = """
            select general_worker_count as count, personnel_type 
            from ioc_zzcj_personnel_info 
            where record_date = (
                select max(record_date) 
                from ioc_zzcj_personnel_info
            )
            union ALL
            select staff_count as count, '总人数' as personnel_type 
            from ioc_zzcj_personnel_info 
            where record_date = (
                select max(record_date) 
                from ioc_zzcj_personnel_info
            ) 
            and personnel_type = '职员'
        """
        
        result = db.execute(text(sql))
        personnel_list = [
            {
                "count": row.count,
                "personnel_type": row.personnel_type
            }
            for row in result
        ]
        
        if not personnel_list:
            return ErrorResponse(code=404, message="没有找到人员信息记录", success=False)
            
        return Response(code=200, message="获取人员信息记录成功", data=personnel_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取人员信息记录失败: {str(e)}", success=False)

@router.get("/attendance/latest", 
    response_model=Response[assembly_schemas.AttendanceRate],
    summary="获取最新出勤率",
    description="获取组装车间最新的出勤率记录")
def read_latest_attendance_rate(db: Session = Depends(get_db)):
    """
    获取最新出勤率记录
    
    返回字段说明：
    - clerk_attendance_rate: 职员出勤率
    - worker_attendance_rate: 工人出勤率
    - record_date: 记录日期
    - updated_by: 更新人
    - updated_at: 更新时间
    """
    try:
        sql = """
            SELECT 
                id,
                clerk_attendance_rate,
                worker_attendance_rate,
                record_date,
                updated_by,
                updated_at
            FROM ioc_zzcj_attendance_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_attendance_rate
            )
        """
        
        result = db.execute(text(sql)).first()
        if not result:
            return ErrorResponse(code=404, message="没有找到出勤率记录", success=False)
            
        return Response(
            code=200,
            message="获取出勤率记录成功",
            data={
                "id": result.id,
                "clerk_attendance_rate": float(result.clerk_attendance_rate),
                "worker_attendance_rate": float(result.worker_attendance_rate),
                "record_date": result.record_date,
                "updated_by": result.updated_by,
                "updated_at": result.updated_at
            }
        )
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取出勤率记录失败: {str(e)}", success=False)

@router.get("/equipment-info/latest", 
    response_model=Response[assembly_schemas.EquipmentInfo],
    summary="获取最新设备信息",
    description="获取组装车间最新的设备信息记录")
def read_latest_equipment_info(db: Session = Depends(get_db)):
    """
    获取最新设备信息记录
    
    返回字段说明：
    - total_equipment_count: 设备总数
    - operating_equipment_count: 总开机数
    - equipment_overall_efficiency: 设备综合效率
    - operating_rate: 开机率
    - record_date: 记录日期
    - updated_by: 更新人
    - updated_at: 更新时间
    """
    try:
        sql = """
            SELECT 
                id,
                total_equipment_count,
                operating_equipment_count,
                equipment_overall_efficiency,
                operating_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_equipment_info 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_equipment_info
            )
        """
        
        result = db.execute(text(sql)).first()
        if not result:
            return ErrorResponse(code=404, message="没有找到设备信息记录", success=False)
            
        return Response(
            code=200,
            message="获取设备信息记录成功",
            data={
                **result._asdict(),
                "equipment_overall_efficiency": float(result.equipment_overall_efficiency),
                "operating_rate": float(result.operating_rate)
            }
        )
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取设备信息记录失败: {str(e)}", success=False)

@router.get("/workshop-status/latest", 
    response_model=Response[List[assembly_schemas.WorkshopStatus]],
    summary="获取最新车间设备状态",
    description="获取组装车间最新的设备状态记录，可选择指定车间")
def read_latest_workshop_status(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新车间设备状态记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - total_equipment: 设备总数
      - operating_equipment: 设备开机数
      - operating_rate: 设备开机率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                total_equipment,
                operating_equipment,
                operating_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_workshop_status 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_workshop_status
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        status_list = [
            {
                **row._asdict(),
                "operating_rate": float(row.operating_rate)
            }
            for row in result
        ]
        
        if not status_list:
            return ErrorResponse(code=404, message="没有找到车间设备状态记录", success=False)
            
        return Response(code=200, message="获取车间设备状态记录成功", data=status_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取车间设备状态记录失败: {str(e)}", success=False)

@router.get("/achievement-rate/today", 
    response_model=Response[List[assembly_schemas.DailyAchievementRate]],
    summary="获取当天计划达成率",
    description="获取组装车间当天的计划达成率记录")
def read_today_achievement_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取当天计划达成率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - planned_quantity: 计划数量
      - actual_quantity: 实际完成数量
      - achievement_rate: 达成率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                planned_quantity,
                actual_quantity,
                achievement_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_daily_achievement_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_daily_achievement_rate
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        achievement_list = [
            {
                **row._asdict(),
                "achievement_rate": float(row.achievement_rate)
            }
            for row in result
        ]
        
        if not achievement_list:
            return ErrorResponse(code=404, message="没有找到当天计划达成率记录", success=False)
            
        return Response(code=200, message="获取当天计划达成率记录成功", data=achievement_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取当天计划达成率记录失败: {str(e)}", success=False)

@router.get("/achievement-rate/week", 
    response_model=Response[List[assembly_schemas.PlanCompletionRate7Days]],
    summary="获取近7天计划达成率",
    description="获取组装车间近7天的计划达成率记录")
def read_week_achievement_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取近7天计划达成率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - completion_rate: 达成率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                completion_rate,
                record_date,
                update_date as updated_by,
                updated_by as updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_plan_completion_rate_7days 
            WHERE record_date >= CURRENT_DATE - INTERVAL '7 days'
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        sql += " ORDER BY record_date DESC"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        achievement_list = [
            {
                **row._asdict(),
                "completion_rate": float(row.completion_rate)
            }
            for row in result
        ]
        
        if not achievement_list:
            return ErrorResponse(code=404, message="没有找到近7天计划达成率记录", success=False)
            
        return Response(code=200, message="获取近7天计划达成率记录成功", data=achievement_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取近7天计划达成率记录失败: {str(e)}", success=False)

@router.get("/project-status/latest", 
    response_model=Response[List[assembly_schemas.ProjectStatus]],
    summary="获取最新项目开机分布",
    description="获取组装车间最新的项目开机分布记录，可选择指定车间")
def read_latest_project_status(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新项目开机分布记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - project_name: 项目名称
      - product_name: 产品名称
      - machine_id: 机台号
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                project_name,
                product_name,
                machine_id,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_project_status 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_project_status
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        project_list = [{**row._asdict()} for row in result]
        
        if not project_list:
            return ErrorResponse(code=404, message="没有找到项目开机分布记录", success=False)
            
        return Response(code=200, message="获取项目开机分布记录成功", data=project_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取项目开机分布记录失败: {str(e)}", success=False)

@router.get("/fqc/quality-rate/latest", 
    response_model=Response[List[assembly_schemas.FQCQualityRate]],
    summary="获取最新FQC合格率",
    description="获取组装车间最新的FQC合格率记录，可选择指定车间")
def read_latest_fqc_quality_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新FQC合格率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - quality_rate: 合格率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                quality_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_fqc_quality_rate 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_fqc_quality_rate
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        quality_list = [
            {
                **row._asdict(),
                "quality_rate": float(row.quality_rate)
            }
            for row in result
        ]
        
        if not quality_list:
            return ErrorResponse(code=404, message="没有找到FQC合格率记录", success=False)
            
        return Response(code=200, message="获取FQC合格率记录成功", data=quality_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取FQC合格率记录失败: {str(e)}", success=False)

@router.get("/fqc/quality-rate/week", 
    response_model=Response[List[assembly_schemas.FQCQualityRate]],
    summary="获取近7天FQC合格率",
    description="获取组装车间近7天的FQC合格率记录，可选择指定车间")
def read_week_fqc_quality_rate(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取近7天FQC合格率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - quality_rate: 合格率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                quality_rate,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_fqc_quality_rate_7days 
            WHERE record_date >= CURRENT_DATE - INTERVAL '7 days'
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        sql += " ORDER BY record_date DESC"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        quality_list = [
            {
                **row._asdict(),
                "quality_rate": float(row.quality_rate)
            }
            for row in result
        ]
        
        if not quality_list:
            return ErrorResponse(code=404, message="没有找到近7天FQC合格率记录", success=False)
            
        return Response(code=200, message="获取近7天FQC合格率记录成功", data=quality_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取近7天FQC合格率记录失败: {str(e)}", success=False)

@router.get("/fqc/defect-analysis/latest", 
    response_model=Response[List[assembly_schemas.FQCDefectAnalysis]],
    summary="获取最新FQC不良类型分析",
    description="获取组装车间最新的FQC不良类型分析记录，可选择指定车间")
def read_latest_fqc_defect_analysis(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新FQC不良类型分析记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - defect_type_name: 不良类型名称
      - defect_type_count: 不良类型数量
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                defect_type_name,
                defect_type_count,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_fqc_inspection_defect_analysis 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_fqc_inspection_defect_analysis
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        defect_list = [{**row._asdict()} for row in result]
        
        if not defect_list:
            return ErrorResponse(code=404, message="没有找到FQC不良类型分析记录", success=False)
            
        return Response(code=200, message="获取FQC不良类型分析记录成功", data=defect_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取FQC不良类型分析记录失败: {str(e)}", success=False)

@router.get("/test-defect/classification/latest", 
    response_model=Response[List[assembly_schemas.TestDefectClassification]],
    summary="获取最新测试不良分类",
    description="获取组装车间最新的测试不良分类记录，可选择指定车间")
def read_latest_test_defect_classification(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新测试不良分类记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - classification_name: 分类名称
      - defect_count: 不良数量
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                classification_name,
                defect_count,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_test_defect_classification 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_test_defect_classification
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        classification_list = [{**row._asdict()} for row in result]
        
        if not classification_list:
            return ErrorResponse(code=404, message="没有找到测试不良分类记录", success=False)
            
        return Response(code=200, message="获取测试不良分类记录成功", data=classification_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取测试不良分类记录失败: {str(e)}", success=False)

@router.get("/process-yield/latest", 
    response_model=Response[List[assembly_schemas.ProcessYieldInfo]],
    summary="获取最新工序良率",
    description="获取组装车间最新的工序良率记录，可选择指定车间")
def read_latest_process_yield(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取最新工序良率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - process_yield: 工序良率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                process_yield,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_process_yield_current_info 
            WHERE record_date = (
                SELECT max(record_date) 
                FROM ioc_zzcj_process_yield_current_info
            )
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        yield_list = [
            {
                **row._asdict(),
                "process_yield": float(row.process_yield)
            }
            for row in result
        ]
        
        if not yield_list:
            return ErrorResponse(code=404, message="没有找到工序良率记录", success=False)
            
        return Response(code=200, message="获取工序良率记录成功", data=yield_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取工序良率记录失败: {str(e)}", success=False)

@router.get("/process-yield/week", 
    response_model=Response[List[assembly_schemas.ProcessYieldInfo]],
    summary="获取近7天工序良率",
    description="获取组装车间近7天的工序良率记录，可选择指定车间")
def read_week_process_yield(
    workshop_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取近7天工序良率记录
    
    参数说明：
    - **workshop_name**: 所属车间（可选）
    - 返回字段说明：
      - workshop_name: 所属车间
      - process_yield: 工序良率
    """
    try:
        sql = """
            SELECT 
                id,
                workshop_name,
                process_yield,
                record_date,
                updated_by,
                updated_at,
                reserved_field1,
                reserved_field2,
                reserved_field3
            FROM ioc_zzcj_process_yield_info 
            WHERE record_date >= CURRENT_DATE - INTERVAL '7 days'
        """
        
        if workshop_name:
            sql += " AND workshop_name = :workshop_name"
            
        sql += " ORDER BY record_date DESC"
            
        result = db.execute(text(sql), {"workshop_name": workshop_name} if workshop_name else {})
        yield_list = [
            {
                **row._asdict(),
                "process_yield": float(row.process_yield)
            }
            for row in result
        ]
        
        if not yield_list:
            return ErrorResponse(code=404, message="没有找到近7天工序良率记录", success=False)
            
        return Response(code=200, message="获取近7天工序良率记录成功", data=yield_list)
    except Exception as e:
        return ErrorResponse(code=500, message=f"获取近7天工序良率记录失败: {str(e)}", success=False) 
package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.common.exception.BusinessException;
import com.vfdata.pujiang_vfd.modules.factory.dto.WorkshopPersonnelInfoDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.WorkshopPersonnelInfo;
import com.vfdata.pujiang_vfd.modules.factory.repository.WorkshopPersonnelInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WorkshopPersonnelInfoService {

    private final WorkshopPersonnelInfoRepository workshopPersonnelInfoRepository;

    /**
     * 获取最新的人员信息
     */
    public List<WorkshopPersonnelInfoDTO> getLatestPersonnelInfo(String workshopName) {
        List<WorkshopPersonnelInfo> latestRecords;
        if (workshopName != null && !workshopName.isEmpty()) {
            latestRecords = workshopPersonnelInfoRepository.findLatestRecordsByWorkshopName(workshopName);
        } else {
            latestRecords = workshopPersonnelInfoRepository.findLatestRecords();
        }
        
        if (latestRecords.isEmpty()) {
            throw new BusinessException("未找到人员信息记录");
        }
        
        return latestRecords.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将实体转换为DTO
     */
    private WorkshopPersonnelInfoDTO convertToDTO(WorkshopPersonnelInfo entity) {
        WorkshopPersonnelInfoDTO dto = new WorkshopPersonnelInfoDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setTotal_worker_count(entity.getTotalWorkerCount());
        dto.setTotal_clerical_count(entity.getTotalClericalCount());
        dto.setTotal_worker_count(entity.getTotalWorkerCount());
        dto.setTotal_clerical_count(entity.getTotalClericalCount());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
server:
  port: 18080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: pujiang_vfd
  datasource:
    driver-class-name: org.postgresql.Driver
    # url: *******************************************************************************
    url: **********************************************************************************
    username: u4wamgg
    password: De1E2GPgh8h7Ga8nTG3P91InV24ywxBR
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 600000
      max-lifetime: 1800000
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect
        physical_naming_strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false

# 日志配置
logging:
  level:
    root: INFO
    com.vfdata.pujiang_vfd: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: false
    enable-reload-cache-parameter: false
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: 
    enable-home-custom: false
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: false
    footer-custom-content: 
    enable-dynamic-parameter: false
    enable-debug: true
    enable-open-api: true
    enable-group: true
  cors: false
  production: false
  basic:
    enable: false
    username: test
    password: 123456

# 执行器配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 数据同步配置
data-sync:
  # 是否启用数据同步
  enabled: false
  # 外部数据源基础URL
  base-url: http://*************:8081
  # HTTP连接超时时间（毫秒）
  connect-timeout: 30000
  # HTTP读取超时时间（毫秒）
  read-timeout: 60000
  # 批量处理大小
  batch-size: 1000
  # 是否跳过已存在的记录
  skip-existing: true
  # 同步间隔（分钟）
  sync-interval-minutes: 10
  # 最大重试次数
  max-retries: 3
  # 重试间隔（毫秒）
  retry-interval: 5000
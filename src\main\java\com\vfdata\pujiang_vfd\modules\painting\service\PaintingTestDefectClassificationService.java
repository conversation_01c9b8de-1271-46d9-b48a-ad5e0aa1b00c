package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingTestDefectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingTestDefectClassification;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingTestDefectClassificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaintingTestDefectClassificationService {

    private final PaintingTestDefectClassificationRepository defectClassificationRepository;

    /**
     * 获取品质不良分类数据
     * @param workshopName 车间名称，为空时返回所有车间数据
     */
    public List<PaintingTestDefectClassificationDTO> getDefectClassification(String workshopName) {
        List<PaintingTestDefectClassification> entities;
        
        if (workshopName == null || workshopName.trim().isEmpty()) {
            // 返回所有车间的最新数据
            entities = defectClassificationRepository.findLatestRecords();
        } else {
            // 返回指定车间的最新数据
            entities = defectClassificationRepository.findLatestRecordsByWorkshop(workshopName.trim());
        }
        
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private PaintingTestDefectClassificationDTO convertToDTO(PaintingTestDefectClassification entity) {
        PaintingTestDefectClassificationDTO dto = new PaintingTestDefectClassificationDTO();
        dto.setId(entity.getId());
        dto.setDefect_type_name(entity.getDefectTypeName());
        dto.setDefect_type_count(entity.getDefectTypeCount());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionAttendanceRate;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionAttendanceRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class InjectionAttendanceRateService {

    private final InjectionAttendanceRateRepository injectionAttendanceRateRepository;

    /**
     * 获取注塑车间最新出勤率信息
     */
    public InjectionAttendanceRateDTO getLatestAttendanceRate() {
        return injectionAttendanceRateRepository.findLatest()
                .map(rate -> {
                    InjectionAttendanceRateDTO dto = new InjectionAttendanceRateDTO();
                    dto.setId(rate.getId());
                    dto.setClerk_attendance_rate(rate.getClerkAttendanceRate());
                    dto.setWorker_attendance_rate(rate.getWorkerAttendanceRate());
                    dto.setRecord_date(rate.getRecordDate());
                    dto.setUpdated_by(rate.getUpdatedBy());
                    dto.setUpdated_at(rate.getUpdatedAt());
                    dto.setReserved_field1(rate.getReservedField1());
                    dto.setReserved_field2(rate.getReservedField2());
                    dto.setReserved_field3(rate.getReservedField3());
                    return dto;
                })
                .orElseThrow(() -> new RuntimeException("未找到出勤率记录"));
    }
} 
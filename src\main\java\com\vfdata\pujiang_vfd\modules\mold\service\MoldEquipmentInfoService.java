package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldEquipmentInfoDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldEquipmentInfo;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldEquipmentInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MoldEquipmentInfoService {

    private final MoldEquipmentInfoRepository equipmentInfoRepository;

    /**
     * 获取最新的设备信息
     */
    public MoldEquipmentInfoDTO getLatestEquipmentInfo() {
        MoldEquipmentInfo entity = equipmentInfoRepository.findLatestRecord();
        return entity != null ? convertToDTO(entity) : null;
    }

    /**
     * 实体转DTO
     */
    private MoldEquipmentInfoDTO convertToDTO(MoldEquipmentInfo entity) {
        MoldEquipmentInfoDTO dto = new MoldEquipmentInfoDTO();
        dto.setId(entity.getId());
        dto.setOperating_rate(entity.getOperatingRate());
        dto.setTotal_equipment_count(entity.getTotalEquipmentCount());
        dto.setOperating_equipment_count(entity.getOperatingEquipmentCount());
        dto.setOee_rate(entity.getOeeRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

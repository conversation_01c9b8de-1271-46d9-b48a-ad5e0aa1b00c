package com.vfdata.pujiang_vfd.modules.assembly.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "ioc_zzcj_environment_info")
@Schema(description = "组装车间环境信息")
public class AssemblyEnvironmentInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "主键ID")
    private Long id;

    @Column(name = "dust_free_workshop_level", length = 50)
    @Schema(description = "无尘车间等级")
    private String dustFreeWorkshopLevel;

    @Column(name = "average_humidity", precision = 5, scale = 2)
    @Schema(description = "平均湿度")
    private BigDecimal averageHumidity;

    @Column(name = "average_temperature", precision = 5, scale = 2)
    @Schema(description = "平均温度")
    private BigDecimal averageTemperature;

    @Column(name = "workshop_name", length = 50)
    @Schema(description = "车间名称")
    private String workshopName;

    @Column(name = "record_date")
    @Schema(description = "记录日期")
    private LocalDateTime recordDate;

    @Column(name = "updated_by", length = 50)
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_at")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Column(name = "reserved_field1", length = 255)
    @Schema(description = "预留字段1")
    private String reservedField1;

    @Column(name = "reserved_field2", length = 255)
    @Schema(description = "预留字段2")
    private String reservedField2;

    @Column(name = "reserved_field3", length = 255)
    @Schema(description = "预留字段3")
    private String reservedField3;
} 
package com.vfdata.pujiang_vfd.modules.factory.service;

import com.vfdata.pujiang_vfd.modules.factory.dto.AnnualProjectCategoryDTO;
import com.vfdata.pujiang_vfd.modules.factory.entity.AnnualProjectCategory;
import com.vfdata.pujiang_vfd.modules.factory.repository.AnnualProjectCategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AnnualProjectCategoryService {

    private final AnnualProjectCategoryRepository repository;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YEAR_FORMATTER = DateTimeFormatter.ofPattern("yyyy");

    public List<AnnualProjectCategoryDTO> getAnnualProjectCategories(String projectName, LocalDate startDate, LocalDate endDate) {
        List<AnnualProjectCategory> categories;

        if (projectName != null && !projectName.isEmpty()) {
            if (startDate != null || endDate != null) {
                categories = repository.findByProjectNameAndDateRange(projectName, startDate, endDate);
            } else {
                categories = repository.findByProjectName(projectName);
            }
        } else {
            if (startDate != null || endDate != null) {
                categories = repository.findByDateRange(startDate, endDate);
            } else {
                categories = repository.findAllOrderByRecordDateDesc();
            }
        }

        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private AnnualProjectCategoryDTO convertToDTO(AnnualProjectCategory entity) {
        AnnualProjectCategoryDTO dto = new AnnualProjectCategoryDTO();
        dto.setId(entity.getId());
        dto.setProject_name(entity.getProjectName());
        dto.setProject_count(entity.getProjectCount());
        
        // 处理日期格式
        if (entity.getRecordDate() != null) {
            // 如果日期是1月1日，则只返回年份
            if (entity.getRecordDate().getMonthValue() == 1 && entity.getRecordDate().getDayOfMonth() == 1) {
                dto.setRecord_date(entity.getRecordDate().format(YEAR_FORMATTER));
            } else {
                dto.setRecord_date(entity.getRecordDate().format(DATE_FORMATTER));
            }
        }
        
        dto.setUpdated_by(entity.getUpdatedBy());
        if (entity.getUpdatedAt() != null) {
            dto.setUpdated_at(entity.getUpdatedAt().toString());
        }
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
} 
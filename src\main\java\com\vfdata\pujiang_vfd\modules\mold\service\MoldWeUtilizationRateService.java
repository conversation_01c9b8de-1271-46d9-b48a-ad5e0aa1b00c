package com.vfdata.pujiang_vfd.modules.mold.service;

import com.vfdata.pujiang_vfd.modules.mold.dto.MoldWeUtilizationRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.entity.MoldWeUtilizationRate;
import com.vfdata.pujiang_vfd.modules.mold.repository.MoldWeUtilizationRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MoldWeUtilizationRateService {

    private final MoldWeUtilizationRateRepository weUtilizationRateRepository;

    /**
     * 获取WE稼动率数据
     * @param period 周期类型：month返回6条，day返回7条
     */
    public List<MoldWeUtilizationRateDTO> getWeUtilizationRateByPeriod(String period) {
        int limit = "day".equals(period) ? 7 : 6;
        List<MoldWeUtilizationRate> entities = weUtilizationRateRepository.findLatestRecordsByPeriod(period, limit);
        return entities.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 实体转DTO
     */
    private MoldWeUtilizationRateDTO convertToDTO(MoldWeUtilizationRate entity) {
        MoldWeUtilizationRateDTO dto = new MoldWeUtilizationRateDTO();
        dto.setId(entity.getId());
        dto.setPeriod(entity.getPeriod());
        dto.setOnce_utilization_rate(entity.getOnceUtilizationRate());
        dto.setEnd_utilization_rate(entity.getEndUtilizationRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

package com.vfdata.pujiang_vfd.modules.cnc.service;

import com.vfdata.pujiang_vfd.modules.cnc.dto.PersonnelCountDTO;
import com.vfdata.pujiang_vfd.modules.cnc.entity.CncPersonnelInfo;
import com.vfdata.pujiang_vfd.modules.cnc.repository.CncPersonnelInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class CncPersonnelInfoService {

    private final CncPersonnelInfoRepository personnelInfoRepository;

    /**
     * 获取最新的人员信息
     */
    public List<PersonnelCountDTO> getLatestPersonnelInfo() {
        List<PersonnelCountDTO> result = new ArrayList<>();

        // 获取最新记录日期的所有人员信息
        List<CncPersonnelInfo> latestInfos = personnelInfoRepository.findLatest();

        if (latestInfos.isEmpty()) {
            throw new RuntimeException("未找到人员信息记录");
        }

        // 按照其他模块的逻辑：返回general_worker_count作为count，personnel_type作为类型
        for (CncPersonnelInfo info : latestInfos) {
            PersonnelCountDTO dto = new PersonnelCountDTO();
            dto.setPersonnel_type(info.getPersonnelType());
            dto.setCount(info.getGeneralWorkerCount());
            result.add(dto);
        }

        // 添加总人数（从职员记录的staff_count获取）
        CncPersonnelInfo staffInfo = latestInfos.stream()
                .filter(info -> "职员".equals(info.getPersonnelType()))
                .findFirst()
                .orElse(null);

        if (staffInfo != null && staffInfo.getStaffCount() != null) {
            PersonnelCountDTO totalDto = new PersonnelCountDTO();
            totalDto.setPersonnel_type("总人数");
            totalDto.setCount(staffInfo.getStaffCount());
            result.add(totalDto);
        }

        return result;
    }
}

package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldWorkshopStatusDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldWorkshopStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/workshop-status")
@RequiredArgsConstructor
public class MoldWorkshopStatusController {

    private final MoldWorkshopStatusService workshopStatusService;

    @GetMapping("/latest")
    @Operation(summary = "获取模具车间设备状况", description = "获取每个模具车间最新的设备状况信息")
    public ResponseUtils.Result<List<MoldWorkshopStatusDTO>> getLatestWorkshopStatus() {
        try {
            List<MoldWorkshopStatusDTO> data = workshopStatusService.getLatestWorkshopStatus();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间设备状况信息失败：" + e.getMessage());
        }
    }
}

package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.ShipmentTrendDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.ShipmentTrendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/factory/shipments")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class ShipmentTrendController {

    private final ShipmentTrendService shipmentTrendService;

    @GetMapping("/trend/")
    @Operation(summary = "近12个月出货数量", description = "获取最新的12条发货趋势数据")
    public ResponseUtils.Result<List<ShipmentTrendDTO>> getShipmentTrendInfo() {
        List<ShipmentTrendDTO> trendInfo = shipmentTrendService.getShipmentTrendInfo();
        return ResponseUtils.success(trendInfo);
    }
} 
package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.ProcessYieldDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyProcessYield;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyProcessYieldRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AssemblyProcessYieldService {

    private final AssemblyProcessYieldRepository processYieldRepository;

    public List<ProcessYieldDTO> getLatestProcessYield(String workshopName) {
        List<AssemblyProcessYield> yields;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            yields = processYieldRepository.findLatestByWorkshopName(workshopName);
        } else {
            yields = processYieldRepository.findLatest();
        }
        return yields.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    public List<ProcessYieldDTO> getWeekProcessYield(String workshopName) {
        List<AssemblyProcessYield> yields;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            // 获取指定车间最新7条记录
            yields = processYieldRepository.findTop7ByWorkshopNameOrderByRecordDateDesc(workshopName);
        } else {
            // 获取所有车间最新7条记录
            yields = processYieldRepository.findTop7ByOrderByRecordDateDesc();
        }

        return yields.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    private ProcessYieldDTO convertToDTO(AssemblyProcessYield yield) {
        ProcessYieldDTO dto = new ProcessYieldDTO();
        dto.setId(yield.getId());
        dto.setWorkshop_name(yield.getWorkshopName());
        dto.setProcess_yield(yield.getProcessYield());
        dto.setRecord_date(yield.getRecordDate());
        dto.setUpdated_by(yield.getUpdatedBy());
        dto.setUpdated_at(yield.getUpdatedAt());
        dto.setReserved_field1(yield.getReservedField1());
        dto.setReserved_field2(yield.getReservedField2());
        dto.setReserved_field3(yield.getReservedField3());
        return dto;
    }
} 
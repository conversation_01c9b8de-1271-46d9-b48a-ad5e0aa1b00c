package com.vfdata.pujiang_vfd.modules.painting.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "喷涂车间设备信息DTO")
public class PaintingEquipmentInfoDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "设备开机率")
    private BigDecimal operating_rate;

    @Schema(description = "设备总数")
    private Integer total_equipment;

    @Schema(description = "运行设备数量")
    private Integer operating_equipment;

    @Schema(description = "OEE率")
    private BigDecimal oee_rate;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

DROP TABLE IF EXISTS "public"."ioc_ptcj_attendance_rate";
CREATE TABLE "public"."ioc_ptcj_attendance_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_attendance_rate_id_seq'::regclass),
  "clerk_attendance_rate" numeric(5,2),
  "worker_attendance_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default",
  "clerk_attendance_num" int4,
  "worker_attendance_num" int4
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."id" IS '自增主键，唯一标识每条记录';
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."clerk_attendance_rate" IS '职员出勤率，保留两位小数，范围0-100';
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."worker_attendance_rate" IS '普工出勤率，保留两位小数，范围0-100';
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."record_date" IS '数据统计日期';
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."updated_by" IS '最后更新人';
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "public"."ioc_ptcj_attendance_rate"."reserved_field1" IS '备用字段，用于存储特殊出勤类型';
COMMENT ON TABLE "public"."ioc_ptcj_attendance_rate" IS '喷涂车间单日出勤率统计';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_attendance_rate
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_attendance_rate" ADD CONSTRAINT "ioc_ptcj_attendance_rate_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_ptcj_daily_achievement_rate";
CREATE TABLE "public"."ioc_ptcj_daily_achievement_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_daily_achievement_rate_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "planned_quantity" int4,
  "actual_quantity" int4,
  "achievement_rate" float8,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."planned_quantity" IS '当日计划生产数量';
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."actual_quantity" IS '当日实际完成数量';
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."achievement_rate" IS '计划达成率，如1.0表示100%完成';
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."record_date" IS '记录日期';
COMMENT ON COLUMN "public"."ioc_ptcj_daily_achievement_rate"."reserved_field1" IS '备用字段，用于存储计划类型';
COMMENT ON TABLE "public"."ioc_ptcj_daily_achievement_rate" IS '计划达成率当天表，记录每日生产计划完成情况';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_daily_achievement_rate
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_daily_achievement_rate" ADD CONSTRAINT "ioc_ptcj_daily_achievement_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_ptcj_environment_info";
CREATE TABLE "public"."ioc_ptcj_environment_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_environment_info_id_seq'::regclass),
  "dust_free_workshop_level" varchar(20) COLLATE "pg_catalog"."default",
  "average_humidity" numeric(5,2),
  "average_temperature" numeric(5,2),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_environment_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_environment_info"."dust_free_workshop_level" IS '无尘车间等级';
COMMENT ON COLUMN "public"."ioc_ptcj_environment_info"."average_humidity" IS '车间平均湿度，保留两位小数';
COMMENT ON COLUMN "public"."ioc_ptcj_environment_info"."average_temperature" IS '车间平均温度，保留两位小数';
COMMENT ON COLUMN "public"."ioc_ptcj_environment_info"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_environment_info"."reserved_field1" IS '备用字段，用于存储环境监测点';
COMMENT ON TABLE "public"."ioc_ptcj_environment_info" IS '环境信息表，记录车间温湿度等环境参数';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_environment_info
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_environment_info" ADD CONSTRAINT "ioc_ptcj_environment_info_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_ptcj_equipment_info";
CREATE TABLE "public"."ioc_ptcj_equipment_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_equipment_info_id_seq'::regclass),
  "operating_rate" numeric(5,2),
  "total_equipment_count" int4,
  "operating_equipment_count" int4,
  "oee_rate" numeric(5,2),
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "record_date" date,
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."operating_rate" IS '设备开机率，保留两位小数，范围0-100';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."total_equipment_count" IS '设备/线体总数';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."operating_equipment_count" IS '当前开机/开线的设备数量';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."oee_rate" IS '设备能源效率指标（OEE），用于评估设备能耗效率';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."updated_at" IS '记录更新时间';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_info"."record_date" IS '备用字段，用于存储设备额外指标';
COMMENT ON TABLE "public"."ioc_ptcj_equipment_info" IS '设备信息表，记录车间设备整体运行状态';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_equipment_info
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_equipment_info" ADD CONSTRAINT "ioc_ptcj_equipment_info_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_ptcj_equipment_status";
CREATE TABLE "public"."ioc_ptcj_equipment_status" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_equipment_status_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "status_name" varchar(20) COLLATE "pg_catalog"."default",
  "status_count" int4,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "record_date" date,
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_status"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_status"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_status"."status_name" IS '设备状态（运行/停机/维修/待料等）';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_status"."status_count" IS '该状态下的设备数量';
COMMENT ON COLUMN "public"."ioc_ptcj_equipment_status"."record_date" IS '备用字段，用于存储设备状态优先级';
COMMENT ON TABLE "public"."ioc_ptcj_equipment_status" IS '设备状态分布表，记录设备状态分类统计';

-- ----------------------------
-- Indexes structure for table ioc_ptcj_equipment_status
-- ----------------------------
CREATE UNIQUE INDEX "idx_ptcj_equipment_status_unique" ON "public"."ioc_ptcj_equipment_status" USING btree (
  "workshop_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "status_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE UNIQUE INDEX "idx_unique_workshop_status" ON "public"."ioc_ptcj_equipment_status" USING btree (
  "workshop_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "status_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_equipment_status
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_equipment_status" ADD CONSTRAINT "ioc_ptcj_equipment_status_pkey" PRIMARY KEY ("id");



DROP TABLE IF EXISTS "public"."ioc_ptcj_paint_usage_info";
CREATE TABLE "public"."ioc_ptcj_paint_usage_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_paint_usage_info_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "paint_usage" numeric(8,2),
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "record_date" date,
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_paint_usage_info"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_paint_usage_info"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_paint_usage_info"."paint_usage" IS '油漆使用量，保留两位小数';
COMMENT ON COLUMN "public"."ioc_ptcj_paint_usage_info"."reserved_field1" IS '备用字段，用于存储油漆类型';
COMMENT ON TABLE "public"."ioc_ptcj_paint_usage_info" IS '油漆用量表，记录车间涂料消耗情况';

-- ----------------------------
-- Indexes structure for table ioc_ptcj_paint_usage_info
-- ----------------------------
CREATE UNIQUE INDEX "unique_workshop_record_date_idx" ON "public"."ioc_ptcj_paint_usage_info" USING btree (
  "workshop_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "record_date" "pg_catalog"."date_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_paint_usage_info
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_paint_usage_info" ADD CONSTRAINT "ioc_ptcj_paint_usage_info_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_ptcj_personnel_info";
CREATE TABLE "public"."ioc_ptcj_personnel_info" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_personnel_info_id_seq'::regclass),
  "personnel_type" varchar(20) COLLATE "pg_catalog"."default",
  "staff_count" int4,
  "general_worker_count" int4,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_personnel_info"."id" IS '自增主键，唯一标识每条记录';
COMMENT ON COLUMN "public"."ioc_ptcj_personnel_info"."staff_count" IS '人员总数';
COMMENT ON COLUMN "public"."ioc_ptcj_personnel_info"."general_worker_count" IS '类型下人员数';
COMMENT ON TABLE "public"."ioc_ptcj_personnel_info" IS '车间人员信息表，记录每日车间人员构成';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_personnel_info
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_personnel_info" ADD CONSTRAINT "ioc_ptcj_personnel_info_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_ptcj_plan_completion_rate_7days";
CREATE TABLE "public"."ioc_ptcj_plan_completion_rate_7days" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_plan_completion_rate_7days_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "completion_rate" float8,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_plan_completion_rate_7days"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_plan_completion_rate_7days"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_plan_completion_rate_7days"."completion_rate" IS '近7天计划达成率';
COMMENT ON COLUMN "public"."ioc_ptcj_plan_completion_rate_7days"."record_date" IS '记录日期（周起始日或结束日）';
COMMENT ON COLUMN "public"."ioc_ptcj_plan_completion_rate_7days"."reserved_field1" IS '备用字段，用于存储计算方式（平均/累计）';
COMMENT ON TABLE "public"."ioc_ptcj_plan_completion_rate_7days" IS '计划达成率近7天表，记录周度生产计划完成情况';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_plan_completion_rate_7days
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_plan_completion_rate_7days" ADD CONSTRAINT "ioc_ptcj_plan_completion_rate_7days_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_ptcj_project_status";
CREATE TABLE "public"."ioc_ptcj_project_status" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_project_status_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "project_name" varchar(20) COLLATE "pg_catalog"."default",
  "product_name" varchar(20) COLLATE "pg_catalog"."default",
  "machine_id" varchar(20) COLLATE "pg_catalog"."default",
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."workshop_name" IS '项目所属车间';
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."project_name" IS '项目名称（如XX订单项目）';
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."product_name" IS '产品名称（如手机外壳、汽车部件）';
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."machine_id" IS '设备编号';
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."record_date" IS '记录日期';
COMMENT ON COLUMN "public"."ioc_ptcj_project_status"."reserved_field1" IS '备用字段，用于存储项目优先级';
COMMENT ON TABLE "public"."ioc_ptcj_project_status" IS '项目开机分布表，记录各项目使用设备情况';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_project_status
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_project_status" ADD CONSTRAINT "ioc_ptcj_project_status_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_ptcj_pzjy_quality_rate";
CREATE TABLE "public"."ioc_ptcj_pzjy_quality_rate" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_pzjy_quality_rate_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "quality_rate" numeric(5,2),
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "record_date" date,
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate"."quality_rate" IS '产品检验合格率，保留两位小数';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate"."updated_at" IS '合格率更新时间';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate"."record_date" IS '备用字段，用于存储检验类型';
COMMENT ON TABLE "public"."ioc_ptcj_pzjy_quality_rate" IS '品质检验良率表，记录当前车间产品质量合格率';

-- ----------------------------
-- Indexes structure for table ioc_ptcj_pzjy_quality_rate
-- ----------------------------
CREATE UNIQUE INDEX "idx_unique_workshop_name" ON "public"."ioc_ptcj_pzjy_quality_rate" USING btree (
  "workshop_name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table ioc_ptcj_pzjy_quality_rate
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_pzjy_quality_rate" ADD CONSTRAINT "unique_ioc_ptcj_pzjy_quality_rate" UNIQUE ("workshop_name");
ALTER TABLE "public"."ioc_ptcj_pzjy_quality_rate" ADD CONSTRAINT "unique_workshop_name" UNIQUE ("workshop_name");

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_pzjy_quality_rate
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_pzjy_quality_rate" ADD CONSTRAINT "ioc_ptcj_pzjy_quality_rate_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_ptcj_pzjy_quality_rate_7days";
CREATE TABLE "public"."ioc_ptcj_pzjy_quality_rate_7days" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_pzjy_quality_rate_7days_id_seq'::regclass),
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "quality_rate" numeric(5,2),
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate_7days"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate_7days"."workshop_name" IS '车间名称';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate_7days"."quality_rate" IS '合格率';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate_7days"."record_date" IS '记录日期';
COMMENT ON COLUMN "public"."ioc_ptcj_pzjy_quality_rate_7days"."reserved_field1" IS '备用字段，用于存储质量控制标准';
COMMENT ON TABLE "public"."ioc_ptcj_pzjy_quality_rate_7days" IS '品质检验良率近7天表，记录周度产品质量趋势';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_pzjy_quality_rate_7days
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_pzjy_quality_rate_7days" ADD CONSTRAINT "ioc_ptcj_pzjy_quality_rate_7days_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_ptcj_rto_status";
CREATE TABLE "public"."ioc_ptcj_rto_status" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_rto_status_id_seq'::regclass),
  "record_date" date,
  "status_name" varchar(20) COLLATE "pg_catalog"."default",
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_rto_status"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_rto_status"."record_date" IS '记录日期';
COMMENT ON COLUMN "public"."ioc_ptcj_rto_status"."status_name" IS 'RTO设备状态（运行/待机/故障/维护）';
COMMENT ON COLUMN "public"."ioc_ptcj_rto_status"."reserved_field1" IS '备用字段，用于存储RTO运行模式';
COMMENT ON TABLE "public"."ioc_ptcj_rto_status" IS 'RTO运行状态表，记录废气处理设备运行情况';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_rto_status
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_rto_status" ADD CONSTRAINT "ioc_ptcj_rto_status_pkey" PRIMARY KEY ("id");


DROP TABLE IF EXISTS "public"."ioc_ptcj_test_defect_classification";
CREATE TABLE "public"."ioc_ptcj_test_defect_classification" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_test_defect_classification_id_seq'::regclass),
  "defect_type_name" varchar(50) COLLATE "pg_catalog"."default",
  "defect_type_count" int4,
  "workshop_name" varchar(20) COLLATE "pg_catalog"."default",
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "record_date" date,
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_test_defect_classification"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_test_defect_classification"."defect_type_name" IS '不良类型（如划痕、色差、变形）';
COMMENT ON COLUMN "public"."ioc_ptcj_test_defect_classification"."defect_type_count" IS '该类型不良发生次数';
COMMENT ON COLUMN "public"."ioc_ptcj_test_defect_classification"."workshop_name" IS '发现不良的车间';
COMMENT ON COLUMN "public"."ioc_ptcj_test_defect_classification"."record_date" IS '备用字段，用于存储不良等级';
COMMENT ON TABLE "public"."ioc_ptcj_test_defect_classification" IS '品质不良分类表，记录各类品质问题分布';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_test_defect_classification
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_test_defect_classification" ADD CONSTRAINT "ioc_ptcj_test_defect_classification_pkey" PRIMARY KEY ("id");

DROP TABLE IF EXISTS "public"."ioc_ptcj_workshop_status";
CREATE TABLE "public"."ioc_ptcj_workshop_status" (
  "id" int4 NOT NULL DEFAULT nextval('ioc_ptcj_workshop_status_id_seq'::regclass),
  "workshop_name" varchar(50) COLLATE "pg_catalog"."default",
  "operating_rate" numeric(5,2),
  "total_equipment_count" int4,
  "operating_equipment_count" int4,
  "record_date" date,
  "updated_by" varchar(20) COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6),
  "reserved_field1" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field2" varchar(255) COLLATE "pg_catalog"."default",
  "reserved_field3" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."id" IS '自增主键';
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."workshop_name" IS '车间名称（如喷涂车间A、装配车间B）';
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."operating_rate" IS '车间设备开机率';
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."total_equipment_count" IS '车间设备总数';
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."operating_equipment_count" IS '车间当前运行设备数';
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."record_date" IS '数据统计日期';
COMMENT ON COLUMN "public"."ioc_ptcj_workshop_status"."reserved_field1" IS '备用字段，用于存储车间特殊设备类型';
COMMENT ON TABLE "public"."ioc_ptcj_workshop_status" IS '车间设备状况表，按车间汇总设备运行情况';

-- ----------------------------
-- Primary Key structure for table ioc_ptcj_workshop_status
-- ----------------------------
ALTER TABLE "public"."ioc_ptcj_workshop_status" ADD CONSTRAINT "ioc_ptcj_workshop_status_pkey" PRIMARY KEY ("id");



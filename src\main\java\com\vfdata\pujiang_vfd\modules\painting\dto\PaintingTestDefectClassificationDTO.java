package com.vfdata.pujiang_vfd.modules.painting.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "喷涂车间品质不良分类DTO")
public class PaintingTestDefectClassificationDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "不良类型")
    private String defect_type_name;

    @Schema(description = "该类型不良发生次数")
    private Integer defect_type_count;

    @Schema(description = "发现不良的车间")
    private String workshop_name;

    @Schema(description = "记录日期")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

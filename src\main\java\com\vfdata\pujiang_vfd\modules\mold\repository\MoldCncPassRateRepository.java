package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldCncPassRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldCncPassRateRepository extends JpaRepository<MoldCncPassRate, Long> {

    /**
     * 获取CNC合格率数据，根据period参数返回不同数量
     * @param period 周期类型：month返回6条，day返回7条
     */
    @Query("SELECT m FROM MoldCncPassRate m WHERE m.period = :period ORDER BY m.recordDate DESC LIMIT :limit")
    List<MoldCncPassRate> findLatestRecordsByPeriod(@Param("period") String period, @Param("limit") int limit);
}

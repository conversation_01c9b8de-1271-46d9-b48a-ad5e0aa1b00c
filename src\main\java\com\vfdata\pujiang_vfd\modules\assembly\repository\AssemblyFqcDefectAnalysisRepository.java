package com.vfdata.pujiang_vfd.modules.assembly.repository;

import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyFqcDefectAnalysis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface AssemblyFqcDefectAnalysisRepository extends JpaRepository<AssemblyFqcDefectAnalysis, Long> {
    
    @Query("SELECT d FROM AssemblyFqcDefectAnalysis d WHERE d.recordDate = (SELECT MAX(d2.recordDate) FROM AssemblyFqcDefectAnalysis d2)")
    List<AssemblyFqcDefectAnalysis> findLatest();
    
    @Query("SELECT d FROM AssemblyFqcDefectAnalysis d WHERE d.workshopName = :workshopName AND d.recordDate = (SELECT MAX(d2.recordDate) FROM AssemblyFqcDefectAnalysis d2 WHERE d2.workshopName = :workshopName)")
    List<AssemblyFqcDefectAnalysis> findLatestByWorkshopName(@Param("workshopName") String workshopName);
} 
package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.TongLvUsageDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyTongLvUsageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/tonglv-usage")
@RequiredArgsConstructor
public class NewEnergyTongLvUsageController {

    private final NewEnergyTongLvUsageService tongLvUsageService;

    @GetMapping("/latest")
    @Operation(summary = "获取最新7条铜铝用量记录", description = "获取按日期降序排列的最新7条铜铝用量数据")
    public ResponseUtils.Result<List<TongLvUsageDTO>> getLatest7Records() {
        try {
            List<TongLvUsageDTO> records = tongLvUsageService.getLatest7Records();
            return ResponseUtils.success(records);
        } catch (Exception e) {
            return ResponseUtils.error("获取铜铝用量数据失败：" + e.getMessage());
        }
    }

}

package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.CncQualityRate7DaysDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncQualityRate7DaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/quality-rate-7days")
@RequiredArgsConstructor
public class CncQualityRate7DaysController {

    private final CncQualityRate7DaysService qualityRate7DaysService;

    @GetMapping
    @Operation(summary = "获取CNC车间巡检检验合格率近7天", description = "获取指定CNC车间巡检检验合格率近7天数据")
    public ResponseUtils.Result<List<CncQualityRate7DaysDTO>> getQualityRate7Days(
            @Parameter(description = "车间名称", required = true)
            @RequestParam String workshop_name) {
        try {
            List<CncQualityRate7DaysDTO> dataList = qualityRate7DaysService.getQualityRate7Days(workshop_name);
            return ResponseUtils.success(dataList);
        } catch (IllegalArgumentException e) {
            return ResponseUtils.error("参数错误：" + e.getMessage());
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间质量率7天数据失败：" + e.getMessage());
        }
    }
}

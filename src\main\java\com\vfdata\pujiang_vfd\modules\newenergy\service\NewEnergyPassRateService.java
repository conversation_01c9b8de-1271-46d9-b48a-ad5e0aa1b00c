package com.vfdata.pujiang_vfd.modules.newenergy.service;

import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyPassRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPassRate;
import com.vfdata.pujiang_vfd.modules.newenergy.repository.NewEnergyPassRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NewEnergyPassRateService {

    private final NewEnergyPassRateRepository passRateRepository;

    /**
     * 获取每个车间最新的性能测试良率数据
     */
    public List<NewEnergyPassRateDTO> getLatestPassRate() {
        List<NewEnergyPassRate> records = passRateRepository.findLatestPassRate();
        return records.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 将Entity转换为DTO
     */
    private NewEnergyPassRateDTO convertToDTO(NewEnergyPassRate entity) {
        NewEnergyPassRateDTO dto = new NewEnergyPassRateDTO();
        dto.setId(entity.getId());
        dto.setWorkshop_name(entity.getWorkshopName());
        dto.setQuality_rate(entity.getQualityRate());
        dto.setRecord_date(entity.getRecordDate());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

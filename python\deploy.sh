#!/bin/bash

# 设置安装路径
INSTALL_PATH="/opt/vfdata/vtools/pujiang"
PYTHON_VERSION="3.11.9"
PYTHON_INSTALL_PATH="/usr/local/python3.11"

echo "开始安装Python ${PYTHON_VERSION}..."

# 创建安装目录
mkdir -p $INSTALL_PATH
cd $INSTALL_PATH

# 安装编译Python所需的依赖
yum install -y gcc gcc-c++ make zlib-devel bzip2-devel openssl-devel ncurses-devel sqlite-devel readline-devel tk-devel libffi-devel xz-devel

# 解压并安装Python
tar -xf Python-${PYTHON_VERSION}.tgz
cd Python-${PYTHON_VERSION}
./configure --prefix=$PYTHON_INSTALL_PATH --enable-optimizations
make -j $(nproc)
make install

# 创建软链接
ln -sf $PYTHON_INSTALL_PATH/bin/python3.11 /usr/bin/python3
ln -sf $PYTHON_INSTALL_PATH/bin/pip3.11 /usr/bin/pip3

# 验证Python版本
python3 --version

# 返回项目目录
cd $INSTALL_PATH

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 升级pip
python3 -m pip install --upgrade pip

# 安装依赖包
cd packages
for package in *.whl; do
    if [ -f "$package" ]; then
        echo "正在安装 $package..."
        pip install "$package" --no-index --find-links .
    fi
done
cd ..

# 创建必要的目录
mkdir -p logs

# 处理文件编码
echo "处理Python文件编码..."
python3 add_encoding.py

# 设置权限
chown -R root:root $INSTALL_PATH
chmod -R 755 $INSTALL_PATH

# 启动服务
echo "正在启动服务..."
./service.sh start 
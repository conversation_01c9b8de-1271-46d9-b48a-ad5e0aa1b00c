package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyPassRate7Days;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NewEnergyPassRate7DaysRepository extends JpaRepository<NewEnergyPassRate7Days, Long> {
    
    /**
     * 获取最新7条记录数据
     * 按record_date降序排列，取前7条
     */
    @Query("SELECT n FROM NewEnergyPassRate7Days n ORDER BY n.recordDate DESC LIMIT 7")
    List<NewEnergyPassRate7Days> findTop7OrderByRecordDateDesc();
}

package com.vfdata.pujiang_vfd.modules.cnc.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.cnc.dto.PersonnelCountDTO;
import com.vfdata.pujiang_vfd.modules.cnc.service.CncPersonnelInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "CNC车间接口")
@RestController
@RequestMapping("/cnc/personnel-info")
@RequiredArgsConstructor
public class CncPersonnelInfoController {

    private final CncPersonnelInfoService personnelInfoService;

    @GetMapping("/latest")
    @Operation(summary = "获取CNC车间人员信息", description = "获取CNC车间最新的人员统计信息")
    public ResponseUtils.Result<List<PersonnelCountDTO>> getLatestPersonnelInfo() {
        try {
            List<PersonnelCountDTO> infoList = personnelInfoService.getLatestPersonnelInfo();
            return ResponseUtils.success(infoList);
        } catch (Exception e) {
            return ResponseUtils.error("获取CNC车间人员信息失败：" + e.getMessage());
        }
    }
}

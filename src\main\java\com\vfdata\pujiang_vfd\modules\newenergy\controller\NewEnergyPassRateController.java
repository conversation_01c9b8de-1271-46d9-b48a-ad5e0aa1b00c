package com.vfdata.pujiang_vfd.modules.newenergy.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.newenergy.dto.NewEnergyPassRateDTO;
import com.vfdata.pujiang_vfd.modules.newenergy.service.NewEnergyPassRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "新能源车间接口")
@RestController
@RequestMapping("/newenergy/pass-rate/latest")
@RequiredArgsConstructor
public class NewEnergyPassRateController {

    private final NewEnergyPassRateService passRateService;

    @GetMapping
    @Operation(summary = "获取最新性能测试良率数据", description = "获取每个车间最新的性能测试良率数据")
    public ResponseUtils.Result<List<NewEnergyPassRateDTO>> getLatestPassRate() {
        try {
            List<NewEnergyPassRateDTO> data = passRateService.getLatestPassRate();
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取最新性能测试良率数据失败：" + e.getMessage());
        }
    }
}

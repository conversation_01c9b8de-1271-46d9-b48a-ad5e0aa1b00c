package com.vfdata.pujiang_vfd.modules.newenergy.repository;

import com.vfdata.pujiang_vfd.modules.newenergy.entity.NewEnergyEquipmentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface NewEnergyEquipmentInfoRepository extends JpaRepository<NewEnergyEquipmentInfo, Long> {
    
    /**
     * 获取最新记录日期的设备信息
     * SQL: SELECT * from ioc_xnycj_equipment_info where record_date = (select max(record_date) from ioc_xnycj_equipment_info)
     */
    @Query("SELECT n FROM NewEnergyEquipmentInfo n WHERE n.recordDate = (SELECT MAX(n2.recordDate) FROM NewEnergyEquipmentInfo n2)")
    Optional<NewEnergyEquipmentInfo> findLatest();
}

package com.vfdata.pujiang_vfd.modules.assembly.service;

import com.vfdata.pujiang_vfd.modules.assembly.dto.AssemblyAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.assembly.entity.AssemblyAttendanceRate;
import com.vfdata.pujiang_vfd.modules.assembly.repository.AssemblyAttendanceRateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AssemblyAttendanceRateService {
    
    private final AssemblyAttendanceRateRepository attendanceRateRepository;
    
    /**
     * 获取组装车间最新出勤率信息
     */
    public AssemblyAttendanceRateDTO getLatestAttendanceRate() {
        Optional<AssemblyAttendanceRate> optionalRate = attendanceRateRepository.findLatest();
        if (optionalRate.isPresent()) {
            AssemblyAttendanceRate rate = optionalRate.get();
            AssemblyAttendanceRateDTO dto = new AssemblyAttendanceRateDTO();
            dto.setId(rate.getId());
            dto.setClerk_attendance_rate(rate.getClerkAttendanceRate());
            dto.setWorker_attendance_rate(rate.getWorkerAttendanceRate());
            dto.setRecord_date(rate.getRecordDate());
            dto.setUpdated_by(rate.getUpdatedBy());
            dto.setUpdated_at(rate.getUpdatedAt());
            dto.setReserved_field1(rate.getReservedField1());
            dto.setReserved_field2(rate.getReservedField2());
            dto.setReserved_field3(rate.getReservedField3());
            return dto;
        }
        return null;
    }
} 
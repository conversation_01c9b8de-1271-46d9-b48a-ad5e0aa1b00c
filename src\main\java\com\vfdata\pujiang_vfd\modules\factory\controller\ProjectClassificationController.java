package com.vfdata.pujiang_vfd.modules.factory.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.factory.dto.ProjectClassificationDTO;
import com.vfdata.pujiang_vfd.modules.factory.service.ProjectClassificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/factory/projects")
@RequiredArgsConstructor
@Tag(name = "全厂页面接口")
public class ProjectClassificationController {

    private final ProjectClassificationService projectClassificationService;

    @GetMapping("/classification")
    @Operation(summary = "获取项目分类", description = "根据项目名称获取项目分类信息")
    public ResponseUtils.Result<List<ProjectClassificationDTO>> getProjectClassifications(
            @Parameter(description = "项目名称") @RequestParam(required = false) String project_name) {
        List<ProjectClassificationDTO> classifications = projectClassificationService.getProjectClassifications(project_name);
        return ResponseUtils.success(classifications);
    }
} 
package com.vfdata.pujiang_vfd.modules.dashboard.repository;

import com.vfdata.pujiang_vfd.modules.dashboard.entity.TechnicalEdu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface TechnicalEduRepository extends JpaRepository<TechnicalEdu, Long> {
    
    List<TechnicalEdu> findAllByOrderByRecordDateDesc();
}

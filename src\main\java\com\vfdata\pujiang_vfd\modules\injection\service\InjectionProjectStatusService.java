package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionProjectStatusDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionProjectStatus;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionProjectStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionProjectStatusService {

    private final InjectionProjectStatusRepository injectionProjectStatusRepository;

    public List<InjectionProjectStatusDTO> getProjectStatus(String workshopName) {
        List<InjectionProjectStatus> statusList;
        if (workshopName != null && !workshopName.trim().isEmpty()) {
            statusList = injectionProjectStatusRepository.findByWorkshopName(workshopName);
            if (statusList.isEmpty()) {
                return null;
            }
        } else {
            statusList = injectionProjectStatusRepository.findAllProjectStatus();
        }
        
        return statusList.stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    private InjectionProjectStatusDTO convertToDTO(InjectionProjectStatus status) {
        InjectionProjectStatusDTO dto = new InjectionProjectStatusDTO();
        dto.setId(status.getId());
        dto.setWorkshop_name(status.getWorkshopName());
        dto.setProject_name(status.getProjectName());
        dto.setProduct_name(status.getProductName());
        dto.setMachine_id(status.getMachineId());
        dto.setRecord_date(status.getRecordDate());
        dto.setUpdated_by(status.getUpdatedBy());
        dto.setUpdated_at(status.getUpdatedAt());
        dto.setReserved_field1(status.getReservedField1());
        dto.setReserved_field2(status.getReservedField2());
        dto.setReserved_field3(status.getReservedField3());
        return dto;
    }
} 
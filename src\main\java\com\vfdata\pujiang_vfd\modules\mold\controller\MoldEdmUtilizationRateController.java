package com.vfdata.pujiang_vfd.modules.mold.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.mold.dto.MoldEdmUtilizationRateDTO;
import com.vfdata.pujiang_vfd.modules.mold.service.MoldEdmUtilizationRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "模具车间接口")
@RestController
@RequestMapping("/mold/edm-utilization-rate")
@RequiredArgsConstructor
public class MoldEdmUtilizationRateController {

    private final MoldEdmUtilizationRateService edmUtilizationRateService;

    @GetMapping("/6months")
    @Operation(summary = "获取模具车间EDM稼动率", description = "获取模具车间EDM稼动率数据，period=month返回6条，period=day返回7条")
    public ResponseUtils.Result<List<MoldEdmUtilizationRateDTO>> getEdmUtilizationRateByPeriod(@RequestParam String period) {
        try {
            List<MoldEdmUtilizationRateDTO> data = edmUtilizationRateService.getEdmUtilizationRateByPeriod(period);
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取模具车间EDM稼动率失败：" + e.getMessage());
        }
    }
}

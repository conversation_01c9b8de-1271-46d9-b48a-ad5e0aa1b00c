package com.vfdata.pujiang_vfd.modules.painting.service;

import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingRtoStatusDTO;
import com.vfdata.pujiang_vfd.modules.painting.entity.PaintingRtoStatus;
import com.vfdata.pujiang_vfd.modules.painting.repository.PaintingRtoStatusRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaintingRtoStatusService {

    private final PaintingRtoStatusRepository rtoStatusRepository;

    /**
     * 获取最新的RTO运行状态
     */
    public PaintingRtoStatusDTO getLatestRtoStatus() {
        PaintingRtoStatus rtoStatus = rtoStatusRepository.findLatestRecord();
        if (rtoStatus == null) {
            return null;
        }
        return convertToDTO(rtoStatus);
    }

    /**
     * 实体转DTO
     */
    private PaintingRtoStatusDTO convertToDTO(PaintingRtoStatus entity) {
        PaintingRtoStatusDTO dto = new PaintingRtoStatusDTO();
        dto.setId(entity.getId());
        dto.setRecord_date(entity.getRecordDate());
        dto.setStatus_name(entity.getStatusName());
        dto.setUpdated_by(entity.getUpdatedBy());
        dto.setUpdated_at(entity.getUpdatedAt());
        dto.setReserved_field1(entity.getReservedField1());
        dto.setReserved_field2(entity.getReservedField2());
        dto.setReserved_field3(entity.getReservedField3());
        return dto;
    }
}

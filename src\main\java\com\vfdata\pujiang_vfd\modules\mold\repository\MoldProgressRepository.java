package com.vfdata.pujiang_vfd.modules.mold.repository;

import com.vfdata.pujiang_vfd.modules.mold.entity.MoldProgress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoldProgressRepository extends JpaRepository<MoldProgress, Long> {

    /**
     * 获取最新记录日期的模具进度数据
     */
    @Query("SELECT m FROM MoldProgress m WHERE m.recordDate = (SELECT MAX(m2.recordDate) FROM MoldProgress m2)")
    List<MoldProgress> findLatestRecords();
}

package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionDefectAnalysisDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionDefectAnalysis;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionDefectAnalysisRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionDefectAnalysisService {
    
    private final InjectionDefectAnalysisRepository defectAnalysisRepository;
    
    public List<InjectionDefectAnalysisDTO> getLatestDefectAnalyses(String workshopName) {
        List<InjectionDefectAnalysis> defectAnalyses;
        if (workshopName == null || workshopName.trim().isEmpty()) {
            defectAnalyses = defectAnalysisRepository.findLatestDefectAnalyses();
        } else {
            defectAnalyses = defectAnalysisRepository.findLatestDefectAnalysesByWorkshopName(workshopName);
        }

        if (defectAnalyses.isEmpty()) {
            throw new RuntimeException("没有找到不良类型分析记录");
        }

        return defectAnalyses.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    private InjectionDefectAnalysisDTO convertToDTO(InjectionDefectAnalysis analysis) {
        InjectionDefectAnalysisDTO dto = new InjectionDefectAnalysisDTO();
        dto.setId(analysis.getId());
        dto.setWorkshop_name(analysis.getWorkshopName());
        dto.setDefect_type_name(analysis.getDefectTypeName());
        dto.setType_count(analysis.getTypeCount());
        dto.setRecord_date(analysis.getRecordDate());
        dto.setUpdated_by(analysis.getUpdatedBy());
        dto.setUpdated_at(analysis.getUpdatedAt());
        dto.setReserved_field1(analysis.getReservedField1());
        dto.setReserved_field2(analysis.getReservedField2());
        dto.setReserved_field3(analysis.getReservedField3());
        return dto;
    }
} 
package com.vfdata.pujiang_vfd.modules.assembly.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Schema(description = "设备测试工序良率信息DTO")
public class AssemblyProcessYieldInfoDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "工序良率")
    private BigDecimal process_yield;

    @Schema(description = "所属车间")
    private String workshop_name;

    @Schema(description = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate record_date;

    @Schema(description = "更新人")
    private String updated_by;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updated_at;

    @Schema(description = "备用字段1")
    private String reserved_field1;

    @Schema(description = "备用字段2")
    private String reserved_field2;

    @Schema(description = "备用字段3")
    private String reserved_field3;
}

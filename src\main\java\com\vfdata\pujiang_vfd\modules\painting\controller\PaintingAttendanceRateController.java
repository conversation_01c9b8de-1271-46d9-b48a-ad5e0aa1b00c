package com.vfdata.pujiang_vfd.modules.painting.controller;

import com.vfdata.pujiang_vfd.common.util.ResponseUtils;
import com.vfdata.pujiang_vfd.modules.painting.dto.PaintingAttendanceRateDTO;
import com.vfdata.pujiang_vfd.modules.painting.service.PaintingAttendanceRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "喷涂车间接口")
@RestController
@RequestMapping("/painting/attendance-rate")
@RequiredArgsConstructor
public class PaintingAttendanceRateController {

    private final PaintingAttendanceRateService attendanceRateService;

    @GetMapping("/latest")
    @Operation(summary = "获取喷涂车间出勤率", description = "获取喷涂车间最新的职员和普工出勤率信息")
    public ResponseUtils.Result<PaintingAttendanceRateDTO> getLatestAttendanceRate() {
        try {
            PaintingAttendanceRateDTO data = attendanceRateService.getLatestAttendanceRate();
            if (data == null) {
                return ResponseUtils.success(new PaintingAttendanceRateDTO());
            }
            return ResponseUtils.success(data);
        } catch (Exception e) {
            return ResponseUtils.error("获取喷涂车间出勤率失败：" + e.getMessage());
        }
    }
}

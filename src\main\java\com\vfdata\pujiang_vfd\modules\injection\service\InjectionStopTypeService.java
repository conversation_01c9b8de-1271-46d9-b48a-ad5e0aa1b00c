package com.vfdata.pujiang_vfd.modules.injection.service;

import com.vfdata.pujiang_vfd.modules.injection.dto.InjectionStopTypeDTO;
import com.vfdata.pujiang_vfd.modules.injection.entity.InjectionStopType;
import com.vfdata.pujiang_vfd.modules.injection.repository.InjectionStopTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class InjectionStopTypeService {
    
    private final InjectionStopTypeRepository stopTypeRepository;
    
    public List<InjectionStopTypeDTO> getStopTypes(String workshopName) {
        List<InjectionStopType> stopTypes;
        if (workshopName == null || workshopName.trim().isEmpty()) {
            stopTypes = stopTypeRepository.findAllStopTypes();
        } else {
            stopTypes = stopTypeRepository.findByWorkshopName(workshopName);
        }
        
        return stopTypes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
    
    private InjectionStopTypeDTO convertToDTO(InjectionStopType stopType) {
        InjectionStopTypeDTO dto = new InjectionStopTypeDTO();
        dto.setId(stopType.getId());
        dto.setWorkshop_name(stopType.getWorkshopName());
        dto.setStop_type_name(stopType.getStopTypeName());
        dto.setType_count(stopType.getTypeCount());
        dto.setRecord_date(stopType.getRecordDate());
        dto.setUpdated_by(stopType.getUpdatedBy());
        dto.setUpdated_at(stopType.getUpdatedAt());
        dto.setReserved_field1(stopType.getReservedField1());
        dto.setReserved_field2(stopType.getReservedField2());
        dto.setReserved_field3(stopType.getReservedField3());
        return dto;
    }
} 